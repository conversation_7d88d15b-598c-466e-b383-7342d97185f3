/// <reference types="node" />
import type { Status } from "./model.js";
import { type ContentType } from "./model.js";
import { type AttachmentOptions, type Label, type Link, type ParameterMode, type ParameterOptions } from "./model.js";
import { LabelName, LinkType } from "./model.js";
export declare const label: (name: LabelName | string, value: string) => PromiseLike<void>;
export declare const labels: (...labelsList: Label[]) => PromiseLike<void>;
export declare const link: (url: string, name?: string, type?: LinkType | string) => PromiseLike<void>;
export declare const links: (...linksList: Link[]) => PromiseLike<void>;
export declare const parameter: (name: string, value: string, options?: ParameterOptions) => PromiseLike<void>;
export declare const description: (markdown: string) => PromiseLike<void>;
export declare const descriptionHtml: (html: string) => PromiseLike<void>;
export declare const displayName: (name: string) => PromiseLike<void>;
export declare const historyId: (value: string) => PromiseLike<void>;
export declare const testCaseId: (value: string) => PromiseLike<void>;
export declare const attachment: (name: string, content: Buffer | string, options: ContentType | string | AttachmentOptions) => PromiseLike<void>;
export declare const attachTrace: (name: string, path: string) => PromiseLike<void>;
export declare const attachmentPath: (name: string, path: string, options: ContentType | string | Omit<AttachmentOptions, "encoding">) => PromiseLike<void>;
export type StepContext = {
    displayName: (name: string) => void | PromiseLike<void>;
    parameter: (name: string, value: string, mode?: ParameterMode) => void | PromiseLike<void>;
};
export declare const logStep: (name: string, status?: Status, error?: Error) => PromiseLike<void>;
export declare const step: <T = void>(name: string, body: (context: StepContext) => T | PromiseLike<T>) => PromiseLike<T>;
export declare const issue: (url: string, name?: string) => PromiseLike<void>;
export declare const tms: (url: string, name?: string) => PromiseLike<void>;
export declare const allureId: (value: string) => PromiseLike<void>;
export declare const epic: (name: string) => PromiseLike<void>;
export declare const feature: (name: string) => PromiseLike<void>;
export declare const story: (name: string) => PromiseLike<void>;
export declare const suite: (name: string) => PromiseLike<void>;
export declare const parentSuite: (name: string) => PromiseLike<void>;
export declare const subSuite: (name: string) => PromiseLike<void>;
export declare const owner: (name: string) => PromiseLike<void>;
export declare const severity: (name: string) => PromiseLike<void>;
export declare const layer: (name: string) => PromiseLike<void>;
export declare const tag: (name: string) => PromiseLike<void>;
export declare const tags: (...tagsList: string[]) => PromiseLike<void>;
