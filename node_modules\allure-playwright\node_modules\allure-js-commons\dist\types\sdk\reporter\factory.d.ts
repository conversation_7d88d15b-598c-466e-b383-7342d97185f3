import type { FixtureR<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TestResultContainer } from "../../model.js";
export declare const createTestResultContainer: (uuid: string) => TestResultContainer;
export declare const createFixtureResult: () => FixtureResult;
export declare const createStepResult: () => StepResult;
export declare const createTestResult: (uuid: string, historyUuid?: string) => TestResult;
