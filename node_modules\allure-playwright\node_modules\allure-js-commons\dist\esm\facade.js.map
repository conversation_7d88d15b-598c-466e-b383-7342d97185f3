{"version": 3, "file": "facade.js", "names": ["LabelName", "LinkType", "getGlobalTestRuntimeWithAutoconfig", "isPromise", "callRuntimeMethod", "method", "_len", "arguments", "length", "args", "Array", "_key", "runtime", "then", "testRuntime", "label", "name", "value", "labels", "_len2", "labelsList", "_key2", "link", "url", "type", "links", "_len3", "linksList", "_key3", "parameter", "options", "description", "markdown", "descriptionHtml", "html", "displayName", "historyId", "testCaseId", "attachment", "content", "opts", "contentType", "attachTrace", "path", "attachmentPath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mode", "logStep", "status", "error", "step", "body", "issue", "ISSUE", "tms", "TMS", "allureId", "ALLURE_ID", "epic", "EPIC", "feature", "FEATURE", "story", "STORY", "suite", "SUITE", "parentSuite", "PARENT_SUITE", "subSuite", "SUB_SUITE", "owner", "OWNER", "severity", "SEVERITY", "layer", "LAYER", "tag", "TAG", "tags", "_len4", "tagsList", "_key4", "map"], "sources": ["../../src/facade.ts"], "sourcesContent": ["import type { Status } from \"./model.js\";\nimport { type ContentType } from \"./model.js\";\nimport { type AttachmentOptions, type Label, type Link, type ParameterMode, type ParameterOptions } from \"./model.js\";\nimport { LabelName, LinkType } from \"./model.js\";\nimport { getGlobalTestRuntimeWithAutoconfig } from \"./sdk/runtime/runtime.js\";\nimport type { TestRuntime } from \"./sdk/runtime/types.js\";\nimport { isPromise } from \"./sdk/utils.js\";\n\nconst callRuntimeMethod = <\n  T extends keyof TestRuntime,\n  S extends Parameters<TestRuntime[T]>,\n  R extends ReturnType<TestRuntime[T]>,\n>(\n  method: T,\n  ...args: S\n): R => {\n  const runtime = getGlobalTestRuntimeWithAutoconfig();\n\n  if (!isPromise(runtime)) {\n    // @ts-ignore\n    return runtime[method](...args);\n  }\n\n  return runtime.then((testRuntime) => {\n    // @ts-ignore\n    return testRuntime[method](...args);\n  }) as R;\n};\n\nexport const label = (name: LabelName | string, value: string) => {\n  return callRuntimeMethod(\"labels\", { name, value });\n};\n\nexport const labels = (...labelsList: Label[]) => {\n  return callRuntimeMethod(\"labels\", ...labelsList);\n};\n\nexport const link = (url: string, name?: string, type?: LinkType | string) => {\n  return callRuntimeMethod(\"links\", { url, type, name });\n};\n\nexport const links = (...linksList: Link[]) => {\n  return callRuntimeMethod(\"links\", ...linksList);\n};\n\nexport const parameter = (name: string, value: string, options?: ParameterOptions) => {\n  return callRuntimeMethod(\"parameter\", name, value, options);\n};\n\nexport const description = (markdown: string) => {\n  return callRuntimeMethod(\"description\", markdown);\n};\n\nexport const descriptionHtml = (html: string) => {\n  return callRuntimeMethod(\"descriptionHtml\", html);\n};\n\nexport const displayName = (name: string) => {\n  return callRuntimeMethod(\"displayName\", name);\n};\n\nexport const historyId = (value: string) => {\n  return callRuntimeMethod(\"historyId\", value);\n};\n\nexport const testCaseId = (value: string) => {\n  return callRuntimeMethod(\"testCaseId\", value);\n};\n\nexport const attachment = (\n  name: string,\n  content: Buffer | string,\n  options: ContentType | string | AttachmentOptions,\n) => {\n  const opts = typeof options === \"string\" ? { contentType: options } : options;\n  return callRuntimeMethod(\"attachment\", name, content, opts);\n};\n\nexport const attachTrace = (name: string, path: string) => {\n  return callRuntimeMethod(\"attachmentFromPath\", name, path, {\n    contentType: \"application/vnd.allure.playwright-trace\",\n  });\n};\n\nexport const attachmentPath = (\n  name: string,\n  path: string,\n  options: ContentType | string | Omit<AttachmentOptions, \"encoding\">,\n) => {\n  const opts = typeof options === \"string\" ? { contentType: options } : options;\n  return callRuntimeMethod(\"attachmentFromPath\", name, path, opts);\n};\n\nexport type StepContext = {\n  displayName: (name: string) => void | PromiseLike<void>;\n  parameter: (name: string, value: string, mode?: ParameterMode) => void | PromiseLike<void>;\n};\n\nconst stepContext: () => StepContext = () => ({\n  displayName: (name: string) => {\n    return callRuntimeMethod(\"stepDisplayName\", name);\n  },\n  parameter: (name, value, mode?) => {\n    return callRuntimeMethod(\"stepParameter\", name, value, mode);\n  },\n});\n\nexport const logStep = (name: string, status?: Status, error?: Error): PromiseLike<void> => {\n  return callRuntimeMethod(\"logStep\", name, status, error);\n};\n\nexport const step = <T = void>(name: string, body: (context: StepContext) => T | PromiseLike<T>): PromiseLike<T> => {\n  return callRuntimeMethod(\"step\", name, () => body(stepContext()));\n};\n\nexport const issue = (url: string, name?: string) => link(url, name, LinkType.ISSUE);\n\nexport const tms = (url: string, name?: string) => link(url, name, LinkType.TMS);\n\nexport const allureId = (value: string) => label(LabelName.ALLURE_ID, value);\n\nexport const epic = (name: string) => label(LabelName.EPIC, name);\n\nexport const feature = (name: string) => label(LabelName.FEATURE, name);\n\nexport const story = (name: string) => label(LabelName.STORY, name);\n\nexport const suite = (name: string) => label(LabelName.SUITE, name);\n\nexport const parentSuite = (name: string) => label(LabelName.PARENT_SUITE, name);\n\nexport const subSuite = (name: string) => label(LabelName.SUB_SUITE, name);\n\nexport const owner = (name: string) => label(LabelName.OWNER, name);\n\nexport const severity = (name: string) => label(LabelName.SEVERITY, name);\n\nexport const layer = (name: string) => label(LabelName.LAYER, name);\n\nexport const tag = (name: string) => label(LabelName.TAG, name);\n\nexport const tags = (...tagsList: string[]) => {\n  return callRuntimeMethod(\"labels\", ...tagsList.map((value) => ({ name: LabelName.TAG, value })));\n};\n"], "mappings": "AAGA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,YAAY;AAChD,SAASC,kCAAkC,QAAQ,0BAA0B;AAE7E,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAKrBC,MAAS,EAEH;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EADHC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAEP,IAAMC,OAAO,GAAGV,kCAAkC,CAAC,CAAC;EAEpD,IAAI,CAACC,SAAS,CAACS,OAAO,CAAC,EAAE;IACvB;IACA,OAAOA,OAAO,CAACP,MAAM,CAAC,CAAC,GAAGI,IAAI,CAAC;EACjC;EAEA,OAAOG,OAAO,CAACC,IAAI,CAAEC,WAAW,IAAK;IACnC;IACA,OAAOA,WAAW,CAACT,MAAM,CAAC,CAAC,GAAGI,IAAI,CAAC;EACrC,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,IAAMM,KAAK,GAAGA,CAACC,IAAwB,EAAEC,KAAa,KAAK;EAChE,OAAOb,iBAAiB,CAAC,QAAQ,EAAE;IAAEY,IAAI;IAAEC;EAAM,CAAC,CAAC;AACrD,CAAC;AAED,OAAO,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAA,EAA+B;EAAA,SAAAC,KAAA,GAAAZ,SAAA,CAAAC,MAAA,EAAxBY,UAAU,OAAAV,KAAA,CAAAS,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAAVD,UAAU,CAAAC,KAAA,IAAAd,SAAA,CAAAc,KAAA;EAAA;EAClC,OAAOjB,iBAAiB,CAAC,QAAQ,EAAE,GAAGgB,UAAU,CAAC;AACnD,CAAC;AAED,OAAO,IAAME,IAAI,GAAGA,CAACC,GAAW,EAAEP,IAAa,EAAEQ,IAAwB,KAAK;EAC5E,OAAOpB,iBAAiB,CAAC,OAAO,EAAE;IAAEmB,GAAG;IAAEC,IAAI;IAAER;EAAK,CAAC,CAAC;AACxD,CAAC;AAED,OAAO,IAAMS,KAAK,GAAG,SAARA,KAAKA,CAAA,EAA6B;EAAA,SAAAC,KAAA,GAAAnB,SAAA,CAAAC,MAAA,EAAtBmB,SAAS,OAAAjB,KAAA,CAAAgB,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAATD,SAAS,CAAAC,KAAA,IAAArB,SAAA,CAAAqB,KAAA;EAAA;EAChC,OAAOxB,iBAAiB,CAAC,OAAO,EAAE,GAAGuB,SAAS,CAAC;AACjD,CAAC;AAED,OAAO,IAAME,SAAS,GAAGA,CAACb,IAAY,EAAEC,KAAa,EAAEa,OAA0B,KAAK;EACpF,OAAO1B,iBAAiB,CAAC,WAAW,EAAEY,IAAI,EAAEC,KAAK,EAAEa,OAAO,CAAC;AAC7D,CAAC;AAED,OAAO,IAAMC,WAAW,GAAIC,QAAgB,IAAK;EAC/C,OAAO5B,iBAAiB,CAAC,aAAa,EAAE4B,QAAQ,CAAC;AACnD,CAAC;AAED,OAAO,IAAMC,eAAe,GAAIC,IAAY,IAAK;EAC/C,OAAO9B,iBAAiB,CAAC,iBAAiB,EAAE8B,IAAI,CAAC;AACnD,CAAC;AAED,OAAO,IAAMC,WAAW,GAAInB,IAAY,IAAK;EAC3C,OAAOZ,iBAAiB,CAAC,aAAa,EAAEY,IAAI,CAAC;AAC/C,CAAC;AAED,OAAO,IAAMoB,SAAS,GAAInB,KAAa,IAAK;EAC1C,OAAOb,iBAAiB,CAAC,WAAW,EAAEa,KAAK,CAAC;AAC9C,CAAC;AAED,OAAO,IAAMoB,UAAU,GAAIpB,KAAa,IAAK;EAC3C,OAAOb,iBAAiB,CAAC,YAAY,EAAEa,KAAK,CAAC;AAC/C,CAAC;AAED,OAAO,IAAMqB,UAAU,GAAGA,CACxBtB,IAAY,EACZuB,OAAwB,EACxBT,OAAiD,KAC9C;EACH,IAAMU,IAAI,GAAG,OAAOV,OAAO,KAAK,QAAQ,GAAG;IAAEW,WAAW,EAAEX;EAAQ,CAAC,GAAGA,OAAO;EAC7E,OAAO1B,iBAAiB,CAAC,YAAY,EAAEY,IAAI,EAAEuB,OAAO,EAAEC,IAAI,CAAC;AAC7D,CAAC;AAED,OAAO,IAAME,WAAW,GAAGA,CAAC1B,IAAY,EAAE2B,IAAY,KAAK;EACzD,OAAOvC,iBAAiB,CAAC,oBAAoB,EAAEY,IAAI,EAAE2B,IAAI,EAAE;IACzDF,WAAW,EAAE;EACf,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,IAAMG,cAAc,GAAGA,CAC5B5B,IAAY,EACZ2B,IAAY,EACZb,OAAmE,KAChE;EACH,IAAMU,IAAI,GAAG,OAAOV,OAAO,KAAK,QAAQ,GAAG;IAAEW,WAAW,EAAEX;EAAQ,CAAC,GAAGA,OAAO;EAC7E,OAAO1B,iBAAiB,CAAC,oBAAoB,EAAEY,IAAI,EAAE2B,IAAI,EAAEH,IAAI,CAAC;AAClE,CAAC;AAOD,IAAMK,WAA8B,GAAGA,CAAA,MAAO;EAC5CV,WAAW,EAAGnB,IAAY,IAAK;IAC7B,OAAOZ,iBAAiB,CAAC,iBAAiB,EAAEY,IAAI,CAAC;EACnD,CAAC;EACDa,SAAS,EAAEA,CAACb,IAAI,EAAEC,KAAK,EAAE6B,IAAK,KAAK;IACjC,OAAO1C,iBAAiB,CAAC,eAAe,EAAEY,IAAI,EAAEC,KAAK,EAAE6B,IAAI,CAAC;EAC9D;AACF,CAAC,CAAC;AAEF,OAAO,IAAMC,OAAO,GAAGA,CAAC/B,IAAY,EAAEgC,MAAe,EAAEC,KAAa,KAAwB;EAC1F,OAAO7C,iBAAiB,CAAC,SAAS,EAAEY,IAAI,EAAEgC,MAAM,EAAEC,KAAK,CAAC;AAC1D,CAAC;AAED,OAAO,IAAMC,IAAI,GAAGA,CAAWlC,IAAY,EAAEmC,IAAkD,KAAqB;EAClH,OAAO/C,iBAAiB,CAAC,MAAM,EAAEY,IAAI,EAAE,MAAMmC,IAAI,CAACN,WAAW,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC;AAED,OAAO,IAAMO,KAAK,GAAGA,CAAC7B,GAAW,EAAEP,IAAa,KAAKM,IAAI,CAACC,GAAG,EAAEP,IAAI,EAAEf,QAAQ,CAACoD,KAAK,CAAC;AAEpF,OAAO,IAAMC,GAAG,GAAGA,CAAC/B,GAAW,EAAEP,IAAa,KAAKM,IAAI,CAACC,GAAG,EAAEP,IAAI,EAAEf,QAAQ,CAACsD,GAAG,CAAC;AAEhF,OAAO,IAAMC,QAAQ,GAAIvC,KAAa,IAAKF,KAAK,CAACf,SAAS,CAACyD,SAAS,EAAExC,KAAK,CAAC;AAE5E,OAAO,IAAMyC,IAAI,GAAI1C,IAAY,IAAKD,KAAK,CAACf,SAAS,CAAC2D,IAAI,EAAE3C,IAAI,CAAC;AAEjE,OAAO,IAAM4C,OAAO,GAAI5C,IAAY,IAAKD,KAAK,CAACf,SAAS,CAAC6D,OAAO,EAAE7C,IAAI,CAAC;AAEvE,OAAO,IAAM8C,KAAK,GAAI9C,IAAY,IAAKD,KAAK,CAACf,SAAS,CAAC+D,KAAK,EAAE/C,IAAI,CAAC;AAEnE,OAAO,IAAMgD,KAAK,GAAIhD,IAAY,IAAKD,KAAK,CAACf,SAAS,CAACiE,KAAK,EAAEjD,IAAI,CAAC;AAEnE,OAAO,IAAMkD,WAAW,GAAIlD,IAAY,IAAKD,KAAK,CAACf,SAAS,CAACmE,YAAY,EAAEnD,IAAI,CAAC;AAEhF,OAAO,IAAMoD,QAAQ,GAAIpD,IAAY,IAAKD,KAAK,CAACf,SAAS,CAACqE,SAAS,EAAErD,IAAI,CAAC;AAE1E,OAAO,IAAMsD,KAAK,GAAItD,IAAY,IAAKD,KAAK,CAACf,SAAS,CAACuE,KAAK,EAAEvD,IAAI,CAAC;AAEnE,OAAO,IAAMwD,QAAQ,GAAIxD,IAAY,IAAKD,KAAK,CAACf,SAAS,CAACyE,QAAQ,EAAEzD,IAAI,CAAC;AAEzE,OAAO,IAAM0D,KAAK,GAAI1D,IAAY,IAAKD,KAAK,CAACf,SAAS,CAAC2E,KAAK,EAAE3D,IAAI,CAAC;AAEnE,OAAO,IAAM4D,GAAG,GAAI5D,IAAY,IAAKD,KAAK,CAACf,SAAS,CAAC6E,GAAG,EAAE7D,IAAI,CAAC;AAE/D,OAAO,IAAM8D,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAA8B;EAAA,SAAAC,KAAA,GAAAxE,SAAA,CAAAC,MAAA,EAAvBwE,QAAQ,OAAAtE,KAAA,CAAAqE,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAARD,QAAQ,CAAAC,KAAA,IAAA1E,SAAA,CAAA0E,KAAA;EAAA;EAC9B,OAAO7E,iBAAiB,CAAC,QAAQ,EAAE,GAAG4E,QAAQ,CAACE,GAAG,CAAEjE,KAAK,KAAM;IAAED,IAAI,EAAEhB,SAAS,CAAC6E,GAAG;IAAE5D;EAAM,CAAC,CAAC,CAAC,CAAC;AAClG,CAAC", "ignoreList": []}