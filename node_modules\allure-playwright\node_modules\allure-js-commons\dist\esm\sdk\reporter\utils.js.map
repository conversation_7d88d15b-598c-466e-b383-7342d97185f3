{"version": 3, "file": "utils.js", "names": ["createHash", "randomUUID", "fs", "readFile", "path", "process", "LabelName", "LinkType", "StatusByPriority", "FileSystemWriter", "MessageWriter", "randomUuid", "md5", "str", "update", "digest", "getTestResultHistoryId", "result", "_result$testCaseId", "historyId", "tcId", "testCaseId", "fullName", "paramsString", "parameters", "filter", "p", "excluded", "sort", "a", "b", "_a$name", "_a$value", "name", "localeCompare", "value", "map", "_p$name", "_p$value", "concat", "join", "paramsHash", "getTestResultTestCaseId", "undefined", "statusToPriority", "status", "indexOf", "getWorstTestStepResult", "steps", "length", "readImageAsBase64", "_ref", "_asyncToGenerator", "filePath", "file", "encoding", "e", "console", "error", "_x", "apply", "arguments", "getProjectRoot", "cachedProjectRoot", "resolveProjectRootByPath", "cwd", "nextDir", "dir", "accessSync", "constants", "F_OK", "_unused", "dirname", "getRelativePath", "filepath", "isAbsolute", "projectRoot", "relative", "getPosixPath", "platform", "replaceAll", "deepClone", "obj", "JSON", "parse", "stringify", "getSuiteLabels", "suites", "parentSuite", "suite", "subSuites", "labels", "push", "PARENT_SUITE", "SUITE", "SUB_SUITE", "suiteLabelNames", "ensureSuiteLabels", "test", "defaultSuites", "_test$labels", "find", "l", "includes", "_test$labels2", "_test$labels3", "Boolean", "reRegExpChar", "reHasRegExpChar", "RegExp", "source", "escapeRegExp", "replace", "isUrl", "potentialUrl", "URL", "applyLinkTemplate", "template", "formatLink", "templates", "link", "url", "originalUrl", "type", "_templates", "formattedLink", "_objectSpread", "urlTemplate", "nameTemplate", "DEFAULT", "formatLinks", "links", "createDefaultWriter", "config", "env", "ALLURE_TEST_MODE", "emitter", "resultsDir"], "sources": ["../../../../src/sdk/reporter/utils.ts"], "sourcesContent": ["import { createHash, randomUUID } from \"node:crypto\";\nimport type { EventEmitter } from \"node:events\";\nimport fs from \"node:fs\";\nimport { readFile } from \"node:fs/promises\";\nimport path from \"node:path\";\nimport process from \"node:process\";\nimport type { Label, Link, Status, StepResult, TestResult } from \"../../model.js\";\nimport { LabelName, LinkType, StatusByPriority } from \"../../model.js\";\nimport type { LinkConfig, LinkTemplate } from \"./types.js\";\nimport { FileSystemWriter } from \"./writer/FileSystemWriter.js\";\nimport { MessageWriter } from \"./writer/MessageWriter.js\";\n\nexport const randomUuid = () => {\n  return randomUUID();\n};\n\nexport const md5 = (str: string) => {\n  return createHash(\"md5\").update(str).digest(\"hex\");\n};\n\nexport const getTestResultHistoryId = (result: TestResult) => {\n  if (result.historyId) {\n    return result.historyId;\n  }\n\n  const tcId = result.testCaseId ?? (result.fullName ? md5(result.fullName) : null);\n\n  if (!tcId) {\n    return \"\";\n  }\n\n  const paramsString = result.parameters\n    .filter((p) => !p?.excluded)\n    .sort((a, b) => a.name?.localeCompare(b?.name) || a.value?.localeCompare(b?.value))\n    .map((p) => `${p.name ?? \"null\"}:${p.value ?? \"null\"}`)\n    .join(\",\");\n  const paramsHash = md5(paramsString);\n\n  return `${tcId}:${paramsHash}`;\n};\n\nexport const getTestResultTestCaseId = (result: TestResult) => {\n  return result.fullName ? md5(result.fullName) : undefined;\n};\n\nconst statusToPriority = (status: Status | undefined) => {\n  if (!status) {\n    return -1;\n  }\n  return StatusByPriority.indexOf(status);\n};\n\nexport const getWorstTestStepResult = (steps: StepResult[]): StepResult | undefined => {\n  if (steps.length === 0) {\n    return;\n  }\n\n  return [...steps].sort((a, b) => statusToPriority(a.status) - statusToPriority(b.status))[0];\n};\n\nexport const readImageAsBase64 = async (filePath: string): Promise<string | undefined> => {\n  try {\n    const file = await readFile(filePath, { encoding: \"base64\" });\n\n    return file ? `data:image/png;base64,${file}` : undefined;\n  } catch (e) {\n    // eslint-disable-next-line no-console\n    console.error(`could not read file ${filePath}`, e);\n    return undefined;\n  }\n};\n\nexport const getProjectRoot = (() => {\n  let cachedProjectRoot: string | null = null;\n\n  const resolveProjectRootByPath = () => {\n    const cwd = process.cwd();\n    let nextDir = cwd;\n    let dir;\n\n    do {\n      dir = nextDir;\n      try {\n        fs.accessSync(path.join(dir, \"package.json\"), fs.constants.F_OK);\n\n        // package.json exists; use the directory as the project root\n        return dir;\n      } catch {}\n\n      nextDir = path.dirname(dir);\n    } while (nextDir.length < dir.length);\n\n    // package.json doesn't exist in any parent; fall back to CWD\n    return cwd;\n  };\n\n  return () => {\n    if (!cachedProjectRoot) {\n      cachedProjectRoot = resolveProjectRootByPath();\n    }\n    return cachedProjectRoot;\n  };\n})();\n\nexport const getRelativePath = (filepath: string) => {\n  if (path.isAbsolute(filepath)) {\n    const projectRoot = getProjectRoot();\n    filepath = path.relative(projectRoot, filepath);\n  }\n  return filepath;\n};\n\nexport const getPosixPath = (filepath: string) => {\n  if (process.platform === \"win32\") {\n    return filepath.replaceAll(\"\\\\\", \"/\");\n  }\n  return filepath;\n};\n\nexport const deepClone = <T>(obj: T): T => JSON.parse(JSON.stringify(obj));\n\nexport const getSuiteLabels = (suites: readonly string[]): Label[] => {\n  if (suites.length === 0) {\n    return [];\n  }\n\n  const [parentSuite, suite, ...subSuites] = suites;\n  const labels: Label[] = [];\n\n  if (parentSuite) {\n    labels.push({\n      name: LabelName.PARENT_SUITE,\n      value: parentSuite,\n    });\n  }\n\n  if (suite) {\n    labels.push({\n      name: LabelName.SUITE,\n      value: suite,\n    });\n  }\n\n  if (subSuites.length > 0) {\n    labels.push({\n      name: LabelName.SUB_SUITE,\n      value: subSuites.join(\" > \"),\n    });\n  }\n\n  return labels;\n};\n\nconst suiteLabelNames: readonly string[] = [LabelName.PARENT_SUITE, LabelName.SUITE, LabelName.SUB_SUITE];\n\n/**\n * Resolves suite labels for the given test results and add default lables if there is no any suite label.\n * @example\n * ```ts\n * ensureSuiteLabels({ labels: [{ name: \"suite\", value: \"foo\" }] }, [\"bar\"]) // => [{ name: \"suite\", value: \"foo\" }]\n * ensureSuiteLabels({ labels: [] }, [\"bar\"]) // => [{ name: \"parentSuite\", value: \"bar\" }]\n * ```\n * @param test - Test result to resolve suite labels for\n * @param defaultSuites - Default suites to add if there is no any suite label\n * @returns Actual suite labels\n */\nexport const ensureSuiteLabels = (test: Partial<TestResult>, defaultSuites: readonly string[]) => {\n  if (!test.labels?.find((l) => suiteLabelNames.includes(l.name))) {\n    test.labels = [...(test.labels ?? []), ...getSuiteLabels(defaultSuites)];\n  }\n\n  return suiteLabelNames.map((name) => test.labels?.find((l) => l.name === name)).filter(Boolean) as Label[];\n};\n\nconst reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g,\n  reHasRegExpChar = RegExp(reRegExpChar.source);\n\nexport const escapeRegExp = (value: string): string => {\n  return reHasRegExpChar.test(value) ? value.replace(reRegExpChar, \"\\\\$&\") : value;\n};\n\n// TODO: may also use URL.canParse instead (requires node.js v18.17, v19.9, or higher)\nconst isUrl = (potentialUrl: string) => {\n  // Short-circuits the check for many short URL cases, bypassing the try-catch logic.\n  if (potentialUrl.indexOf(\":\") === -1) {\n    return false;\n  }\n\n  // There is ':' in the string: a potential scheme separator.\n  // The string might be a proper URL already.\n  try {\n    new URL(potentialUrl);\n    return true;\n  } catch (e) {\n    return false;\n  }\n};\n\nexport const applyLinkTemplate = (template: LinkTemplate, value: string) =>\n  typeof template === \"string\" ? template.replace(\"%s\", value) : template(value);\n\nexport const formatLink = (templates: LinkConfig, link: Link) => {\n  const { url: originalUrl, name, type } = link;\n  if (isUrl(originalUrl)) {\n    return link;\n  } else {\n    const formattedLink = { ...link };\n    const { urlTemplate, nameTemplate } = templates[type ?? LinkType.DEFAULT] ?? {};\n    if (urlTemplate !== undefined) {\n      formattedLink.url = applyLinkTemplate(urlTemplate, originalUrl);\n    }\n    if (name === undefined && nameTemplate !== undefined) {\n      formattedLink.name = applyLinkTemplate(nameTemplate, originalUrl);\n    }\n    return formattedLink;\n  }\n};\n\nexport const formatLinks = (templates: LinkConfig, links: readonly Link[]) =>\n  links.map((link) => formatLink(templates, link));\n\nexport const createDefaultWriter = (config: { resultsDir?: string; emitter?: EventEmitter }) => {\n  return process.env.ALLURE_TEST_MODE\n    ? new MessageWriter(config.emitter)\n    : new FileSystemWriter({\n        resultsDir: config.resultsDir || \"./allure-results\",\n      });\n};\n"], "mappings": ";;;;;;;AAAA,SAASA,UAAU,EAAEC,UAAU,QAAQ,aAAa;AAEpD,OAAOC,EAAE,MAAM,SAAS;AACxB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,OAAO,MAAM,cAAc;AAElC,SAASC,SAAS,EAAEC,QAAQ,EAAEC,gBAAgB,QAAQ,gBAAgB;AAEtE,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,aAAa,QAAQ,2BAA2B;AAEzD,OAAO,IAAMC,UAAU,GAAGA,CAAA,KAAM;EAC9B,OAAOV,UAAU,CAAC,CAAC;AACrB,CAAC;AAED,OAAO,IAAMW,GAAG,GAAIC,GAAW,IAAK;EAClC,OAAOb,UAAU,CAAC,KAAK,CAAC,CAACc,MAAM,CAACD,GAAG,CAAC,CAACE,MAAM,CAAC,KAAK,CAAC;AACpD,CAAC;AAED,OAAO,IAAMC,sBAAsB,GAAIC,MAAkB,IAAK;EAAA,IAAAC,kBAAA;EAC5D,IAAID,MAAM,CAACE,SAAS,EAAE;IACpB,OAAOF,MAAM,CAACE,SAAS;EACzB;EAEA,IAAMC,IAAI,IAAAF,kBAAA,GAAGD,MAAM,CAACI,UAAU,cAAAH,kBAAA,cAAAA,kBAAA,GAAKD,MAAM,CAACK,QAAQ,GAAGV,GAAG,CAACK,MAAM,CAACK,QAAQ,CAAC,GAAG,IAAK;EAEjF,IAAI,CAACF,IAAI,EAAE;IACT,OAAO,EAAE;EACX;EAEA,IAAMG,YAAY,GAAGN,MAAM,CAACO,UAAU,CACnCC,MAAM,CAAEC,CAAC,IAAK,EAACA,CAAC,aAADA,CAAC,eAADA,CAAC,CAAEC,QAAQ,EAAC,CAC3BC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC;IAAA,IAAAC,OAAA,EAAAC,QAAA;IAAA,OAAK,EAAAD,OAAA,GAAAF,CAAC,CAACI,IAAI,cAAAF,OAAA,uBAANA,OAAA,CAAQG,aAAa,CAACJ,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEG,IAAI,CAAC,OAAAD,QAAA,GAAIH,CAAC,CAACM,KAAK,cAAAH,QAAA,uBAAPA,QAAA,CAASE,aAAa,CAACJ,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEK,KAAK,CAAC;EAAA,EAAC,CAClFC,GAAG,CAAEV,CAAC;IAAA,IAAAW,OAAA,EAAAC,QAAA;IAAA,UAAAC,MAAA,EAAAF,OAAA,GAAQX,CAAC,CAACO,IAAI,cAAAI,OAAA,cAAAA,OAAA,GAAI,MAAM,OAAAE,MAAA,EAAAD,QAAA,GAAIZ,CAAC,CAACS,KAAK,cAAAG,QAAA,cAAAA,QAAA,GAAI,MAAM;EAAA,CAAE,CAAC,CACtDE,IAAI,CAAC,GAAG,CAAC;EACZ,IAAMC,UAAU,GAAG7B,GAAG,CAACW,YAAY,CAAC;EAEpC,UAAAgB,MAAA,CAAUnB,IAAI,OAAAmB,MAAA,CAAIE,UAAU;AAC9B,CAAC;AAED,OAAO,IAAMC,uBAAuB,GAAIzB,MAAkB,IAAK;EAC7D,OAAOA,MAAM,CAACK,QAAQ,GAAGV,GAAG,CAACK,MAAM,CAACK,QAAQ,CAAC,GAAGqB,SAAS;AAC3D,CAAC;AAED,IAAMC,gBAAgB,GAAIC,MAA0B,IAAK;EACvD,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,CAAC,CAAC;EACX;EACA,OAAOrC,gBAAgB,CAACsC,OAAO,CAACD,MAAM,CAAC;AACzC,CAAC;AAED,OAAO,IAAME,sBAAsB,GAAIC,KAAmB,IAA6B;EACrF,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACtB;EACF;EAEA,OAAO,CAAC,GAAGD,KAAK,CAAC,CAACpB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKc,gBAAgB,CAACf,CAAC,CAACgB,MAAM,CAAC,GAAGD,gBAAgB,CAACd,CAAC,CAACe,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9F,CAAC;AAED,OAAO,IAAMK,iBAAiB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,QAAgB,EAAkC;IACxF,IAAI;MACF,IAAMC,IAAI,SAASnD,QAAQ,CAACkD,QAAQ,EAAE;QAAEE,QAAQ,EAAE;MAAS,CAAC,CAAC;MAE7D,OAAOD,IAAI,4BAAAf,MAAA,CAA4Be,IAAI,IAAKX,SAAS;IAC3D,CAAC,CAAC,OAAOa,CAAC,EAAE;MACV;MACAC,OAAO,CAACC,KAAK,wBAAAnB,MAAA,CAAwBc,QAAQ,GAAIG,CAAC,CAAC;MACnD,OAAOb,SAAS;IAClB;EACF,CAAC;EAAA,gBAVYO,iBAAiBA,CAAAS,EAAA;IAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;EAAA;AAAA,GAU7B;AAED,OAAO,IAAMC,cAAc,GAAG,CAAC,MAAM;EACnC,IAAIC,iBAAgC,GAAG,IAAI;EAE3C,IAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAMC,GAAG,GAAG5D,OAAO,CAAC4D,GAAG,CAAC,CAAC;IACzB,IAAIC,OAAO,GAAGD,GAAG;IACjB,IAAIE,GAAG;IAEP,GAAG;MACDA,GAAG,GAAGD,OAAO;MACb,IAAI;QACFhE,EAAE,CAACkE,UAAU,CAAChE,IAAI,CAACoC,IAAI,CAAC2B,GAAG,EAAE,cAAc,CAAC,EAAEjE,EAAE,CAACmE,SAAS,CAACC,IAAI,CAAC;;QAEhE;QACA,OAAOH,GAAG;MACZ,CAAC,CAAC,OAAAI,OAAA,EAAM,CAAC;MAETL,OAAO,GAAG9D,IAAI,CAACoE,OAAO,CAACL,GAAG,CAAC;IAC7B,CAAC,QAAQD,OAAO,CAACjB,MAAM,GAAGkB,GAAG,CAAClB,MAAM;;IAEpC;IACA,OAAOgB,GAAG;EACZ,CAAC;EAED,OAAO,MAAM;IACX,IAAI,CAACF,iBAAiB,EAAE;MACtBA,iBAAiB,GAAGC,wBAAwB,CAAC,CAAC;IAChD;IACA,OAAOD,iBAAiB;EAC1B,CAAC;AACH,CAAC,EAAE,CAAC;AAEJ,OAAO,IAAMU,eAAe,GAAIC,QAAgB,IAAK;EACnD,IAAItE,IAAI,CAACuE,UAAU,CAACD,QAAQ,CAAC,EAAE;IAC7B,IAAME,WAAW,GAAGd,cAAc,CAAC,CAAC;IACpCY,QAAQ,GAAGtE,IAAI,CAACyE,QAAQ,CAACD,WAAW,EAAEF,QAAQ,CAAC;EACjD;EACA,OAAOA,QAAQ;AACjB,CAAC;AAED,OAAO,IAAMI,YAAY,GAAIJ,QAAgB,IAAK;EAChD,IAAIrE,OAAO,CAAC0E,QAAQ,KAAK,OAAO,EAAE;IAChC,OAAOL,QAAQ,CAACM,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;EACvC;EACA,OAAON,QAAQ;AACjB,CAAC;AAED,OAAO,IAAMO,SAAS,GAAOC,GAAM,IAAQC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAAC,CAAC;AAE1E,OAAO,IAAMI,cAAc,GAAIC,MAAyB,IAAc;EACpE,IAAIA,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE;IACvB,OAAO,EAAE;EACX;EAEA,IAAM,CAACuC,WAAW,EAAEC,KAAK,EAAE,GAAGC,SAAS,CAAC,GAAGH,MAAM;EACjD,IAAMI,MAAe,GAAG,EAAE;EAE1B,IAAIH,WAAW,EAAE;IACfG,MAAM,CAACC,IAAI,CAAC;MACV3D,IAAI,EAAE3B,SAAS,CAACuF,YAAY;MAC5B1D,KAAK,EAAEqD;IACT,CAAC,CAAC;EACJ;EAEA,IAAIC,KAAK,EAAE;IACTE,MAAM,CAACC,IAAI,CAAC;MACV3D,IAAI,EAAE3B,SAAS,CAACwF,KAAK;MACrB3D,KAAK,EAAEsD;IACT,CAAC,CAAC;EACJ;EAEA,IAAIC,SAAS,CAACzC,MAAM,GAAG,CAAC,EAAE;IACxB0C,MAAM,CAACC,IAAI,CAAC;MACV3D,IAAI,EAAE3B,SAAS,CAACyF,SAAS;MACzB5D,KAAK,EAAEuD,SAAS,CAAClD,IAAI,CAAC,KAAK;IAC7B,CAAC,CAAC;EACJ;EAEA,OAAOmD,MAAM;AACf,CAAC;AAED,IAAMK,eAAkC,GAAG,CAAC1F,SAAS,CAACuF,YAAY,EAAEvF,SAAS,CAACwF,KAAK,EAAExF,SAAS,CAACyF,SAAS,CAAC;;AAEzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAME,iBAAiB,GAAGA,CAACC,IAAyB,EAAEC,aAAgC,KAAK;EAAA,IAAAC,YAAA;EAChG,IAAI,GAAAA,YAAA,GAACF,IAAI,CAACP,MAAM,cAAAS,YAAA,eAAXA,YAAA,CAAaC,IAAI,CAAEC,CAAC,IAAKN,eAAe,CAACO,QAAQ,CAACD,CAAC,CAACrE,IAAI,CAAC,CAAC,GAAE;IAAA,IAAAuE,aAAA;IAC/DN,IAAI,CAACP,MAAM,GAAG,CAAC,KAAAa,aAAA,GAAIN,IAAI,CAACP,MAAM,cAAAa,aAAA,cAAAA,aAAA,GAAI,EAAE,CAAC,EAAE,GAAGlB,cAAc,CAACa,aAAa,CAAC,CAAC;EAC1E;EAEA,OAAOH,eAAe,CAAC5D,GAAG,CAAEH,IAAI;IAAA,IAAAwE,aAAA;IAAA,QAAAA,aAAA,GAAKP,IAAI,CAACP,MAAM,cAAAc,aAAA,uBAAXA,aAAA,CAAaJ,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACrE,IAAI,KAAKA,IAAI,CAAC;EAAA,EAAC,CAACR,MAAM,CAACiF,OAAO,CAAC;AACjG,CAAC;AAED,IAAMC,YAAY,GAAG,qBAAqB;EACxCC,eAAe,GAAGC,MAAM,CAACF,YAAY,CAACG,MAAM,CAAC;AAE/C,OAAO,IAAMC,YAAY,GAAI5E,KAAa,IAAa;EACrD,OAAOyE,eAAe,CAACV,IAAI,CAAC/D,KAAK,CAAC,GAAGA,KAAK,CAAC6E,OAAO,CAACL,YAAY,EAAE,MAAM,CAAC,GAAGxE,KAAK;AAClF,CAAC;;AAED;AACA,IAAM8E,KAAK,GAAIC,YAAoB,IAAK;EACtC;EACA,IAAIA,YAAY,CAACpE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IACpC,OAAO,KAAK;EACd;;EAEA;EACA;EACA,IAAI;IACF,IAAIqE,GAAG,CAACD,YAAY,CAAC;IACrB,OAAO,IAAI;EACb,CAAC,CAAC,OAAO1D,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,IAAM4D,iBAAiB,GAAGA,CAACC,QAAsB,EAAElF,KAAa,KACrE,OAAOkF,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,CAACL,OAAO,CAAC,IAAI,EAAE7E,KAAK,CAAC,GAAGkF,QAAQ,CAAClF,KAAK,CAAC;AAEhF,OAAO,IAAMmF,UAAU,GAAGA,CAACC,SAAqB,EAAEC,IAAU,KAAK;EAC/D,IAAM;IAAEC,GAAG,EAAEC,WAAW;IAAEzF,IAAI;IAAE0F;EAAK,CAAC,GAAGH,IAAI;EAC7C,IAAIP,KAAK,CAACS,WAAW,CAAC,EAAE;IACtB,OAAOF,IAAI;EACb,CAAC,MAAM;IAAA,IAAAI,UAAA;IACL,IAAMC,aAAa,GAAAC,aAAA,KAAQN,IAAI,CAAE;IACjC,IAAM;MAAEO,WAAW;MAAEC;IAAa,CAAC,IAAAJ,UAAA,GAAGL,SAAS,CAACI,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAIpH,QAAQ,CAAC0H,OAAO,CAAC,cAAAL,UAAA,cAAAA,UAAA,GAAI,CAAC,CAAC;IAC/E,IAAIG,WAAW,KAAKpF,SAAS,EAAE;MAC7BkF,aAAa,CAACJ,GAAG,GAAGL,iBAAiB,CAACW,WAAW,EAAEL,WAAW,CAAC;IACjE;IACA,IAAIzF,IAAI,KAAKU,SAAS,IAAIqF,YAAY,KAAKrF,SAAS,EAAE;MACpDkF,aAAa,CAAC5F,IAAI,GAAGmF,iBAAiB,CAACY,YAAY,EAAEN,WAAW,CAAC;IACnE;IACA,OAAOG,aAAa;EACtB;AACF,CAAC;AAED,OAAO,IAAMK,WAAW,GAAGA,CAACX,SAAqB,EAAEY,KAAsB,KACvEA,KAAK,CAAC/F,GAAG,CAAEoF,IAAI,IAAKF,UAAU,CAACC,SAAS,EAAEC,IAAI,CAAC,CAAC;AAElD,OAAO,IAAMY,mBAAmB,GAAIC,MAAuD,IAAK;EAC9F,OAAOhI,OAAO,CAACiI,GAAG,CAACC,gBAAgB,GAC/B,IAAI7H,aAAa,CAAC2H,MAAM,CAACG,OAAO,CAAC,GACjC,IAAI/H,gBAAgB,CAAC;IACnBgI,UAAU,EAAEJ,MAAM,CAACI,UAAU,IAAI;EACnC,CAAC,CAAC;AACR,CAAC", "ignoreList": []}