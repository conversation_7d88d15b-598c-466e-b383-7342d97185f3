{"version": 3, "file": "index.js", "names": ["_runtime", "require", "_MessageTestRuntime", "_MessageHolderTestRuntime"], "sources": ["../../../../src/sdk/runtime/index.ts"], "sourcesContent": ["export { setGlobalTestRuntime, getGlobalTestRuntime, getGlobalTestRuntimeWithAutoconfig } from \"./runtime.js\";\nexport type { TestRuntime } from \"./types.js\";\nexport { MessageTestRuntime } from \"./MessageTestRuntime.js\";\nexport { MessageHolderTestRuntime } from \"./MessageHolderTestRuntime.js\";\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAEA,IAAAC,mBAAA,GAAAD,OAAA;AACA,IAAAE,yBAAA,GAAAF,OAAA", "ignoreList": []}