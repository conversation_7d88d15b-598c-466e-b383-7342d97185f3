{"version": 3, "file": "types.js", "names": [], "sources": ["../../../../src/sdk/runtime/types.ts"], "sourcesContent": ["import type { AttachmentOptions, Label, Link, ParameterMode, ParameterOptions, Status } from \"../../model.js\";\n\nexport interface TestRuntime {\n  labels: (...labels: Label[]) => PromiseLike<void>;\n\n  links: (...links: Link[]) => PromiseLike<void>;\n\n  parameter: (name: string, value: string, options?: ParameterOptions) => PromiseLike<void>;\n\n  description: (markdown: string) => PromiseLike<void>;\n\n  descriptionHtml: (html: string) => PromiseLike<void>;\n\n  displayName: (name: string) => PromiseLike<void>;\n\n  historyId: (value: string) => PromiseLike<void>;\n\n  testCaseId: (value: string) => PromiseLike<void>;\n\n  attachment: (name: string, content: Buffer | string, options: AttachmentOptions) => PromiseLike<void>;\n\n  attachmentFromPath: (name: string, path: string, options: Omit<AttachmentOptions, \"encoding\">) => PromiseLike<void>;\n\n  logStep: (name: string, status?: Status, error?: Error) => PromiseLike<void>;\n\n  step: <T = void>(name: string, body: () => T | PromiseLike<T>) => PromiseLike<T>;\n\n  stepDisplayName: (name: string) => PromiseLike<void>;\n\n  stepParameter: (name: string, value: string, mode?: ParameterMode) => PromiseLike<void>;\n}\n"], "mappings": "", "ignoreList": []}