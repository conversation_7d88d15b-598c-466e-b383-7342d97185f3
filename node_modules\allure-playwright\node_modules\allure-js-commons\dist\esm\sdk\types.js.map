{"version": 3, "file": "types.js", "names": [], "sources": ["../../../src/sdk/types.ts"], "sourcesContent": ["import type { Label, Link, Parameter, Status, StatusDetails, TestResult, TestResultContainer } from \"../model.js\";\n\ntype RuntimeMessageBase<T extends string> = {\n  type: T;\n};\n\nexport type RuntimeMetadataMessage = RuntimeMessageBase<\"metadata\"> & {\n  data: {\n    labels?: Label[];\n    links?: Link[];\n    parameters?: Parameter[];\n    description?: string;\n    descriptionHtml?: string;\n    testCaseId?: string;\n    historyId?: string;\n    displayName?: string;\n  };\n};\n\nexport type RuntimeStartStepMessage = RuntimeMessageBase<\"step_start\"> & {\n  data: {\n    name: string;\n    start: number;\n  };\n};\n\nexport type RuntimeStepMetadataMessage = RuntimeMessageBase<\"step_metadata\"> & {\n  data: {\n    name?: string;\n    parameters?: Parameter[];\n  };\n};\n\nexport type RuntimeStopStepMessage = RuntimeMessageBase<\"step_stop\"> & {\n  data: {\n    stop: number;\n    status: Status;\n    statusDetails?: StatusDetails;\n  };\n};\n\nexport type RuntimeAttachmentContentMessage = RuntimeMessageBase<\"attachment_content\"> & {\n  data: {\n    name: string;\n    content: string;\n    encoding: BufferEncoding;\n    contentType: string;\n    fileExtension?: string;\n    wrapInStep?: boolean;\n    timestamp?: number;\n  };\n};\n\nexport type RuntimeAttachmentPathMessage = RuntimeMessageBase<\"attachment_path\"> & {\n  data: {\n    name: string;\n    path: string;\n    contentType: string;\n    fileExtension?: string;\n    wrapInStep?: boolean;\n    timestamp?: number;\n  };\n};\n\nexport type RuntimeMessage =\n  | RuntimeMetadataMessage\n  | RuntimeStartStepMessage\n  | RuntimeStepMetadataMessage\n  | RuntimeStopStepMessage\n  | RuntimeAttachmentContentMessage\n  | RuntimeAttachmentPathMessage;\n\nexport interface TestPlanV1Test {\n  id?: string | number;\n  selector?: string;\n}\n\nexport interface TestPlanV1 {\n  version: \"1.0\";\n  tests: TestPlanV1Test[];\n}\n\nexport type EnvironmentInfo = Record<string, string | undefined>;\n\nexport interface Category {\n  name?: string;\n  description?: string;\n  descriptionHtml?: string;\n  messageRegex?: string | RegExp;\n  traceRegex?: string | RegExp;\n  matchedStatuses?: Status[];\n  flaky?: boolean;\n}\n\nexport interface ExecutorInfo {\n  name?: string;\n  type?: string;\n  url?: string;\n  buildOrder?: number;\n  buildName?: string;\n  buildUrl?: string;\n  reportUrl?: string;\n  reportName?: string;\n}\n\nexport interface AllureResults {\n  tests: TestResult[];\n  groups: TestResultContainer[];\n  attachments: Record<string, Buffer | string>;\n  envInfo?: EnvironmentInfo;\n  categories?: Category[];\n}\n\nexport type SerializerReplacerFunc = (this: unknown, key: string, value: unknown) => unknown;\n\nexport type SerializeOptions = {\n  maxDepth?: number;\n  maxLength?: number;\n  replacer?: SerializerReplacerFunc;\n};\n"], "mappings": "", "ignoreList": []}