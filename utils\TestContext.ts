import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontex<PERSON>, <PERSON> } from '@playwright/test';
import { logger } from './logger';

export interface TestData {
  [key: string]: any;
}

export interface TestConfig {
  baseUrl: string;
  timeout: number;
  headless: boolean;
  viewport: {
    width: number;
    height: number;
  };
}

export class TestContext {
  private static instance: TestContext;
  private _browser: Browser | null = null;
  private _context: BrowserContext | null = null;
  private _page: Page | null = null;
  private _testData: TestData = {};
  private _config: TestConfig;

  private constructor() {
    this._config = {
      baseUrl: process.env.BASE_URL || 'http://localhost:3000',
      timeout: parseInt(process.env.TIMEOUT || '30000'),
      headless: process.env.HEADLESS === 'true',
      viewport: {
        width: parseInt(process.env.VIEWPORT_WIDTH || '1280'),
        height: parseInt(process.env.VIEWPORT_HEIGHT || '720'),
      },
    };
  }

  public static getInstance(): TestContext {
    if (!TestContext.instance) {
      TestContext.instance = new TestContext();
    }
    return TestContext.instance;
  }

  // Browser management
  public setBrowser(browser: Browser): void {
    this._browser = browser;
    logger.info('🌐 Browser instance set');
  }

  public getBrowser(): Browser {
    if (!this._browser) {
      throw new Error('Browser not initialized. Call setBrowser() first.');
    }
    return this._browser;
  }

  // Context management
  public setContext(context: BrowserContext): void {
    this._context = context;
    logger.info('📋 Browser context set');
  }

  public getContext(): BrowserContext {
    if (!this._context) {
      throw new Error('Browser context not initialized. Call setContext() first.');
    }
    return this._context;
  }

  // Page management
  public setPage(page: Page): void {
    this._page = page;
    logger.info('📄 Page instance set');
  }

  public getPage(): Page {
    if (!this._page) {
      throw new Error('Page not initialized. Call setPage() first.');
    }
    return this._page;
  }

  // Test data management
  public setTestData(key: string, value: any): void {
    this._testData[key] = value;
    logger.debug(`💾 Test data set: ${key} = ${JSON.stringify(value)}`);
  }

  public getTestData(key: string): any {
    const value = this._testData[key];
    logger.debug(`📖 Test data retrieved: ${key} = ${JSON.stringify(value)}`);
    return value;
  }

  public getAllTestData(): TestData {
    return { ...this._testData };
  }

  public clearTestData(): void {
    this._testData = {};
    logger.info('🧹 Test data cleared');
  }

  // Configuration management
  public getConfig(): TestConfig {
    return { ...this._config };
  }

  public updateConfig(updates: Partial<TestConfig>): void {
    this._config = { ...this._config, ...updates };
    logger.info(`⚙️ Configuration updated: ${JSON.stringify(updates)}`);
  }

  // Cleanup methods
  public async cleanup(): Promise<void> {
    logger.info('🧹 Starting cleanup...');
    
    if (this._page) {
      await this._page.close();
      this._page = null;
      logger.info('📄 Page closed');
    }

    if (this._context) {
      await this._context.close();
      this._context = null;
      logger.info('📋 Browser context closed');
    }

    if (this._browser) {
      await this._browser.close();
      this._browser = null;
      logger.info('🌐 Browser closed');
    }

    this.clearTestData();
    logger.info('✅ Cleanup completed');
  }

  // Utility methods
  public async takeScreenshot(name: string): Promise<string> {
    if (!this._page) {
      throw new Error('Page not initialized');
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${name}-${timestamp}.png`;
    const path = `logs/${filename}`;
    
    await this._page.screenshot({ path, fullPage: true });
    logger.info(`📸 Screenshot saved: ${filename}`);
    
    return path;
  }

  public async getCurrentUrl(): Promise<string> {
    if (!this._page) {
      throw new Error('Page not initialized');
    }
    return this._page.url();
  }

  public async getPageTitle(): Promise<string> {
    if (!this._page) {
      throw new Error('Page not initialized');
    }
    return this._page.title();
  }

  // Reset singleton (useful for testing)
  public static reset(): void {
    TestContext.instance = new TestContext();
  }
}
