{"browser": {"headless": false, "slowMo": 0, "viewport": {"width": 1280, "height": 720}, "timeout": 30000}, "test": {"retries": 0, "workers": 1, "timeout": 30000, "screenshotOnFailure": true, "videoOnFailure": true, "traceOnFailure": true}, "logging": {"level": "info", "console": true, "file": true, "maxFiles": 14, "maxSize": "20m"}, "reporting": {"allure": true, "cucumber": true, "html": true, "junit": true}, "api": {"timeout": 10000, "retries": 3}, "database": {"enabled": false, "cleanup": true}}