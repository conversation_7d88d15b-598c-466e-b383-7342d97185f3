{"version": 3, "file": "utils.js", "names": ["_model", "require", "_wrapRegExp", "e", "r", "BabelRegExp", "RegExp", "prototype", "WeakMap", "t", "p", "o", "set", "get", "_setPrototypeOf", "buildGroups", "Object", "keys", "reduce", "i", "length", "create", "_inherits", "exec", "call", "groups", "indices", "Symbol", "replace", "Array", "isArray", "join", "arguments", "slice", "push", "apply", "TypeError", "constructor", "value", "writable", "configurable", "defineProperty", "setPrototypeOf", "bind", "__proto__", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "_objectSpread", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "getStatusFromError", "error", "test", "name", "message", "stack", "inspect", "Status", "FAILED", "BROKEN", "exports", "ansiRegex", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "pattern", "stripAnsi", "str", "regex", "actualAndExpected", "matcherR<PERSON>ult", "actual", "serialize", "expected", "getMessageAndTraceFromError", "trace", "allureMetadataRegexp", "type", "allureTitleMetadataRegexp", "allureTitleMetadataRegexpGlobal", "allureIdRegexp", "id", "allureLabelRegexp", "getTypeFromAllureTitleMetadataMatch", "match", "getValueFromAllureTitleMetadataMatch", "_match$", "quotesRegexp", "quoteOpenRegexp", "concat", "source", "quoteCloseRegexp", "matchedValue", "isMetadataTag", "tag", "getMetadataLabel", "_match$groups", "subtype", "split", "LabelName", "ALLURE_ID", "extractMetadataFromString", "title", "labels", "links", "metadata", "matchAll", "cleanTitle", "replaceAll", "Boolean", "acc", "word", "trim", "m", "url", "isAnyStepFailed", "item", "isFailed", "status", "steps", "find", "step", "isAllStepsEnded", "every", "val", "stop", "<PERSON><PERSON><PERSON><PERSON>", "testResult", "labelName", "l", "hasStepMessage", "messages", "some", "getStepsMessagesPair", "unfinishedStepIdx", "findLastIndex", "getUnfinishedStepsMessages", "grouppedStepsMessage", "isPromise", "obj", "then", "max<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "replacer", "limitString", "JSON", "stringify", "createSerializeReplacer", "userDefinedReplacer", "parents", "limitingReplacer", "_", "is", "at", "pop", "includes", "Map", "excludeCircularRefsFromMap", "Set", "excludeCircularRefsFromSet", "composeReplacers", "first", "second", "k", "v", "map", "from", "_ref", "_ref2", "substring"], "sources": ["../../../src/sdk/utils.ts"], "sourcesContent": ["import type { FixtureResult, Label, Link, StatusDetails, StepResult, TestResult } from \"../model.js\";\nimport { LabelName, Status } from \"../model.js\";\nimport type { RuntimeMessage, SerializeOptions, SerializerReplacerFunc } from \"./types.js\";\n\nexport const getStatusFromError = (error: Partial<Error>): Status => {\n  switch (true) {\n    /**\n     * Native `node:assert` and `chai` (`vitest` uses it under the hood) throw `AssertionError`\n     * `jest` throws `JestAssertionError` instance\n     * `jasmine` throws `ExpectationFailed` instance\n     * `vitest` throws `Error` for extended assertions, so we look into stack\n     */\n    case /assert/gi.test(error.constructor.name):\n    case /expectation/gi.test(error.constructor.name):\n    case error.name && /assert/gi.test(error.name):\n    case error.message && /assert/gi.test(error.message):\n    case error.stack && /@vitest\\/expect/gi.test(error.stack):\n    case error.stack && /playwright\\/lib\\/matchers\\/expect\\.js/gi.test(error.stack):\n    case \"matcherResult\" in error:\n    case \"inspect\" in error && typeof error.inspect === \"function\":\n      return Status.FAILED;\n    default:\n      return Status.BROKEN;\n  }\n};\n\n/**\n * Source: https://github.com/chalk/ansi-regex\n */\nconst ansiRegex = ({ onlyFirst = false } = {}) => {\n  const pattern = [\n    \"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\",\n    \"(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-nq-uy=><~]))\",\n  ].join(\"|\");\n\n  return new RegExp(pattern, onlyFirst ? undefined : \"g\");\n};\n\n/**\n * https://github.com/chalk/strip-ansi\n */\nexport const stripAnsi = (str: string): string => {\n  const regex = ansiRegex();\n  return str.replace(regex, \"\");\n};\n\nconst actualAndExpected = (value: unknown): { actual?: string; expected?: string } => {\n  if (!value || typeof value !== \"object\") {\n    return {};\n  }\n\n  // support for jest asserts\n  if (\"matcherResult\" in value && value.matcherResult !== undefined && typeof value.matcherResult === \"object\") {\n    return {\n      actual: serialize((value.matcherResult as any).actual),\n      expected: serialize((value.matcherResult as any).expected),\n    };\n  }\n\n  const actual = \"actual\" in value && value.actual !== undefined ? { actual: serialize(value.actual) } : {};\n  const expected = \"expected\" in value && value.expected !== undefined ? { expected: serialize(value.expected) } : {};\n  return {\n    ...actual,\n    ...expected,\n  };\n};\n\nexport const getMessageAndTraceFromError = (\n  error:\n    | Error\n    | {\n        message?: string;\n        stack?: string;\n      },\n): StatusDetails => {\n  const { message, stack } = error;\n  return {\n    message: message ? stripAnsi(message) : undefined,\n    trace: stack ? stripAnsi(stack) : undefined,\n    ...actualAndExpected(error),\n  };\n};\n\ntype AllureTitleMetadataMatch = RegExpMatchArray & {\n  groups: {\n    type?: string;\n    v1?: string;\n    v2?: string;\n    v3?: string;\n    v4?: string;\n  };\n};\n\nexport const allureMetadataRegexp = /(?:^|\\s)@?allure\\.(?<type>\\S+)$/;\nexport const allureTitleMetadataRegexp = /(?:^|\\s)@?allure\\.(?<type>[^:=\\s]+)[:=](\"[^\"]+\"|'[^']+'|`[^`]+`|\\S+)/;\nexport const allureTitleMetadataRegexpGlobal = new RegExp(allureTitleMetadataRegexp, \"g\");\nexport const allureIdRegexp = /(?:^|\\s)@?allure\\.id[:=](?<id>\\S+)/;\nexport const allureLabelRegexp = /(?:^|\\s)@?allure\\.label\\.(?<name>[^:=\\s]+)[:=](?<value>[^\\s]+)/;\n\nexport const getTypeFromAllureTitleMetadataMatch = (match: AllureTitleMetadataMatch) => {\n  return match?.[1];\n};\n\nexport const getValueFromAllureTitleMetadataMatch = (match: AllureTitleMetadataMatch) => {\n  const quotesRegexp = /['\"`]/;\n  const quoteOpenRegexp = new RegExp(`^${quotesRegexp.source}`);\n  const quoteCloseRegexp = new RegExp(`${quotesRegexp.source}$`);\n  const matchedValue = match?.[2] ?? \"\";\n\n  if (quoteOpenRegexp.test(matchedValue) && quoteCloseRegexp.test(matchedValue)) {\n    return matchedValue.slice(1, -1);\n  }\n\n  return matchedValue;\n};\n\nexport const isMetadataTag = (tag: string) => {\n  return allureMetadataRegexp.test(tag);\n};\n\nexport const getMetadataLabel = (tag: string, value?: string): Label | undefined => {\n  const match = tag.match(allureMetadataRegexp);\n  const type = match?.groups?.type;\n\n  if (!type) {\n    return undefined;\n  }\n\n  const [subtype, name] = type.split(\".\");\n\n  return {\n    name: subtype === \"id\" ? LabelName.ALLURE_ID : name,\n    value: value ?? \"\",\n  };\n};\n\nexport const extractMetadataFromString = (\n  title: string,\n): {\n  labels: Label[];\n  links: Link[];\n  cleanTitle: string;\n} => {\n  const labels = [] as Label[];\n  const links = [] as Link[];\n  const metadata = title.matchAll(allureTitleMetadataRegexpGlobal);\n  const cleanTitle = title\n    .replaceAll(allureTitleMetadataRegexpGlobal, \"\")\n    .split(\" \")\n    .filter(Boolean)\n    .reduce((acc, word) => {\n      if (/^[\\n\\r]/.test(word)) {\n        return acc + word;\n      }\n\n      return `${acc} ${word}`;\n    }, \"\")\n    .trim();\n\n  for (const m of metadata) {\n    const match = m as AllureTitleMetadataMatch;\n    const type = getTypeFromAllureTitleMetadataMatch(match);\n    const value = getValueFromAllureTitleMetadataMatch(match);\n\n    if (!type || !value) {\n      continue;\n    }\n\n    const [subtype, name] = type.split(\".\");\n\n    switch (subtype) {\n      case \"id\":\n        labels.push({ name: LabelName.ALLURE_ID, value });\n        break;\n      case \"label\":\n        labels.push({ name, value });\n        break;\n      case \"link\":\n        links.push({ type: name, url: value });\n        break;\n    }\n  }\n\n  return {\n    labels,\n    links,\n    cleanTitle,\n  };\n};\n\nexport const isAnyStepFailed = (item: StepResult | TestResult | FixtureResult): boolean => {\n  const isFailed = item.status === Status.FAILED;\n\n  if (isFailed || item.steps.length === 0) {\n    return isFailed;\n  }\n\n  return !!item.steps.find((step) => isAnyStepFailed(step));\n};\n\nexport const isAllStepsEnded = (item: StepResult | TestResult | FixtureResult): boolean => {\n  return item.steps.every((val) => val.stop && isAllStepsEnded(val));\n};\n\nexport const hasLabel = (testResult: TestResult, labelName: LabelName | string): boolean => {\n  return !!testResult.labels.find((l) => l.name === labelName);\n};\n\nexport const hasStepMessage = (messages: RuntimeMessage[]) => {\n  return messages.some((message) => message.type === \"step_start\" || message.type === \"step_stop\");\n};\n\nexport const getStepsMessagesPair = (messages: RuntimeMessage[]) =>\n  messages.reduce((acc, message) => {\n    if (message.type !== \"step_start\" && message.type !== \"step_stop\") {\n      return acc;\n    }\n\n    if (message.type === \"step_start\") {\n      acc.push([message]);\n\n      return acc;\n    }\n\n    const unfinishedStepIdx = acc.findLastIndex((step) => step.length === 1);\n\n    if (unfinishedStepIdx === -1) {\n      return acc;\n    }\n\n    acc[unfinishedStepIdx].push(message);\n\n    return acc;\n  }, [] as RuntimeMessage[][]);\n\nexport const getUnfinishedStepsMessages = (messages: RuntimeMessage[]) => {\n  const grouppedStepsMessage = getStepsMessagesPair(messages);\n\n  return grouppedStepsMessage.filter((step) => step.length === 1);\n};\n\nexport const isPromise = <T = any>(obj: any): obj is PromiseLike<T> =>\n  !!obj && (typeof obj === \"object\" || typeof obj === \"function\") && typeof obj.then === \"function\";\n\nexport const serialize = (value: any, { maxDepth = 0, maxLength = 0, replacer }: SerializeOptions = {}): string =>\n  limitString(\n    typeof value === \"object\" ? JSON.stringify(value, createSerializeReplacer(maxDepth, replacer)) : String(value),\n    maxLength,\n  );\n\nconst createSerializeReplacer = (maxDepth: number, userDefinedReplacer: SerializeOptions[\"replacer\"]) => {\n  const parents: unknown[] = [];\n  const limitingReplacer = function (this: unknown, _: string, value: unknown) {\n    if (typeof value !== \"object\" || value === null) {\n      return value;\n    }\n\n    while (parents.length > 0 && !Object.is(parents.at(-1), this)) {\n      parents.pop();\n    }\n\n    if ((maxDepth && parents.length >= maxDepth) || parents.includes(value)) {\n      return undefined;\n    }\n\n    parents.push(value);\n\n    return value instanceof Map\n      ? excludeCircularRefsFromMap(parents, value)\n      : value instanceof Set\n        ? excludeCircularRefsFromSet(parents, value)\n        : value;\n  };\n  return userDefinedReplacer ? composeReplacers(userDefinedReplacer, limitingReplacer) : limitingReplacer;\n};\n\nconst composeReplacers = (first: SerializerReplacerFunc, second: SerializerReplacerFunc): SerializerReplacerFunc =>\n  function (k, v) {\n    return second.call(this, k, first.call(this, k, v));\n  };\n\nconst excludeCircularRefsFromMap = (parents: any[], map: Map<any, any>) => {\n  return Array.from(map)\n    .filter(([k]) => !parents.includes(k))\n    .map(([k, v]) => [k, parents.includes(v) ? undefined : v]);\n};\n\nconst excludeCircularRefsFromSet = (parents: any[], set: Set<any>) => {\n  return Array.from(set).map((v) => (parents.includes(v) ? undefined : v));\n};\n\nconst limitString = (value: string, maxLength: number) =>\n  maxLength && value.length > maxLength ? `${value.substring(0, maxLength)}...` : value;\n"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AAAgD,SAAAC,YAAA,IAAAA,WAAA,YAAAA,YAAAC,CAAA,EAAAC,CAAA,eAAAC,WAAA,CAAAF,CAAA,UAAAC,CAAA,UAAAD,CAAA,GAAAG,MAAA,CAAAC,SAAA,EAAAH,CAAA,OAAAI,OAAA,aAAAH,YAAAF,CAAA,EAAAM,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAL,MAAA,CAAAH,CAAA,EAAAM,CAAA,UAAAL,CAAA,CAAAQ,GAAA,CAAAD,CAAA,EAAAD,CAAA,IAAAN,CAAA,CAAAS,GAAA,CAAAV,CAAA,IAAAW,eAAA,CAAAH,CAAA,EAAAN,WAAA,CAAAE,SAAA,cAAAQ,YAAAZ,CAAA,EAAAM,CAAA,QAAAC,CAAA,GAAAN,CAAA,CAAAS,GAAA,CAAAJ,CAAA,UAAAO,MAAA,CAAAC,IAAA,CAAAP,CAAA,EAAAQ,MAAA,WAAAd,CAAA,EAAAK,CAAA,QAAAE,CAAA,GAAAD,CAAA,CAAAD,CAAA,0BAAAE,CAAA,EAAAP,CAAA,CAAAK,CAAA,IAAAN,CAAA,CAAAQ,CAAA,kBAAAQ,CAAA,iBAAAhB,CAAA,CAAAQ,CAAA,CAAAQ,CAAA,MAAAA,CAAA,OAAAR,CAAA,CAAAS,MAAA,GAAAD,CAAA,IAAAf,CAAA,CAAAK,CAAA,IAAAN,CAAA,CAAAQ,CAAA,CAAAQ,CAAA,aAAAf,CAAA,KAAAY,MAAA,CAAAK,MAAA,kBAAAC,SAAA,CAAAjB,WAAA,EAAAC,MAAA,GAAAD,WAAA,CAAAE,SAAA,CAAAgB,IAAA,aAAAnB,CAAA,QAAAK,CAAA,GAAAN,CAAA,CAAAoB,IAAA,CAAAC,IAAA,OAAApB,CAAA,OAAAK,CAAA,IAAAA,CAAA,CAAAgB,MAAA,GAAAV,WAAA,CAAAN,CAAA,aAAAC,CAAA,GAAAD,CAAA,CAAAiB,OAAA,EAAAhB,CAAA,KAAAA,CAAA,CAAAe,MAAA,GAAAV,WAAA,CAAAL,CAAA,mBAAAD,CAAA,KAAAJ,WAAA,CAAAE,SAAA,CAAAoB,MAAA,CAAAC,OAAA,cAAAnB,CAAA,EAAAC,CAAA,2BAAAA,CAAA,QAAAC,CAAA,GAAAP,CAAA,CAAAS,GAAA,eAAAV,CAAA,CAAAwB,MAAA,CAAAC,OAAA,EAAAJ,IAAA,OAAAf,CAAA,EAAAC,CAAA,CAAAkB,OAAA,+BAAAzB,CAAA,EAAAC,CAAA,EAAAK,CAAA,eAAAA,CAAA,SAAAN,CAAA,MAAAO,CAAA,GAAAC,CAAA,CAAAP,CAAA,UAAAyB,KAAA,CAAAC,OAAA,CAAApB,CAAA,UAAAA,CAAA,CAAAqB,IAAA,2BAAArB,CAAA,SAAAA,CAAA,uCAAAA,CAAA,QAAAS,CAAA,gBAAAhB,CAAA,CAAAwB,MAAA,CAAAC,OAAA,EAAAJ,IAAA,OAAAf,CAAA,oBAAAN,CAAA,GAAA6B,SAAA,4BAAA7B,CAAA,CAAAA,CAAA,CAAAiB,MAAA,UAAAjB,CAAA,MAAA8B,KAAA,CAAAT,IAAA,CAAArB,CAAA,GAAA+B,IAAA,CAAAnB,WAAA,CAAAZ,CAAA,EAAAgB,CAAA,IAAAT,CAAA,CAAAyB,KAAA,OAAAhC,CAAA,gBAAAA,CAAA,CAAAwB,MAAA,CAAAC,OAAA,EAAAJ,IAAA,OAAAf,CAAA,EAAAC,CAAA,MAAAR,WAAA,CAAAiC,KAAA,OAAAH,SAAA;AAAA,SAAAV,UAAAb,CAAA,EAAAN,CAAA,6BAAAA,CAAA,aAAAA,CAAA,YAAAiC,SAAA,wDAAA3B,CAAA,CAAAF,SAAA,GAAAS,MAAA,CAAAK,MAAA,CAAAlB,CAAA,IAAAA,CAAA,CAAAI,SAAA,IAAA8B,WAAA,IAAAC,KAAA,EAAA7B,CAAA,EAAA8B,QAAA,MAAAC,YAAA,WAAAxB,MAAA,CAAAyB,cAAA,CAAAhC,CAAA,iBAAA8B,QAAA,SAAApC,CAAA,IAAAW,eAAA,CAAAL,CAAA,EAAAN,CAAA;AAAA,SAAAW,gBAAAL,CAAA,EAAAN,CAAA,WAAAW,eAAA,GAAAE,MAAA,CAAA0B,cAAA,GAAA1B,MAAA,CAAA0B,cAAA,CAAAC,IAAA,eAAAlC,CAAA,EAAAN,CAAA,WAAAM,CAAA,CAAAmC,SAAA,GAAAzC,CAAA,EAAAM,CAAA,KAAAK,eAAA,CAAAL,CAAA,EAAAN,CAAA;AAAA,SAAA0C,QAAA1C,CAAA,EAAAC,CAAA,QAAAK,CAAA,GAAAO,MAAA,CAAAC,IAAA,CAAAd,CAAA,OAAAa,MAAA,CAAA8B,qBAAA,QAAAnC,CAAA,GAAAK,MAAA,CAAA8B,qBAAA,CAAA3C,CAAA,GAAAC,CAAA,KAAAO,CAAA,GAAAA,CAAA,CAAAoC,MAAA,WAAA3C,CAAA,WAAAY,MAAA,CAAAgC,wBAAA,CAAA7C,CAAA,EAAAC,CAAA,EAAA6C,UAAA,OAAAxC,CAAA,CAAAyB,IAAA,CAAAC,KAAA,CAAA1B,CAAA,EAAAE,CAAA,YAAAF,CAAA;AAAA,SAAAyC,cAAA/C,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAA4B,SAAA,CAAAZ,MAAA,EAAAhB,CAAA,UAAAK,CAAA,WAAAuB,SAAA,CAAA5B,CAAA,IAAA4B,SAAA,CAAA5B,CAAA,QAAAA,CAAA,OAAAyC,OAAA,CAAA7B,MAAA,CAAAP,CAAA,OAAA0C,OAAA,WAAA/C,CAAA,IAAAgD,eAAA,CAAAjD,CAAA,EAAAC,CAAA,EAAAK,CAAA,CAAAL,CAAA,SAAAY,MAAA,CAAAqC,yBAAA,GAAArC,MAAA,CAAAsC,gBAAA,CAAAnD,CAAA,EAAAa,MAAA,CAAAqC,yBAAA,CAAA5C,CAAA,KAAAoC,OAAA,CAAA7B,MAAA,CAAAP,CAAA,GAAA0C,OAAA,WAAA/C,CAAA,IAAAY,MAAA,CAAAyB,cAAA,CAAAtC,CAAA,EAAAC,CAAA,EAAAY,MAAA,CAAAgC,wBAAA,CAAAvC,CAAA,EAAAL,CAAA,iBAAAD,CAAA;AAAA,SAAAiD,gBAAAjD,CAAA,EAAAC,CAAA,EAAAK,CAAA,YAAAL,CAAA,GAAAmD,cAAA,CAAAnD,CAAA,MAAAD,CAAA,GAAAa,MAAA,CAAAyB,cAAA,CAAAtC,CAAA,EAAAC,CAAA,IAAAkC,KAAA,EAAA7B,CAAA,EAAAwC,UAAA,MAAAT,YAAA,MAAAD,QAAA,UAAApC,CAAA,CAAAC,CAAA,IAAAK,CAAA,EAAAN,CAAA;AAAA,SAAAoD,eAAA9C,CAAA,QAAAU,CAAA,GAAAqC,YAAA,CAAA/C,CAAA,uCAAAU,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAqC,aAAA/C,CAAA,EAAAL,CAAA,2BAAAK,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAN,CAAA,GAAAM,CAAA,CAAAkB,MAAA,CAAA8B,WAAA,kBAAAtD,CAAA,QAAAgB,CAAA,GAAAhB,CAAA,CAAAqB,IAAA,CAAAf,CAAA,EAAAL,CAAA,uCAAAe,CAAA,SAAAA,CAAA,YAAAiB,SAAA,yEAAAhC,CAAA,GAAAsD,MAAA,GAAAC,MAAA,EAAAlD,CAAA;AAGzC,IAAMmD,kBAAkB,GAAIC,KAAqB,IAAa;EACnE,QAAQ,IAAI;IACV;AACJ;AACA;AACA;AACA;AACA;IACI,KAAK,UAAU,CAACC,IAAI,CAACD,KAAK,CAACxB,WAAW,CAAC0B,IAAI,CAAC;IAC5C,KAAK,eAAe,CAACD,IAAI,CAACD,KAAK,CAACxB,WAAW,CAAC0B,IAAI,CAAC;IACjD,KAAKF,KAAK,CAACE,IAAI,IAAI,UAAU,CAACD,IAAI,CAACD,KAAK,CAACE,IAAI,CAAC;IAC9C,KAAKF,KAAK,CAACG,OAAO,IAAI,UAAU,CAACF,IAAI,CAACD,KAAK,CAACG,OAAO,CAAC;IACpD,KAAKH,KAAK,CAACI,KAAK,IAAI,mBAAmB,CAACH,IAAI,CAACD,KAAK,CAACI,KAAK,CAAC;IACzD,KAAKJ,KAAK,CAACI,KAAK,IAAI,yCAAyC,CAACH,IAAI,CAACD,KAAK,CAACI,KAAK,CAAC;IAC/E,KAAK,eAAe,IAAIJ,KAAK;IAC7B,KAAK,SAAS,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAACK,OAAO,KAAK,UAAU;MAC5D,OAAOC,aAAM,CAACC,MAAM;IACtB;MACE,OAAOD,aAAM,CAACE,MAAM;EACxB;AACF,CAAC;;AAED;AACA;AACA;AAFAC,OAAA,CAAAV,kBAAA,GAAAA,kBAAA;AAGA,IAAMW,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAmC;EAAA,IAA/B;IAAEC,SAAS,GAAG;EAAM,CAAC,GAAAxC,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAyC,SAAA,GAAAzC,SAAA,MAAG,CAAC,CAAC;EAC3C,IAAM0C,OAAO,GAAG,CACd,8HAA8H,EAC9H,0DAA0D,CAC3D,CAAC3C,IAAI,CAAC,GAAG,CAAC;EAEX,OAAO,IAAIzB,MAAM,CAACoE,OAAO,EAAEF,SAAS,GAAGC,SAAS,GAAG,GAAG,CAAC;AACzD,CAAC;;AAED;AACA;AACA;AACO,IAAME,SAAS,GAAIC,GAAW,IAAa;EAChD,IAAMC,KAAK,GAAGN,SAAS,CAAC,CAAC;EACzB,OAAOK,GAAG,CAAChD,OAAO,CAACiD,KAAK,EAAE,EAAE,CAAC;AAC/B,CAAC;AAACP,OAAA,CAAAK,SAAA,GAAAA,SAAA;AAEF,IAAMG,iBAAiB,GAAIxC,KAAc,IAA6C;EACpF,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACvC,OAAO,CAAC,CAAC;EACX;;EAEA;EACA,IAAI,eAAe,IAAIA,KAAK,IAAIA,KAAK,CAACyC,aAAa,KAAKN,SAAS,IAAI,OAAOnC,KAAK,CAACyC,aAAa,KAAK,QAAQ,EAAE;IAC5G,OAAO;MACLC,MAAM,EAAEC,SAAS,CAAE3C,KAAK,CAACyC,aAAa,CAASC,MAAM,CAAC;MACtDE,QAAQ,EAAED,SAAS,CAAE3C,KAAK,CAACyC,aAAa,CAASG,QAAQ;IAC3D,CAAC;EACH;EAEA,IAAMF,MAAM,GAAG,QAAQ,IAAI1C,KAAK,IAAIA,KAAK,CAAC0C,MAAM,KAAKP,SAAS,GAAG;IAAEO,MAAM,EAAEC,SAAS,CAAC3C,KAAK,CAAC0C,MAAM;EAAE,CAAC,GAAG,CAAC,CAAC;EACzG,IAAME,QAAQ,GAAG,UAAU,IAAI5C,KAAK,IAAIA,KAAK,CAAC4C,QAAQ,KAAKT,SAAS,GAAG;IAAES,QAAQ,EAAED,SAAS,CAAC3C,KAAK,CAAC4C,QAAQ;EAAE,CAAC,GAAG,CAAC,CAAC;EACnH,OAAAhC,aAAA,CAAAA,aAAA,KACK8B,MAAM,GACNE,QAAQ;AAEf,CAAC;AAEM,IAAMC,2BAA2B,GACtCtB,KAKK,IACa;EAClB,IAAM;IAAEG,OAAO;IAAEC;EAAM,CAAC,GAAGJ,KAAK;EAChC,OAAAX,aAAA;IACEc,OAAO,EAAEA,OAAO,GAAGW,SAAS,CAACX,OAAO,CAAC,GAAGS,SAAS;IACjDW,KAAK,EAAEnB,KAAK,GAAGU,SAAS,CAACV,KAAK,CAAC,GAAGQ;EAAS,GACxCK,iBAAiB,CAACjB,KAAK,CAAC;AAE/B,CAAC;AAACS,OAAA,CAAAa,2BAAA,GAAAA,2BAAA;AAYK,IAAME,oBAAoB,GAAAf,OAAA,CAAAe,oBAAA,gBAAAnF,WAAA,CAAG,0BAAiC;EAAAoF,IAAA;AAAA;AAC9D,IAAMC,yBAAyB,GAAAjB,OAAA,CAAAiB,yBAAA,gBAAArF,WAAA,CAAG,+DAAsE;EAAAoF,IAAA;AAAA;AACxG,IAAME,+BAA+B,GAAAlB,OAAA,CAAAkB,+BAAA,GAAG,IAAIlF,MAAM,CAACiF,yBAAyB,EAAE,GAAG,CAAC;AAClF,IAAME,cAAc,GAAAnB,OAAA,CAAAmB,cAAA,gBAAAvF,WAAA,CAAG,+BAAoC;EAAAwF,EAAA;AAAA;AAC3D,IAAMC,iBAAiB,GAAArB,OAAA,CAAAqB,iBAAA,gBAAAzF,WAAA,CAAG,iDAAgE;EAAA6D,IAAA;EAAAzB,KAAA;AAAA;AAE1F,IAAMsD,mCAAmC,GAAIC,KAA+B,IAAK;EACtF,OAAOA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAC,CAAC;AACnB,CAAC;AAACvB,OAAA,CAAAsB,mCAAA,GAAAA,mCAAA;AAEK,IAAME,oCAAoC,GAAID,KAA+B,IAAK;EAAA,IAAAE,OAAA;EACvF,IAAMC,YAAY,GAAG,OAAO;EAC5B,IAAMC,eAAe,GAAG,IAAI3F,MAAM,KAAA4F,MAAA,CAAKF,YAAY,CAACG,MAAM,CAAE,CAAC;EAC7D,IAAMC,gBAAgB,GAAG,IAAI9F,MAAM,IAAA4F,MAAA,CAAIF,YAAY,CAACG,MAAM,MAAG,CAAC;EAC9D,IAAME,YAAY,IAAAN,OAAA,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAC,CAAC,cAAAE,OAAA,cAAAA,OAAA,GAAI,EAAE;EAErC,IAAIE,eAAe,CAACnC,IAAI,CAACuC,YAAY,CAAC,IAAID,gBAAgB,CAACtC,IAAI,CAACuC,YAAY,CAAC,EAAE;IAC7E,OAAOA,YAAY,CAACpE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClC;EAEA,OAAOoE,YAAY;AACrB,CAAC;AAAC/B,OAAA,CAAAwB,oCAAA,GAAAA,oCAAA;AAEK,IAAMQ,aAAa,GAAIC,GAAW,IAAK;EAC5C,OAAOlB,oBAAoB,CAACvB,IAAI,CAACyC,GAAG,CAAC;AACvC,CAAC;AAACjC,OAAA,CAAAgC,aAAA,GAAAA,aAAA;AAEK,IAAME,gBAAgB,GAAGA,CAACD,GAAW,EAAEjE,KAAc,KAAwB;EAAA,IAAAmE,aAAA;EAClF,IAAMZ,KAAK,GAAGU,GAAG,CAACV,KAAK,CAACR,oBAAoB,CAAC;EAC7C,IAAMC,IAAI,GAAGO,KAAK,aAALA,KAAK,gBAAAY,aAAA,GAALZ,KAAK,CAAEpE,MAAM,cAAAgF,aAAA,uBAAbA,aAAA,CAAenB,IAAI;EAEhC,IAAI,CAACA,IAAI,EAAE;IACT,OAAOb,SAAS;EAClB;EAEA,IAAM,CAACiC,OAAO,EAAE3C,IAAI,CAAC,GAAGuB,IAAI,CAACqB,KAAK,CAAC,GAAG,CAAC;EAEvC,OAAO;IACL5C,IAAI,EAAE2C,OAAO,KAAK,IAAI,GAAGE,gBAAS,CAACC,SAAS,GAAG9C,IAAI;IACnDzB,KAAK,EAAEA,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI;EAClB,CAAC;AACH,CAAC;AAACgC,OAAA,CAAAkC,gBAAA,GAAAA,gBAAA;AAEK,IAAMM,yBAAyB,GACpCC,KAAa,IAKV;EACH,IAAMC,MAAM,GAAG,EAAa;EAC5B,IAAMC,KAAK,GAAG,EAAY;EAC1B,IAAMC,QAAQ,GAAGH,KAAK,CAACI,QAAQ,CAAC3B,+BAA+B,CAAC;EAChE,IAAM4B,UAAU,GAAGL,KAAK,CACrBM,UAAU,CAAC7B,+BAA+B,EAAE,EAAE,CAAC,CAC/CmB,KAAK,CAAC,GAAG,CAAC,CACV5D,MAAM,CAACuE,OAAO,CAAC,CACfpG,MAAM,CAAC,CAACqG,GAAG,EAAEC,IAAI,KAAK;IACrB,IAAI,SAAS,CAAC1D,IAAI,CAAC0D,IAAI,CAAC,EAAE;MACxB,OAAOD,GAAG,GAAGC,IAAI;IACnB;IAEA,UAAAtB,MAAA,CAAUqB,GAAG,OAAArB,MAAA,CAAIsB,IAAI;EACvB,CAAC,EAAE,EAAE,CAAC,CACLC,IAAI,CAAC,CAAC;EAET,KAAK,IAAMC,CAAC,IAAIR,QAAQ,EAAE;IACxB,IAAMrB,KAAK,GAAG6B,CAA6B;IAC3C,IAAMpC,IAAI,GAAGM,mCAAmC,CAACC,KAAK,CAAC;IACvD,IAAMvD,KAAK,GAAGwD,oCAAoC,CAACD,KAAK,CAAC;IAEzD,IAAI,CAACP,IAAI,IAAI,CAAChD,KAAK,EAAE;MACnB;IACF;IAEA,IAAM,CAACoE,OAAO,EAAE3C,IAAI,CAAC,GAAGuB,IAAI,CAACqB,KAAK,CAAC,GAAG,CAAC;IAEvC,QAAQD,OAAO;MACb,KAAK,IAAI;QACPM,MAAM,CAAC9E,IAAI,CAAC;UAAE6B,IAAI,EAAE6C,gBAAS,CAACC,SAAS;UAAEvE;QAAM,CAAC,CAAC;QACjD;MACF,KAAK,OAAO;QACV0E,MAAM,CAAC9E,IAAI,CAAC;UAAE6B,IAAI;UAAEzB;QAAM,CAAC,CAAC;QAC5B;MACF,KAAK,MAAM;QACT2E,KAAK,CAAC/E,IAAI,CAAC;UAAEoD,IAAI,EAAEvB,IAAI;UAAE4D,GAAG,EAAErF;QAAM,CAAC,CAAC;QACtC;IACJ;EACF;EAEA,OAAO;IACL0E,MAAM;IACNC,KAAK;IACLG;EACF,CAAC;AACH,CAAC;AAAC9C,OAAA,CAAAwC,yBAAA,GAAAA,yBAAA;AAEK,IAAMc,eAAe,GAAIC,IAA6C,IAAc;EACzF,IAAMC,QAAQ,GAAGD,IAAI,CAACE,MAAM,KAAK5D,aAAM,CAACC,MAAM;EAE9C,IAAI0D,QAAQ,IAAID,IAAI,CAACG,KAAK,CAAC5G,MAAM,KAAK,CAAC,EAAE;IACvC,OAAO0G,QAAQ;EACjB;EAEA,OAAO,CAAC,CAACD,IAAI,CAACG,KAAK,CAACC,IAAI,CAAEC,IAAI,IAAKN,eAAe,CAACM,IAAI,CAAC,CAAC;AAC3D,CAAC;AAAC5D,OAAA,CAAAsD,eAAA,GAAAA,eAAA;AAEK,IAAMO,eAAe,GAAIN,IAA6C,IAAc;EACzF,OAAOA,IAAI,CAACG,KAAK,CAACI,KAAK,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,IAAIH,eAAe,CAACE,GAAG,CAAC,CAAC;AACpE,CAAC;AAAC/D,OAAA,CAAA6D,eAAA,GAAAA,eAAA;AAEK,IAAMI,QAAQ,GAAGA,CAACC,UAAsB,EAAEC,SAA6B,KAAc;EAC1F,OAAO,CAAC,CAACD,UAAU,CAACxB,MAAM,CAACiB,IAAI,CAAES,CAAC,IAAKA,CAAC,CAAC3E,IAAI,KAAK0E,SAAS,CAAC;AAC9D,CAAC;AAACnE,OAAA,CAAAiE,QAAA,GAAAA,QAAA;AAEK,IAAMI,cAAc,GAAIC,QAA0B,IAAK;EAC5D,OAAOA,QAAQ,CAACC,IAAI,CAAE7E,OAAO,IAAKA,OAAO,CAACsB,IAAI,KAAK,YAAY,IAAItB,OAAO,CAACsB,IAAI,KAAK,WAAW,CAAC;AAClG,CAAC;AAAChB,OAAA,CAAAqE,cAAA,GAAAA,cAAA;AAEK,IAAMG,oBAAoB,GAAIF,QAA0B,IAC7DA,QAAQ,CAAC1H,MAAM,CAAC,CAACqG,GAAG,EAAEvD,OAAO,KAAK;EAChC,IAAIA,OAAO,CAACsB,IAAI,KAAK,YAAY,IAAItB,OAAO,CAACsB,IAAI,KAAK,WAAW,EAAE;IACjE,OAAOiC,GAAG;EACZ;EAEA,IAAIvD,OAAO,CAACsB,IAAI,KAAK,YAAY,EAAE;IACjCiC,GAAG,CAACrF,IAAI,CAAC,CAAC8B,OAAO,CAAC,CAAC;IAEnB,OAAOuD,GAAG;EACZ;EAEA,IAAMwB,iBAAiB,GAAGxB,GAAG,CAACyB,aAAa,CAAEd,IAAI,IAAKA,IAAI,CAAC9G,MAAM,KAAK,CAAC,CAAC;EAExE,IAAI2H,iBAAiB,KAAK,CAAC,CAAC,EAAE;IAC5B,OAAOxB,GAAG;EACZ;EAEAA,GAAG,CAACwB,iBAAiB,CAAC,CAAC7G,IAAI,CAAC8B,OAAO,CAAC;EAEpC,OAAOuD,GAAG;AACZ,CAAC,EAAE,EAAwB,CAAC;AAACjD,OAAA,CAAAwE,oBAAA,GAAAA,oBAAA;AAExB,IAAMG,0BAA0B,GAAIL,QAA0B,IAAK;EACxE,IAAMM,oBAAoB,GAAGJ,oBAAoB,CAACF,QAAQ,CAAC;EAE3D,OAAOM,oBAAoB,CAACnG,MAAM,CAAEmF,IAAI,IAAKA,IAAI,CAAC9G,MAAM,KAAK,CAAC,CAAC;AACjE,CAAC;AAACkD,OAAA,CAAA2E,0BAAA,GAAAA,0BAAA;AAEK,IAAME,SAAS,GAAaC,GAAQ,IACzC,CAAC,CAACA,GAAG,KAAK,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IAAI,OAAOA,GAAG,CAACC,IAAI,KAAK,UAAU;AAAC/E,OAAA,CAAA6E,SAAA,GAAAA,SAAA;AAE7F,IAAMlE,SAAS,GAAAX,OAAA,CAAAW,SAAA,GAAG,SAAZA,SAASA,CAAI3C,KAAU;EAAA,IAAE;IAAEgH,QAAQ,GAAG,CAAC;IAAEC,SAAS,GAAG,CAAC;IAAEC;EAA2B,CAAC,GAAAxH,SAAA,CAAAZ,MAAA,QAAAY,SAAA,QAAAyC,SAAA,GAAAzC,SAAA,MAAG,CAAC,CAAC;EAAA,OACpGyH,WAAW,CACT,OAAOnH,KAAK,KAAK,QAAQ,GAAGoH,IAAI,CAACC,SAAS,CAACrH,KAAK,EAAEsH,uBAAuB,CAACN,QAAQ,EAAEE,QAAQ,CAAC,CAAC,GAAG9F,MAAM,CAACpB,KAAK,CAAC,EAC9GiH,SACF,CAAC;AAAA;AAEH,IAAMK,uBAAuB,GAAGA,CAACN,QAAgB,EAAEO,mBAAiD,KAAK;EACvG,IAAMC,OAAkB,GAAG,EAAE;EAC7B,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAA4BC,CAAS,EAAE1H,KAAc,EAAE;IAC3E,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;MAC/C,OAAOA,KAAK;IACd;IAEA,OAAOwH,OAAO,CAAC1I,MAAM,GAAG,CAAC,IAAI,CAACJ,MAAM,CAACiJ,EAAE,CAACH,OAAO,CAACI,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;MAC7DJ,OAAO,CAACK,GAAG,CAAC,CAAC;IACf;IAEA,IAAKb,QAAQ,IAAIQ,OAAO,CAAC1I,MAAM,IAAIkI,QAAQ,IAAKQ,OAAO,CAACM,QAAQ,CAAC9H,KAAK,CAAC,EAAE;MACvE,OAAOmC,SAAS;IAClB;IAEAqF,OAAO,CAAC5H,IAAI,CAACI,KAAK,CAAC;IAEnB,OAAOA,KAAK,YAAY+H,GAAG,GACvBC,0BAA0B,CAACR,OAAO,EAAExH,KAAK,CAAC,GAC1CA,KAAK,YAAYiI,GAAG,GAClBC,0BAA0B,CAACV,OAAO,EAAExH,KAAK,CAAC,GAC1CA,KAAK;EACb,CAAC;EACD,OAAOuH,mBAAmB,GAAGY,gBAAgB,CAACZ,mBAAmB,EAAEE,gBAAgB,CAAC,GAAGA,gBAAgB;AACzG,CAAC;AAED,IAAMU,gBAAgB,GAAGA,CAACC,KAA6B,EAAEC,MAA8B,KACrF,UAAUC,CAAC,EAAEC,CAAC,EAAE;EACd,OAAOF,MAAM,CAACnJ,IAAI,CAAC,IAAI,EAAEoJ,CAAC,EAAEF,KAAK,CAAClJ,IAAI,CAAC,IAAI,EAAEoJ,CAAC,EAAEC,CAAC,CAAC,CAAC;AACrD,CAAC;AAEH,IAAMP,0BAA0B,GAAGA,CAACR,OAAc,EAAEgB,GAAkB,KAAK;EACzE,OAAOjJ,KAAK,CAACkJ,IAAI,CAACD,GAAG,CAAC,CACnB/H,MAAM,CAACiI,IAAA;IAAA,IAAC,CAACJ,CAAC,CAAC,GAAAI,IAAA;IAAA,OAAK,CAAClB,OAAO,CAACM,QAAQ,CAACQ,CAAC,CAAC;EAAA,EAAC,CACrCE,GAAG,CAACG,KAAA;IAAA,IAAC,CAACL,CAAC,EAAEC,CAAC,CAAC,GAAAI,KAAA;IAAA,OAAK,CAACL,CAAC,EAAEd,OAAO,CAACM,QAAQ,CAACS,CAAC,CAAC,GAAGpG,SAAS,GAAGoG,CAAC,CAAC;EAAA,EAAC;AAC9D,CAAC;AAED,IAAML,0BAA0B,GAAGA,CAACV,OAAc,EAAElJ,GAAa,KAAK;EACpE,OAAOiB,KAAK,CAACkJ,IAAI,CAACnK,GAAG,CAAC,CAACkK,GAAG,CAAED,CAAC,IAAMf,OAAO,CAACM,QAAQ,CAACS,CAAC,CAAC,GAAGpG,SAAS,GAAGoG,CAAE,CAAC;AAC1E,CAAC;AAED,IAAMpB,WAAW,GAAGA,CAACnH,KAAa,EAAEiH,SAAiB,KACnDA,SAAS,IAAIjH,KAAK,CAAClB,MAAM,GAAGmI,SAAS,MAAArD,MAAA,CAAM5D,KAAK,CAAC4I,SAAS,CAAC,CAAC,EAAE3B,SAAS,CAAC,WAAQjH,KAAK", "ignoreList": []}