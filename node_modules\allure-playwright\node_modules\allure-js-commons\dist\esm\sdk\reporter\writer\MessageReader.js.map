{"version": 3, "file": "MessageReader.js", "names": ["attachment", "step", "parseEnvInfo", "stringifyEnvInfo", "parseJsonResult", "data", "JSON", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "MessageReader", "constructor", "_this", "_defineProperty", "tests", "groups", "attachments", "jsonMessage", "path", "type", "results", "push", "envInfo", "categories", "handleCustomMessage", "_asyncToGenerator", "stringify", "key", "Object", "keys", "content", "contentType", "encoding", "tr", "concat", "uuid", "trc"], "sources": ["../../../../../src/sdk/reporter/writer/MessageReader.ts"], "sourcesContent": ["import { attachment, step } from \"../../../facade.js\";\nimport type { TestResult, TestResultContainer } from \"../../../model.js\";\nimport type { AllureResults } from \"../../types.js\";\nimport { parseEnvInfo, stringifyEnvInfo } from \"../utils/envInfo.js\";\n\nconst parseJsonResult = <T>(data: string) => {\n  return JSON.parse(Buffer.from(data, \"base64\").toString(\"utf-8\")) as T;\n};\n\nexport class MessageReader {\n  readonly results: AllureResults = {\n    tests: [],\n    groups: [],\n    attachments: {},\n  };\n\n  handleMessage = (jsonMessage: string) => {\n    const { path, type = \"undefined\", data }: { path: string; type?: string; data: string } = JSON.parse(jsonMessage);\n\n    switch (type) {\n      case \"container\":\n        this.results.groups.push(parseJsonResult<TestResultContainer>(data));\n        return;\n      case \"result\":\n        this.results.tests.push(parseJsonResult<TestResult>(data));\n        return;\n      case \"attachment\":\n        this.results.attachments[path] = data;\n        return;\n      case \"misc\":\n        switch (path) {\n          case \"environment.properties\":\n            this.results.envInfo = parseEnvInfo(Buffer.from(data, \"base64\").toString());\n            break;\n          case \"categories.json\":\n            this.results.categories = parseJsonResult(data);\n            break;\n          default:\n            break;\n        }\n        return;\n      default:\n        this.handleCustomMessage(type, data, path);\n        return;\n    }\n  };\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  handleCustomMessage = (type: string, data: any, path: string) => {};\n\n  attachResults = async () => {\n    await step(\"allure-results\", async () => {\n      if (this.results.categories) {\n        await attachment(\"categories.json\", JSON.stringify(this.results.categories), \"application/json\");\n      }\n      if (this.results.envInfo) {\n        await attachment(\"environment.properties\", stringifyEnvInfo(this.results.envInfo), \"text/plain\");\n      }\n      if (this.results.attachments) {\n        for (const key of Object.keys(this.results.attachments)) {\n          const content = this.results.attachments[key];\n          await attachment(key, content, {\n            contentType: \"text/plain\",\n            encoding: \"base64\",\n          });\n        }\n      }\n      if (this.results.tests) {\n        for (const tr of this.results.tests) {\n          await attachment(`${tr.uuid}-result.json`, JSON.stringify(tr, null, 2), {\n            contentType: \"application/json\",\n            encoding: \"utf-8\",\n          });\n        }\n      }\n      if (this.results.groups) {\n        for (const trc of this.results.groups) {\n          await attachment(`${trc.uuid}-container.json`, JSON.stringify(trc, null, 2), {\n            contentType: \"application/json\",\n            encoding: \"utf-8\",\n          });\n        }\n      }\n    });\n  };\n}\n"], "mappings": ";;;;;AAAA,SAASA,UAAU,EAAEC,IAAI,QAAQ,oBAAoB;AAGrD,SAASC,YAAY,EAAEC,gBAAgB,QAAQ,qBAAqB;AAEpE,IAAMC,eAAe,GAAOC,IAAY,IAAK;EAC3C,OAAOC,IAAI,CAACC,KAAK,CAACC,MAAM,CAACC,IAAI,CAACJ,IAAI,EAAE,QAAQ,CAAC,CAACK,QAAQ,CAAC,OAAO,CAAC,CAAC;AAClE,CAAC;AAED,OAAO,MAAMC,aAAa,CAAC;EAAAC,YAAA;IAAA,IAAAC,KAAA;IAAAC,eAAA,kBACS;MAChCC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVC,WAAW,EAAE,CAAC;IAChB,CAAC;IAAAH,eAAA,wBAEgBI,WAAmB,IAAK;MACvC,IAAM;QAAEC,IAAI;QAAEC,IAAI,GAAG,WAAW;QAAEf;MAAoD,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACW,WAAW,CAAC;MAEjH,QAAQE,IAAI;QACV,KAAK,WAAW;UACd,IAAI,CAACC,OAAO,CAACL,MAAM,CAACM,IAAI,CAAClB,eAAe,CAAsBC,IAAI,CAAC,CAAC;UACpE;QACF,KAAK,QAAQ;UACX,IAAI,CAACgB,OAAO,CAACN,KAAK,CAACO,IAAI,CAAClB,eAAe,CAAaC,IAAI,CAAC,CAAC;UAC1D;QACF,KAAK,YAAY;UACf,IAAI,CAACgB,OAAO,CAACJ,WAAW,CAACE,IAAI,CAAC,GAAGd,IAAI;UACrC;QACF,KAAK,MAAM;UACT,QAAQc,IAAI;YACV,KAAK,wBAAwB;cAC3B,IAAI,CAACE,OAAO,CAACE,OAAO,GAAGrB,YAAY,CAACM,MAAM,CAACC,IAAI,CAACJ,IAAI,EAAE,QAAQ,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC;cAC3E;YACF,KAAK,iBAAiB;cACpB,IAAI,CAACW,OAAO,CAACG,UAAU,GAAGpB,eAAe,CAACC,IAAI,CAAC;cAC/C;YACF;cACE;UACJ;UACA;QACF;UACE,IAAI,CAACoB,mBAAmB,CAACL,IAAI,EAAEf,IAAI,EAAEc,IAAI,CAAC;UAC1C;MACJ;IACF,CAAC;IAED;IAAAL,eAAA,8BACsB,CAACM,IAAY,EAAEf,IAAS,EAAEc,IAAY,KAAK,CAAC,CAAC;IAAAL,eAAA,qCAAAY,iBAAA,CAEnD,aAAY;MAC1B,MAAMzB,IAAI,CAAC,gBAAgB,eAAAyB,iBAAA,CAAE,aAAY;QACvC,IAAIb,KAAI,CAACQ,OAAO,CAACG,UAAU,EAAE;UAC3B,MAAMxB,UAAU,CAAC,iBAAiB,EAAEM,IAAI,CAACqB,SAAS,CAACd,KAAI,CAACQ,OAAO,CAACG,UAAU,CAAC,EAAE,kBAAkB,CAAC;QAClG;QACA,IAAIX,KAAI,CAACQ,OAAO,CAACE,OAAO,EAAE;UACxB,MAAMvB,UAAU,CAAC,wBAAwB,EAAEG,gBAAgB,CAACU,KAAI,CAACQ,OAAO,CAACE,OAAO,CAAC,EAAE,YAAY,CAAC;QAClG;QACA,IAAIV,KAAI,CAACQ,OAAO,CAACJ,WAAW,EAAE;UAC5B,KAAK,IAAMW,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACjB,KAAI,CAACQ,OAAO,CAACJ,WAAW,CAAC,EAAE;YACvD,IAAMc,OAAO,GAAGlB,KAAI,CAACQ,OAAO,CAACJ,WAAW,CAACW,GAAG,CAAC;YAC7C,MAAM5B,UAAU,CAAC4B,GAAG,EAAEG,OAAO,EAAE;cAC7BC,WAAW,EAAE,YAAY;cACzBC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QACF;QACA,IAAIpB,KAAI,CAACQ,OAAO,CAACN,KAAK,EAAE;UACtB,KAAK,IAAMmB,EAAE,IAAIrB,KAAI,CAACQ,OAAO,CAACN,KAAK,EAAE;YACnC,MAAMf,UAAU,IAAAmC,MAAA,CAAID,EAAE,CAACE,IAAI,mBAAgB9B,IAAI,CAACqB,SAAS,CAACO,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;cACtEF,WAAW,EAAE,kBAAkB;cAC/BC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QACF;QACA,IAAIpB,KAAI,CAACQ,OAAO,CAACL,MAAM,EAAE;UACvB,KAAK,IAAMqB,GAAG,IAAIxB,KAAI,CAACQ,OAAO,CAACL,MAAM,EAAE;YACrC,MAAMhB,UAAU,IAAAmC,MAAA,CAAIE,GAAG,CAACD,IAAI,sBAAmB9B,IAAI,CAACqB,SAAS,CAACU,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE;cAC3EL,WAAW,EAAE,kBAAkB;cAC/BC,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QACF;MACF,CAAC,EAAC;IACJ,CAAC;EAAA;AACH", "ignoreList": []}