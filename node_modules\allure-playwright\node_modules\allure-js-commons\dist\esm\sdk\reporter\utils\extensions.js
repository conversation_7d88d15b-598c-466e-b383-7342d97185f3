/* eslint-disable */
export var EXTENSIONS_BY_TYPE = {
  "application/andrew-inset": ".ez",
  "application/applixware": ".aw",
  "application/atom+xml": ".atom",
  "application/atomcat+xml": ".atomcat",
  "application/atomsvc+xml": ".atomsvc",
  "application/bizagi-modeler": ".bpm",
  "application/cbor": ".cbor",
  "application/ccxml+xml": ".ccxml",
  "application/coreldraw": ".cdr",
  "application/cu-seeme": ".cu",
  "application/dash+xml": ".mpd",
  "application/davmount+xml": ".davmount",
  "application/dif+xml": ".dif",
  "application/dita+xml; format=map": ".ditamap",
  "application/dita+xml; format=topic": ".dita",
  "application/dita+xml; format=val": ".ditaval",
  "application/ecmascript": ".ecma",
  "application/emma+xml": ".emma",
  "application/envi.hdr": ".hdr",
  "application/epub+zip": ".epub",
  "application/fits": ".fits",
  "application/font-tdpfr": ".pfr",
  "application/gzip": ".gz",
  "application/hyperstudio": ".stk",
  "application/illustrator": ".ai",
  "application/java-archive": ".jar",
  "application/java-serialized-object": ".ser",
  "application/java-vm": ".class",
  "application/javascript": ".js",
  "application/json": ".json",
  "application/lost+xml": ".lostxml",
  "application/mac-binhex40": ".hqx",
  "application/mac-compactpro": ".cpt",
  "application/manifest+json": ".webmanifest",
  "application/marc": ".mrc",
  "application/mathematica": ".ma",
  "application/mathml+xml": ".mathml",
  "application/mbox": ".mbox",
  "application/mediaservercontrol+xml": ".mscml",
  "application/mp4": ".mp4s",
  "application/msword": ".doc",
  "application/mxf": ".mxf",
  "application/octet-stream": ".bin",
  "application/oda": ".oda",
  "application/oebps-package+xml": ".opf",
  "application/ogg": ".ogx",
  "application/onenote": ".onetmp",
  "application/onenote; format=one": ".one",
  "application/onenote; format=onetoc2": ".onetoc",
  "application/onenote; format=package": ".onepkg",
  "application/patch-ops-error+xml": ".xer",
  "application/pdf": ".pdf",
  "application/pgp-encrypted": ".pgp",
  "application/pgp-signature": ".asc",
  "application/pics-rules": ".prf",
  "application/pkcs7-mime": ".p7m",
  "application/pkcs7-signature": ".p7s",
  "application/pkcs10": ".p10",
  "application/pkix-cert": ".cer",
  "application/pkix-crl": ".crl",
  "application/pkix-pkipath": ".pkipath",
  "application/pkixcmp": ".pki",
  "application/pls+xml": ".pls",
  "application/postscript": ".ps",
  "application/prs.cww": ".cww",
  "application/rdf+xml": ".rdf",
  "application/reginfo+xml": ".rif",
  "application/relax-ng-compact-syntax": ".rnc",
  "application/resource-lists+xml": ".rl",
  "application/resource-lists-diff+xml": ".rld",
  "application/rls-services+xml": ".rs",
  "application/rsd+xml": ".rsd",
  "application/rss+xml": ".rss",
  "application/rtf": ".rtf",
  "application/sbml+xml": ".sbml",
  "application/scvp-cv-request": ".scq",
  "application/scvp-cv-response": ".scs",
  "application/scvp-vp-request": ".spq",
  "application/scvp-vp-response": ".spp",
  "application/sdp": ".sdp",
  "application/sereal": ".srl",
  "application/set-payment-initiation": ".setpay",
  "application/set-registration-initiation": ".setreg",
  "application/shf+xml": ".shf",
  "application/sldworks": ".sldprt",
  "application/smil+xml": ".smi",
  "application/sparql-query": ".rq",
  "application/sparql-results+xml": ".srx",
  "application/srgs": ".gram",
  "application/srgs+xml": ".grxml",
  "application/ssml+xml": ".ssml",
  "application/timestamped-data": ".tsd",
  "application/vnd.3gpp.pic-bw-large": ".plb",
  "application/vnd.3gpp.pic-bw-small": ".psb",
  "application/vnd.3gpp.pic-bw-var": ".pvb",
  "application/vnd.3gpp2.tcap": ".tcap",
  "application/vnd.3m.post-it-notes": ".pwn",
  "application/vnd.accpac.simply.aso": ".aso",
  "application/vnd.accpac.simply.imp": ".imp",
  "application/vnd.acucobol": ".acu",
  "application/vnd.acucorp": ".atc",
  "application/vnd.adobe.aftereffects.project": ".aep",
  "application/vnd.adobe.aftereffects.template": ".aet",
  "application/vnd.adobe.air-application-installer-package+zip": ".air",
  "application/vnd.adobe.indesign-idml-package": ".idml",
  "application/vnd.adobe.xdp+xml": ".xdp",
  "application/vnd.adobe.xfdf": ".xfdf",
  "application/vnd.airzip.filesecure.azf": ".azf",
  "application/vnd.airzip.filesecure.azs": ".azs",
  "application/vnd.allure.image.diff": ".imagediff",
  "application/vnd.allure.metadata+json": ".metadata",
  "application/vnd.amazon.ebook": ".azw",
  "application/vnd.americandynamics.acc": ".acc",
  "application/vnd.amiga.ami": ".ami",
  "application/vnd.android.package-archive": ".apk",
  "application/vnd.anser-web-certificate-issue-initiation": ".cii",
  "application/vnd.anser-web-funds-transfer-initiation": ".fti",
  "application/vnd.antix.game-component": ".atx",
  "application/vnd.apple.installer+xml": ".mpkg",
  "application/vnd.apple.keynote": ".key",
  "application/vnd.apple.mpegurl": ".m3u8",
  "application/vnd.apple.numbers": ".numbers",
  "application/vnd.apple.pages": ".pages",
  "application/vnd.arastra.swi": ".swi",
  "application/vnd.blueice.multipass": ".mpm",
  "application/vnd.bmi": ".bmi",
  "application/vnd.businessobjects": ".rep",
  "application/vnd.chemdraw+xml": ".cdxml",
  "application/vnd.chipnuts.karaoke-mmd": ".mmd",
  "application/vnd.cinderella": ".cdy",
  "application/vnd.claymore": ".cla",
  "application/vnd.clonk.c4group": ".c4g",
  "application/vnd.commonspace": ".csp",
  "application/vnd.contact.cmsg": ".cdbcmsg",
  "application/vnd.cosmocaller": ".cmc",
  "application/vnd.crick.clicker": ".clkx",
  "application/vnd.crick.clicker.keyboard": ".clkk",
  "application/vnd.crick.clicker.palette": ".clkp",
  "application/vnd.crick.clicker.template": ".clkt",
  "application/vnd.crick.clicker.wordbank": ".clkw",
  "application/vnd.criticaltools.wbs+xml": ".wbs",
  "application/vnd.ctc-posml": ".pml",
  "application/vnd.cups-ppd": ".ppd",
  "application/vnd.curl.car": ".car",
  "application/vnd.curl.pcurl": ".pcurl",
  "application/vnd.data-vision.rdz": ".rdz",
  "application/vnd.denovo.fcselayout-link": ".fe_launch",
  "application/vnd.dna": ".dna",
  "application/vnd.dolby.mlp": ".mlp",
  "application/vnd.dpgraph": ".dpg",
  "application/vnd.dreamfactory": ".dfac",
  "application/vnd.dynageo": ".geo",
  "application/vnd.ecowin.chart": ".mag",
  "application/vnd.enliven": ".nml",
  "application/vnd.epson.esf": ".esf",
  "application/vnd.epson.msf": ".msf",
  "application/vnd.epson.quickanime": ".qam",
  "application/vnd.epson.salt": ".slt",
  "application/vnd.epson.ssf": ".ssf",
  "application/vnd.eszigno3+xml": ".es3",
  "application/vnd.etsi.asic-e+zip": ".asice",
  "application/vnd.etsi.asic-s+zip": ".asics",
  "application/vnd.ezpix-album": ".ez2",
  "application/vnd.ezpix-package": ".ez3",
  "application/vnd.fdf": ".fdf",
  "application/vnd.fdsn.mseed": ".mseed",
  "application/vnd.fdsn.seed": ".seed",
  "application/vnd.flographit": ".gph",
  "application/vnd.fluxtime.clip": ".ftc",
  "application/vnd.framemaker": ".fm",
  "application/vnd.frogans.fnc": ".fnc",
  "application/vnd.frogans.ltf": ".ltf",
  "application/vnd.fsc.weblaunch": ".fsc",
  "application/vnd.fujitsu.oasys": ".oas",
  "application/vnd.fujitsu.oasys2": ".oa2",
  "application/vnd.fujitsu.oasys3": ".oa3",
  "application/vnd.fujitsu.oasysgp": ".fg5",
  "application/vnd.fujitsu.oasysprs": ".bh2",
  "application/vnd.fujixerox.ddd": ".ddd",
  "application/vnd.fujixerox.docuworks": ".xdw",
  "application/vnd.fujixerox.docuworks.binder": ".xbd",
  "application/vnd.fuzzysheet": ".fzs",
  "application/vnd.genomatix.tuxedo": ".txd",
  "application/vnd.geogebra.file": ".ggb",
  "application/vnd.geogebra.tool": ".ggt",
  "application/vnd.geometry-explorer": ".gex",
  "application/vnd.gmx": ".gmx",
  "application/vnd.google-earth.kml+xml": ".kml",
  "application/vnd.google-earth.kmz": ".kmz",
  "application/vnd.grafeq": ".gqf",
  "application/vnd.groove-account": ".gac",
  "application/vnd.groove-help": ".ghf",
  "application/vnd.groove-identity-message": ".gim",
  "application/vnd.groove-injector": ".grv",
  "application/vnd.groove-tool-message": ".gtm",
  "application/vnd.groove-tool-template": ".tpl",
  "application/vnd.groove-vcard": ".vcg",
  "application/vnd.handheld-entertainment+xml": ".zmm",
  "application/vnd.hbci": ".hbci",
  "application/vnd.hhe.lesson-player": ".les",
  "application/vnd.hp-hpgl": ".hpgl",
  "application/vnd.hp-hpid": ".hpid",
  "application/vnd.hp-hps": ".hps",
  "application/vnd.hp-jlyt": ".jlt",
  "application/vnd.hp-pcl": ".pcl",
  "application/vnd.hp-pclxl": ".pclxl",
  "application/vnd.hydrostatix.sof-data": ".sfd-hdstx",
  "application/vnd.hzn-3d-crossword": ".x3d",
  "application/vnd.ibm.minipay": ".mpy",
  "application/vnd.ibm.modcap": ".afp",
  "application/vnd.ibm.rights-management": ".irm",
  "application/vnd.ibm.secure-container": ".sc",
  "application/vnd.iccprofile": ".icc",
  "application/vnd.igloader": ".igl",
  "application/vnd.immervision-ivp": ".ivp",
  "application/vnd.immervision-ivu": ".ivu",
  "application/vnd.intercon.formnet": ".xpw",
  "application/vnd.intu.qbo": ".qbo",
  "application/vnd.intu.qfx": ".qfx",
  "application/vnd.iptc.g2.newsmessage+xml": ".nar",
  "application/vnd.ipunplugged.rcprofile": ".rcprofile",
  "application/vnd.irepository.package+xml": ".irp",
  "application/vnd.is-xpr": ".xpr",
  "application/vnd.jam": ".jam",
  "application/vnd.java.hprof": ".hprof",
  "application/vnd.java.hprof.text": ".hprof.txt",
  "application/vnd.jcp.javame.midlet-rms": ".rms",
  "application/vnd.jisp": ".jisp",
  "application/vnd.joost.joda-archive": ".joda",
  "application/vnd.kahootz": ".ktz",
  "application/vnd.kde.karbon": ".karbon",
  "application/vnd.kde.kchart": ".chrt",
  "application/vnd.kde.kformula": ".kfo",
  "application/vnd.kde.kivio": ".flw",
  "application/vnd.kde.kontour": ".kon",
  "application/vnd.kde.kpresenter": ".kpr",
  "application/vnd.kde.kspread": ".ksp",
  "application/vnd.kde.kword": ".kwd",
  "application/vnd.kenameaapp": ".htke",
  "application/vnd.kidspiration": ".kia",
  "application/vnd.kinar": ".kne",
  "application/vnd.koan": ".skp",
  "application/vnd.kodak-descriptor": ".sse",
  "application/vnd.llamagraphics.life-balance.desktop": ".lbd",
  "application/vnd.llamagraphics.life-balance.exchange+xml": ".lbe",
  "application/vnd.lotus-1-2-3": ".wk1",
  "application/vnd.lotus-1-2-3; version=2": ".wk1",
  "application/vnd.lotus-1-2-3; version=3": ".wk3",
  "application/vnd.lotus-1-2-3; version=4": ".wk4",
  "application/vnd.lotus-1-2-3; version=97+9.x": ".123",
  "application/vnd.lotus-approach": ".apr",
  "application/vnd.lotus-freelance": ".pre",
  "application/vnd.lotus-notes": ".nsf",
  "application/vnd.lotus-organizer": ".org",
  "application/vnd.lotus-wordpro": ".lwp",
  "application/vnd.macports.portpkg": ".portpkg",
  "application/vnd.mcd": ".mcd",
  "application/vnd.medcalcdata": ".mc1",
  "application/vnd.mediastation.cdkey": ".cdkey",
  "application/vnd.mfer": ".mwf",
  "application/vnd.mfmp": ".mfm",
  "application/vnd.micrografx.flo": ".flo",
  "application/vnd.micrografx.igx": ".igx",
  "application/vnd.mif": ".mif",
  "application/vnd.mindjet.mindmanager": ".mmp",
  "application/vnd.mobius.daf": ".daf",
  "application/vnd.mobius.dis": ".dis",
  "application/vnd.mobius.mbk": ".mbk",
  "application/vnd.mobius.mqy": ".mqy",
  "application/vnd.mobius.msl": ".msl",
  "application/vnd.mobius.plc": ".plc",
  "application/vnd.mobius.txf": ".txf",
  "application/vnd.mophun.application": ".mpn",
  "application/vnd.mophun.certificate": ".mpc",
  "application/vnd.mozilla.xul+xml": ".xul",
  "application/vnd.ms-artgalry": ".cil",
  "application/vnd.ms-cab-compressed": ".cab",
  "application/vnd.ms-excel": ".xls",
  "application/vnd.ms-excel.addin.macroenabled.12": ".xlam",
  "application/vnd.ms-excel.sheet.binary.macroenabled.12": ".xlsb",
  "application/vnd.ms-excel.sheet.macroenabled.12": ".xlsm",
  "application/vnd.ms-excel.template.macroenabled.12": ".xltm",
  "application/vnd.ms-fontobject": ".eot",
  "application/vnd.ms-htmlhelp": ".chm",
  "application/vnd.ms-ims": ".ims",
  "application/vnd.ms-lrm": ".lrm",
  "application/vnd.ms-outlook": ".msg",
  "application/vnd.ms-outlook-pst": ".pst",
  "application/vnd.ms-pki.seccat": ".cat",
  "application/vnd.ms-pki.stl": ".stl",
  "application/vnd.ms-powerpoint": ".ppt",
  "application/vnd.ms-powerpoint.addin.macroenabled.12": ".ppam",
  "application/vnd.ms-powerpoint.presentation.macroenabled.12": ".pptm",
  "application/vnd.ms-powerpoint.slide.macroenabled.12": ".sldm",
  "application/vnd.ms-powerpoint.slideshow.macroenabled.12": ".ppsm",
  "application/vnd.ms-powerpoint.template.macroenabled.12": ".potm",
  "application/vnd.ms-project": ".mpp",
  "application/vnd.ms-visio.drawing": ".vsdx",
  "application/vnd.ms-visio.drawing.macroenabled.12": ".vsdm",
  "application/vnd.ms-visio.stencil": ".vssx",
  "application/vnd.ms-visio.stencil.macroenabled.12": ".vssm",
  "application/vnd.ms-visio.template": ".vstx",
  "application/vnd.ms-visio.template.macroenabled.12": ".vstm",
  "application/vnd.ms-word.document.macroenabled.12": ".docm",
  "application/vnd.ms-word.template.macroenabled.12": ".dotm",
  "application/vnd.ms-works": ".wps",
  "application/vnd.ms-wpl": ".wpl",
  "application/vnd.ms-xpsdocument": ".xps",
  "application/vnd.mseq": ".mseq",
  "application/vnd.musician": ".mus",
  "application/vnd.muvee.style": ".msty",
  "application/vnd.neurolanguage.nlu": ".nlu",
  "application/vnd.noblenet-directory": ".nnd",
  "application/vnd.noblenet-sealer": ".nns",
  "application/vnd.noblenet-web": ".nnw",
  "application/vnd.nokia.n-gage.data": ".ngdat",
  "application/vnd.nokia.n-gage.symbian.install": ".n-gage",
  "application/vnd.nokia.radio-preset": ".rpst",
  "application/vnd.nokia.radio-presets": ".rpss",
  "application/vnd.novadigm.edm": ".edm",
  "application/vnd.novadigm.edx": ".edx",
  "application/vnd.novadigm.ext": ".ext",
  "application/vnd.oasis.opendocument.base": ".odb",
  "application/vnd.oasis.opendocument.chart": ".odc",
  "application/vnd.oasis.opendocument.chart-template": ".otc",
  "application/vnd.oasis.opendocument.flat.presentation": ".fodp",
  "application/vnd.oasis.opendocument.flat.spreadsheet": ".fods",
  "application/vnd.oasis.opendocument.flat.text": ".fodt",
  "application/vnd.oasis.opendocument.formula": ".odf",
  "application/vnd.oasis.opendocument.formula-template": ".odft",
  "application/vnd.oasis.opendocument.graphics": ".odg",
  "application/vnd.oasis.opendocument.graphics-template": ".otg",
  "application/vnd.oasis.opendocument.image": ".odi",
  "application/vnd.oasis.opendocument.image-template": ".oti",
  "application/vnd.oasis.opendocument.presentation": ".odp",
  "application/vnd.oasis.opendocument.presentation-template": ".otp",
  "application/vnd.oasis.opendocument.spreadsheet": ".ods",
  "application/vnd.oasis.opendocument.spreadsheet-template": ".ots",
  "application/vnd.oasis.opendocument.text": ".odt",
  "application/vnd.oasis.opendocument.text-master": ".otm",
  "application/vnd.oasis.opendocument.text-template": ".ott",
  "application/vnd.oasis.opendocument.text-web": ".oth",
  "application/vnd.olpc-sugar": ".xo",
  "application/vnd.oma.dd2+xml": ".dd2",
  "application/vnd.openofficeorg.autotext": ".bau",
  "application/vnd.openofficeorg.extension": ".oxt",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation": ".pptx",
  "application/vnd.openxmlformats-officedocument.presentationml.slide": ".sldx",
  "application/vnd.openxmlformats-officedocument.presentationml.slideshow": ".ppsx",
  "application/vnd.openxmlformats-officedocument.presentationml.template": ".potx",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": ".xlsx",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.template": ".xltx",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": ".docx",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.template": ".dotx",
  "application/vnd.osgi.dp": ".dp",
  "application/vnd.palm": ".pqa",
  "application/vnd.pg.format": ".str",
  "application/vnd.pg.osasli": ".ei6",
  "application/vnd.picsel": ".efif",
  "application/vnd.pocketlearn": ".plf",
  "application/vnd.powerbuilder6": ".pbd",
  "application/vnd.previewsystems.box": ".box",
  "application/vnd.proteus.magazine": ".mgz",
  "application/vnd.publishare-delta-tree": ".qps",
  "application/vnd.pvi.ptid1": ".ptid",
  "application/vnd.quark.quarkxpress": ".qxd",
  "application/vnd.recordare.musicxml": ".mxl",
  "application/vnd.recordare.musicxml+xml": ".musicxml",
  "application/vnd.rim.cod": ".cod",
  "application/vnd.rn-realmedia": ".rm",
  "application/vnd.route66.link66+xml": ".link66",
  "application/vnd.seemail": ".see",
  "application/vnd.sema": ".sema",
  "application/vnd.semd": ".semd",
  "application/vnd.semf": ".semf",
  "application/vnd.shana.informed.formdata": ".ifm",
  "application/vnd.shana.informed.formtemplate": ".itp",
  "application/vnd.shana.informed.interchange": ".iif",
  "application/vnd.shana.informed.package": ".ipk",
  "application/vnd.simtech-mindmapper": ".twd",
  "application/vnd.smaf": ".mmf",
  "application/vnd.smart.teacher": ".teacher",
  "application/vnd.solent.sdkm+xml": ".sdkm",
  "application/vnd.spotfire.dxp": ".dxp",
  "application/vnd.spotfire.sfs": ".sfs",
  "application/vnd.stardivision.calc": ".sdc",
  "application/vnd.stardivision.draw": ".sda",
  "application/vnd.stardivision.impress": ".sdd",
  "application/vnd.stardivision.math": ".smf",
  "application/vnd.stardivision.writer": ".sdw",
  "application/vnd.stardivision.writer-global": ".sgl",
  "application/vnd.sun.xml.calc": ".sxc",
  "application/vnd.sun.xml.calc.template": ".stc",
  "application/vnd.sun.xml.draw": ".sxd",
  "application/vnd.sun.xml.draw.template": ".std",
  "application/vnd.sun.xml.impress": ".sxi",
  "application/vnd.sun.xml.impress.template": ".sti",
  "application/vnd.sun.xml.math": ".sxm",
  "application/vnd.sun.xml.writer": ".sxw",
  "application/vnd.sun.xml.writer.global": ".sxg",
  "application/vnd.sun.xml.writer.template": ".stw",
  "application/vnd.sus-calendar": ".sus",
  "application/vnd.svd": ".svd",
  "application/vnd.symbian.install": ".sis",
  "application/vnd.syncml+xml": ".xsm",
  "application/vnd.syncml.dm+wbxml": ".bdm",
  "application/vnd.syncml.dm+xml": ".xdm",
  "application/vnd.tao.intent-module-archive": ".tao",
  "application/vnd.tcpdump.pcap": ".pcap",
  "application/vnd.tmobile-livetv": ".tmo",
  "application/vnd.trid.tpt": ".tpt",
  "application/vnd.triscape.mxs": ".mxs",
  "application/vnd.trueapp": ".tra",
  "application/vnd.ufdl": ".ufd",
  "application/vnd.uiq.theme": ".utz",
  "application/vnd.umajin": ".umj",
  "application/vnd.unity": ".unityweb",
  "application/vnd.uoml+xml": ".uoml",
  "application/vnd.vcx": ".vcx",
  "application/vnd.visio": ".vsd",
  "application/vnd.visionary": ".vis",
  "application/vnd.vsf": ".vsf",
  "application/vnd.wap.wbxml": ".wbxml",
  "application/vnd.wap.wmlc": ".wmlc",
  "application/vnd.wap.wmlscriptc": ".wmlsc",
  "application/vnd.webturbo": ".wtb",
  "application/vnd.wolfram.wl": ".wl",
  "application/vnd.wordperfect": ".wpd",
  "application/vnd.wqd": ".wqd",
  "application/vnd.wt.stf": ".stf",
  "application/vnd.xara": ".xar",
  "application/vnd.xfdl": ".xfdl",
  "application/vnd.yamaha.hv-dic": ".hvd",
  "application/vnd.yamaha.hv-script": ".hvs",
  "application/vnd.yamaha.hv-voice": ".hvp",
  "application/vnd.yamaha.openscoreformat": ".osf",
  "application/vnd.yamaha.openscoreformat.osfpvg+xml": ".osfpvg",
  "application/vnd.yamaha.smaf-audio": ".saf",
  "application/vnd.yamaha.smaf-phrase": ".spf",
  "application/vnd.yellowriver-custom-menu": ".cmp",
  "application/vnd.zul": ".zir",
  "application/vnd.zzazz.deck+xml": ".zaz",
  "application/voicexml+xml": ".vxml",
  "application/warc": ".warc",
  "application/wasm": ".wasm",
  "application/winhlp": ".hlp",
  "application/wsdl+xml": ".wsdl",
  "application/wspolicy+xml": ".wspolicy",
  "application/x-7z-compressed": ".7z",
  "application/x-abiword": ".abw",
  "application/x-ace-compressed": ".ace",
  "application/x-adobe-indesign": ".indd",
  "application/x-adobe-indesign-interchange": ".inx",
  "application/x-apple-diskimage": ".dmg",
  "application/x-appleworks": ".cwk",
  "application/x-archive": ".ar",
  "application/x-arj": ".arj",
  "application/x-authorware-bin": ".aab",
  "application/x-authorware-map": ".aam",
  "application/x-authorware-seg": ".aas",
  "application/x-axcrypt": ".axx",
  "application/x-bat": ".bat",
  "application/x-bcpio": ".bcpio",
  "application/x-bibtex-text-file": ".bib",
  "application/x-bittorrent": ".torrent",
  "application/x-brotli": ".br",
  "application/x-bzip": ".bz",
  "application/x-bzip2": ".bz2",
  "application/x-cdlink": ".vcd",
  "application/x-chat": ".chat",
  "application/x-chess-pgn": ".pgn",
  "application/x-chrome-package": ".crx",
  "application/x-compress": ".z",
  "application/x-corelpresentations": ".shw",
  "application/x-cpio": ".cpio",
  "application/x-csh": ".csh",
  "application/x-dbf": ".dbf",
  "application/x-debian-package": ".deb",
  "application/x-dex": ".dex",
  "application/x-director": ".dir",
  "application/x-doom": ".wad",
  "application/x-dosexec": ".exe",
  "application/x-dtbncx+xml": ".ncx",
  "application/x-dtbook+xml": ".dtb",
  "application/x-dtbresource+xml": ".res",
  "application/x-dvi": ".dvi",
  "application/x-elc": ".elc",
  "application/x-endnote-refer": ".enw",
  "application/x-erdas-hfa": ".hfa",
  "application/x-esri-layer": ".lyr",
  "application/x-fictionbook+xml": ".fb2",
  "application/x-filemaker": ".fp7",
  "application/x-font-adobe-metric": ".afm",
  "application/x-font-bdf": ".bdf",
  "application/x-font-ghostscript": ".gsf",
  "application/x-font-linux-psf": ".psf",
  "application/x-font-otf": ".otf",
  "application/x-font-pcf": ".pcf",
  "application/x-font-printer-metric": ".pfm",
  "application/x-font-snf": ".snf",
  "application/x-font-ttf": ".ttf",
  "application/x-font-type1": ".pfa",
  "application/x-futuresplash": ".spl",
  "application/x-gnucash": ".gnucash",
  "application/x-gnumeric": ".gnumeric",
  "application/x-grib": ".grb",
  "application/x-gtar": ".gtar",
  "application/x-hdf": ".hdf",
  "application/x-ibooks+zip": ".ibooks",
  "application/x-internet-archive": ".arc",
  "application/x-iso9660-image": ".iso",
  "application/x-itunes-ipa": ".ipa",
  "application/x-java-jnilib": ".jnilib",
  "application/x-java-jnlp-file": ".jnlp",
  "application/x-java-pack200": ".pack",
  "application/x-killustrator": ".kil",
  "application/x-latex": ".latex",
  "application/x-lz4": ".lz4",
  "application/x-lzip": ".lz",
  "application/x-lzma": ".lzma",
  "application/x-matlab-data": ".mat",
  "application/x-memgraph": ".memgraph",
  "application/x-mobipocket-ebook": ".prc",
  "application/x-ms-application": ".application",
  "application/x-ms-asx": ".asx",
  "application/x-ms-installer": ".msi",
  "application/x-ms-wmd": ".wmd",
  "application/x-ms-wmz": ".wmz",
  "application/x-ms-xbap": ".xbap",
  "application/x-msaccess": ".mdb",
  "application/x-msbinder": ".obd",
  "application/x-mscardfile": ".crd",
  "application/x-msclip": ".clp",
  "application/x-msdownload": ".dll",
  "application/x-msmediaview": ".mvb",
  "application/x-msmoney": ".mny",
  "application/x-mspublisher": ".pub",
  "application/x-msschedule": ".scd",
  "application/x-msterminal": ".trm",
  "application/x-mswrite": ".wri",
  "application/x-mysql-misam-compressed-index": ".MYI",
  "application/x-mysql-misam-data": ".MYD",
  "application/x-nesrom": ".nes",
  "application/x-netcdf": ".nc",
  "application/x-parquet": ".parquet",
  "application/x-pkcs7-certificates": ".p7b",
  "application/x-pkcs7-certreqresp": ".p7r",
  "application/x-pkcs12": ".p12",
  "application/x-project": ".mpx",
  "application/x-prt": ".prt",
  "application/x-quattro-pro": ".wq1",
  "application/x-quattro-pro; version=1+5": ".wb1",
  "application/x-quattro-pro; version=1-4": ".wq1",
  "application/x-quattro-pro; version=5": ".wq2",
  "application/x-quattro-pro; version=6": ".wb2",
  "application/x-rar-compressed": ".rar",
  "application/x-roxio-toast": ".toast",
  "application/x-rpm": ".rpm",
  "application/x-sas": ".sas",
  "application/x-sas-access": ".sa7",
  "application/x-sas-audit": ".st7",
  "application/x-sas-backup": ".sas7bbak",
  "application/x-sas-catalog": ".sc7",
  "application/x-sas-data": ".sd7",
  "application/x-sas-data-index": ".si7",
  "application/x-sas-data-v6": ".sd2",
  "application/x-sas-dmdb": ".s7m",
  "application/x-sas-fdb": ".sf7",
  "application/x-sas-itemstor": ".sr7",
  "application/x-sas-mddb": ".sm7",
  "application/x-sas-program-data": ".ss7",
  "application/x-sas-putility": ".sp7",
  "application/x-sas-transport": ".stx",
  "application/x-sas-utility": ".su7",
  "application/x-sas-view": ".sv7",
  "application/x-sas-xport": ".xpt",
  "application/x-sfdu": ".sfdu",
  "application/x-sh": ".sh",
  "application/x-shapefile": ".shp",
  "application/x-shar": ".shar",
  "application/x-shockwave-flash": ".swf",
  "application/x-silverlight-app": ".xap",
  "application/x-snappy-framed": ".sz",
  "application/x-staroffice-template": ".vor",
  "application/x-stata-do": ".do",
  "application/x-stata-dta": ".dta",
  "application/x-stuffit": ".sit",
  "application/x-stuffitx": ".sitx",
  "application/x-sv4cpio": ".sv4cpio",
  "application/x-sv4crc": ".sv4crc",
  "application/x-tar": ".tar",
  "application/x-tex": ".tex",
  "application/x-tex-tfm": ".tfm",
  "application/x-texinfo": ".texinfo",
  "application/x-tika-java-enterprise-archive": ".ear",
  "application/x-tika-java-web-archive": ".war",
  "application/x-tika-msworks-spreadsheet": ".xlr",
  "application/x-tmx": ".tmx",
  "application/x-uc2-compressed": ".uc2",
  "application/x-ustar": ".ustar",
  "application/x-vmdk": ".vmdk",
  "application/x-wais-source": ".src",
  "application/x-webarchive": ".webarchive",
  "application/x-x509-cert": ".crt",
  "application/x-x509-cert; format=der": ".der",
  "application/x-x509-cert; format=pem": ".pem",
  "application/x-xfig": ".fig",
  "application/x-xliff+xml": ".xlf",
  "application/x-xliff+zip": ".xlz",
  "application/x-xmind": ".xmind",
  "application/x-xpinstall": ".xpi",
  "application/x-xz": ".xz",
  "application/x-zoo": ".zoo",
  "application/xenc+xml": ".xenc",
  "application/xhtml+xml": ".xhtml",
  "application/xml": ".xml",
  "application/xml-dtd": ".dtd",
  "application/xop+xml": ".xop",
  "application/xquery": ".xq",
  "application/xslfo+xml": ".xslfo",
  "application/xslt+xml": ".xslt",
  "application/xspf+xml": ".xspf",
  "application/xv+xml": ".mxml",
  "application/zip": ".zip",
  "application/vnd.allure.playwright-trace": ".zip",
  "application/zstd": ".zst",
  "audio/ac3": ".ac3",
  "audio/adpcm": ".adp",
  "audio/amr": ".amr",
  "audio/basic": ".au",
  "audio/midi": ".mid",
  "audio/mp4": ".mp4a",
  "audio/mpeg": ".mpga",
  "audio/ogg": ".oga",
  "audio/opus": ".opus",
  "audio/speex": ".spx",
  "audio/vnd.adobe.soundbooth": ".asnd",
  "audio/vnd.digital-winds": ".eol",
  "audio/vnd.dts": ".dts",
  "audio/vnd.dts.hd": ".dtshd",
  "audio/vnd.lucent.voice": ".lvp",
  "audio/vnd.ms-playready.media.pya": ".pya",
  "audio/vnd.nuera.ecelp4800": ".ecelp4800",
  "audio/vnd.nuera.ecelp7470": ".ecelp7470",
  "audio/vnd.nuera.ecelp9600": ".ecelp9600",
  "audio/vnd.wave": ".wav",
  "audio/vorbis": ".ogg",
  "audio/x-aac": ".aac",
  "audio/x-aiff": ".aif",
  "audio/x-caf": ".caf",
  "audio/x-flac": ".flac",
  "audio/x-matroska": ".mka",
  "audio/x-mod": ".mod",
  "audio/x-mpegurl": ".m3u",
  "audio/x-ms-wax": ".wax",
  "audio/x-ms-wma": ".wma",
  "audio/x-pn-realaudio": ".ram",
  "audio/x-pn-realaudio-plugin": ".rmp",
  "chemical/x-cdx": ".cdx",
  "chemical/x-cif": ".cif",
  "chemical/x-cmdf": ".cmdf",
  "chemical/x-cml": ".cml",
  "chemical/x-csml": ".csml",
  "chemical/x-pdb": ".pdb",
  "chemical/x-xyz": ".xyz",
  "image/aces": ".exr",
  "image/avif": ".avif",
  "image/bmp": ".bmp",
  "image/cgm": ".cgm",
  "image/emf": ".emf",
  "image/g3fax": ".g3",
  "image/gif": ".gif",
  "image/heic": ".heic",
  "image/heif": ".heif",
  "image/icns": ".icns",
  "image/ief": ".ief",
  "image/jp2": ".jp2",
  "image/jpeg": ".jpg",
  "image/jpm": ".jpm",
  "image/jpx": ".jpf",
  "image/jxl": ".jxl",
  "image/nitf": ".ntf",
  "image/png": ".png",
  "image/prs.btif": ".btif",
  "image/svg+xml": ".svg",
  "image/tiff": ".tiff",
  "image/vnd.adobe.photoshop": ".psd",
  "image/vnd.adobe.premiere": ".ppj",
  "image/vnd.dgn": ".dgn",
  "image/vnd.djvu": ".djvu",
  "image/vnd.dwg": ".dwg",
  "image/vnd.dxb": ".dxb",
  "image/vnd.dxf": ".dxf",
  "image/vnd.fastbidsheet": ".fbs",
  "image/vnd.fpx": ".fpx",
  "image/vnd.fst": ".fst",
  "image/vnd.fujixerox.edmics-mmr": ".mmr",
  "image/vnd.fujixerox.edmics-rlc": ".rlc",
  "image/vnd.microsoft.icon": ".ico",
  "image/vnd.ms-modi": ".mdi",
  "image/vnd.net-fpx": ".npx",
  "image/vnd.wap.wbmp": ".wbmp",
  "image/vnd.xiff": ".xif",
  "image/vnd.zbrush.dcx": ".dcx",
  "image/vnd.zbrush.pcx": ".pcx",
  "image/webp": ".webp",
  "image/wmf": ".wmf",
  "image/x-bpg": ".bpg",
  "image/x-cmu-raster": ".ras",
  "image/x-cmx": ".cmx",
  "image/x-dpx": ".dpx",
  "image/x-emf-compressed": ".emz",
  "image/x-freehand": ".fh",
  "image/x-jbig2": ".jb2",
  "image/x-jp2-codestream": ".j2c",
  "image/x-pict": ".pic",
  "image/x-portable-anymap": ".pnm",
  "image/x-portable-bitmap": ".pbm",
  "image/x-portable-graymap": ".pgm",
  "image/x-portable-pixmap": ".ppm",
  "image/x-raw-adobe": ".dng",
  "image/x-raw-canon": ".crw",
  "image/x-raw-casio": ".bay",
  "image/x-raw-epson": ".erf",
  "image/x-raw-fuji": ".raf",
  "image/x-raw-hasselblad": ".3fr",
  "image/x-raw-imacon": ".fff",
  "image/x-raw-kodak": ".k25",
  "image/x-raw-leaf": ".mos",
  "image/x-raw-logitech": ".pxn",
  "image/x-raw-mamiya": ".mef",
  "image/x-raw-minolta": ".mrw",
  "image/x-raw-nikon": ".nef",
  "image/x-raw-olympus": ".orf",
  "image/x-raw-panasonic": ".raw",
  "image/x-raw-pentax": ".ptx",
  "image/x-raw-phaseone": ".iiq",
  "image/x-raw-rawzor": ".rwz",
  "image/x-raw-red": ".r3d",
  "image/x-raw-sigma": ".x3f",
  "image/x-raw-sony": ".arw",
  "image/x-rgb": ".rgb",
  "image/x-tga": ".tga",
  "image/x-xbitmap": ".xbm",
  "image/x-xcf": ".xcf",
  "image/x-xpixmap": ".xpm",
  "image/x-xwindowdump": ".xwd",
  "message/rfc822": ".eml",
  "message/x-emlx": ".emlx",
  "model/e57": ".e57",
  "model/iges": ".igs",
  "model/mesh": ".msh",
  "model/vnd.dwf": ".dwf",
  "model/vnd.dwfx+xps": ".dwfx",
  "model/vnd.gdl": ".gdl",
  "model/vnd.gtw": ".gtw",
  "model/vnd.mts": ".mts",
  "model/vnd.vtu": ".vtu",
  "model/vrml": ".wrl",
  "multipart/related": ".mht",
  "text/asp": ".asp",
  "text/aspdotnet": ".aspx",
  "text/calendar": ".ics",
  "text/css": ".css",
  "text/csv": ".csv",
  "text/html": ".html",
  "text/iso19139+xml": ".iso19139",
  "text/plain": ".txt",
  "text/prs.lines.tag": ".dsc",
  "text/richtext": ".rtx",
  "text/sgml": ".sgml",
  "text/tab-separated-values": ".tsv",
  "text/troff": ".t",
  "text/uri-list": ".uri",
  "text/vnd.curl": ".curl",
  "text/vnd.curl.dcurl": ".dcurl",
  "text/vnd.curl.mcurl": ".mcurl",
  "text/vnd.curl.scurl": ".scurl",
  "text/vnd.fly": ".fly",
  "text/vnd.fmi.flexstor": ".flx",
  "text/vnd.graphviz": ".gv",
  "text/vnd.in3d.3dml": ".3dml",
  "text/vnd.in3d.spot": ".spot",
  "text/vnd.iptc.anpa": ".anpa",
  "text/vnd.sun.j2me.app-descriptor": ".jad",
  "text/vnd.wap.wml": ".wml",
  "text/vnd.wap.wmlscript": ".wmls",
  "text/vtt": ".vtt",
  "text/x-actionscript": ".as",
  "text/x-ada": ".ada",
  "text/x-applescript": ".applescript",
  "text/x-asciidoc": ".asciidoc",
  "text/x-aspectj": ".aj",
  "text/x-assembly": ".s",
  "text/x-awk": ".awk",
  "text/x-basic": ".bas",
  "text/x-c++hdr": ".hpp",
  "text/x-c++src": ".cpp",
  "text/x-cgi": ".cgi",
  "text/x-chdr": ".h",
  "text/x-clojure": ".clj",
  "text/x-cobol": ".cbl",
  "text/x-coffeescript": ".coffee",
  "text/x-coldfusion": ".cfm",
  "text/x-common-lisp": ".cl",
  "text/x-config": ".config",
  "text/x-csharp": ".cs",
  "text/x-csrc": ".c",
  "text/x-d": ".d",
  "text/x-diff": ".diff",
  "text/x-eiffel": ".e",
  "text/x-emacs-lisp": ".el",
  "text/x-erlang": ".erl",
  "text/x-expect": ".exp",
  "text/x-forth": ".4th",
  "text/x-fortran": ".f",
  "text/x-go": ".go",
  "text/x-groovy": ".groovy",
  "text/x-haml": ".haml",
  "text/x-haskell": ".hs",
  "text/x-haxe": ".hx",
  "text/x-idl": ".idl",
  "text/x-ini": ".ini",
  "text/x-java-properties": ".properties",
  "text/x-java-source": ".java",
  "text/x-jsp": ".jsp",
  "text/x-less": ".less",
  "text/x-lex": ".l",
  "text/x-log": ".log",
  "text/x-lua": ".lua",
  "text/x-ml": ".ml",
  "text/x-modula": ".m3",
  "text/x-objcsrc": ".m",
  "text/x-ocaml": ".ocaml",
  "text/x-pascal": ".p",
  "text/x-perl": ".pl",
  "text/x-php": ".php",
  "text/x-prolog": ".pro",
  "text/x-python": ".py",
  "text/x-rexx": ".rexx",
  "text/x-rsrc": ".r",
  "text/x-rst": ".rest",
  "text/x-ruby": ".rb",
  "text/x-scala": ".scala",
  "text/x-scheme": ".scm",
  "text/x-sed": ".sed",
  "text/x-setext": ".etx",
  "text/x-sql": ".sql",
  "text/x-stsrc": ".st",
  "text/x-tcl": ".itk",
  "text/x-uuencode": ".uu",
  "text/x-vbasic": ".cls",
  "text/x-vbdotnet": ".vb",
  "text/x-vbscript": ".vbs",
  "text/x-vcalendar": ".vcs",
  "text/x-vcard": ".vcf",
  "text/x-verilog": ".v",
  "text/x-vhdl": ".vhd",
  "text/x-web-markdown": ".md",
  "text/x-yacc": ".y",
  "text/x-yaml": ".yaml",
  "video/3gpp": ".3gp",
  "video/3gpp2": ".3g2",
  "video/h261": ".h261",
  "video/h263": ".h263",
  "video/h264": ".h264",
  "video/iso.segment": ".m4s",
  "video/jpeg": ".jpgv",
  "video/mj2": ".mj2",
  "video/mp4": ".mp4",
  "video/mpeg": ".mpeg",
  "video/ogg": ".ogv",
  "video/quicktime": ".qt",
  "video/vnd.fvt": ".fvt",
  "video/vnd.mpegurl": ".mxu",
  "video/vnd.ms-playready.media.pyv": ".pyv",
  "video/vnd.vivo": ".viv",
  "video/webm": ".webm",
  "video/x-dirac": ".drc",
  "video/x-f4v": ".f4v",
  "video/x-flc": ".flc",
  "video/x-fli": ".fli",
  "video/x-flv": ".flv",
  "video/x-jng": ".jng",
  "video/x-m4v": ".m4v",
  "video/x-matroska": ".mkv",
  "video/x-mng": ".mng",
  "video/x-ms-asf": ".asf",
  "video/x-ms-wm": ".wm",
  "video/x-ms-wmv": ".wmv",
  "video/x-ms-wmx": ".wmx",
  "video/x-ms-wvx": ".wvx",
  "video/x-msvideo": ".avi",
  "video/x-ogm": ".ogm",
  "video/x-sgi-movie": ".movie",
  "x-conference/x-cooltalk": ".ice"
};
//# sourceMappingURL=extensions.js.map