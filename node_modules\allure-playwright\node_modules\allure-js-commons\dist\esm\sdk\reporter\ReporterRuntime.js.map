{"version": 3, "file": "ReporterRuntime.js", "names": ["path", "extname", "Stage", "LifecycleState", "Notifier", "createFixtureResult", "createStepResult", "createTestResult", "hasSkipLabel", "deepClone", "formatLinks", "getTestResultHistoryId", "getTestResultTestCaseId", "randomUuid", "buildAttachmentFileName", "resolveWriter", "_runningSteps", "WeakMap", "_ShallowStepsStack_brand", "WeakSet", "ShallowStepsStack", "constructor", "_classPrivateMethodInitSpec", "_defineProperty", "_classPrivateFieldInitSpec", "currentStep", "_classPrivateGetter", "_get_currentStep", "startStep", "step", "step<PERSON><PERSON><PERSON>", "_objectSpread", "steps", "push", "_classPrivateFieldGet", "updateStep", "updateFunc", "console", "error", "stopStep", "opts", "stop", "duration", "result", "start", "undefined", "pop", "findStepByUuid", "uuid", "findRecursively", "s", "found", "addAttachment", "attachment", "writer", "isPath", "fileExt", "fileName", "contentType", "fileExtension", "writeAttachmentFromPath", "body", "writeAttachment", "_this", "length", "DefaultStepStack", "Map", "stepsByRoot", "clear", "rootsByStep", "rootUuid", "maybeV<PERSON>ue", "get", "delete", "for<PERSON>ach", "stepUuid", "set", "removeStep", "newValue", "filter", "value", "_handleMetadataMessage", "_handleStepMetadataMessage", "_handleStartStepMessage", "_handleStopStepMessage", "_handleAttachmentContentMessage", "_handleAttachmentPathMessage", "_findParent", "_writeFixturesOfScope", "_writeContainer", "_calculateTimings", "ReporterRuntime", "_ref", "_this2", "listeners", "environmentInfo", "categories", "links", "globalLabels", "state", "setScope", "scope", "getScope", "concat", "call", "deleteScope", "scopeUuid", "type", "fixtureResult", "wrappedFixture", "setFixtureResult", "Date", "now", "fixtures", "fixture", "getFixtureResult", "startStop", "stage", "FINISHED", "scopeUuids", "arguments", "testResult", "notifier", "beforeTestResultStart", "tests", "setTestResult", "afterTestResultStart", "getTestResult", "beforeTestResultUpdate", "afterTestResultUpdate", "_testResult$testCaseI", "_testResult$historyId", "wrapped", "getWrappedTestResult", "beforeTestResultStop", "labels", "parameters", "description", "_testResult$descripti", "descriptionHtml", "_testResult$descripti2", "testCaseId", "historyId", "afterTestResultStop", "deleteTestResult", "beforeTestResultWrite", "writeResult", "afterTestResultWrite", "step<PERSON><PERSON>ck", "parentStepUuid", "parent", "JSON", "stringify", "setStepResult", "addStep", "getStepResult", "beforeStepStop", "afterStepStop", "attachmentName", "attachmentContentOrPath", "options", "_options$fileExtensio", "attachmentFileName", "name", "source", "wrapInStep", "timestamp", "attachments", "writeEnvironmentInfo", "serializedCategories", "map", "c", "messageRegex", "RegExp", "traceRegex", "writeCategoriesDefinitions", "messages", "message", "data", "getWrappedFixtureResult", "displayName", "updateFixture", "updateScope", "linkConfig", "updateTest", "status", "statusDetails", "<PERSON><PERSON><PERSON>", "from", "content", "encoding", "root", "getExecutionItem", "_ref2", "writtenFixtures", "Set", "has", "deleteFixtureResult", "add", "befores", "afters", "writeGroup", "children", "normalisedDuration", "Math", "max", "round", "Array", "isArray", "Object", "keys", "entries", "flatMap", "_ref3", "v"], "sources": ["../../../../src/sdk/reporter/ReporterRuntime.ts"], "sourcesContent": ["/* eslint max-lines: 0 */\nimport path from \"node:path\";\nimport { extname } from \"path\";\nimport {\n  type Attachment,\n  type AttachmentOptions,\n  type FixtureResult,\n  type Label,\n  Stage,\n  type StepResult,\n  type TestResult,\n} from \"../../model.js\";\nimport type {\n  Category,\n  EnvironmentInfo,\n  RuntimeAttachmentContentMessage,\n  RuntimeAttachmentPathMessage,\n  RuntimeMessage,\n  RuntimeMetadataMessage,\n  RuntimeStartStepMessage,\n  RuntimeStepMetadataMessage,\n  RuntimeStopStepMessage,\n} from \"../types.js\";\nimport { LifecycleState } from \"./LifecycleState.js\";\nimport { Notifier } from \"./Notifier.js\";\nimport { createFixtureResult, createStepResult, createTestResult } from \"./factory.js\";\nimport { hasSkipLabel } from \"./testplan.js\";\nimport type {\n  FixtureResultWrapper,\n  FixtureType,\n  LinkConfig,\n  ReporterRuntimeConfig,\n  TestScope,\n  Writer,\n} from \"./types.js\";\nimport { deepClone, formatLinks, getTestResultHistoryId, getTestResultTestCaseId, randomUuid } from \"./utils.js\";\nimport { buildAttachmentFileName } from \"./utils/attachments.js\";\nimport { resolveWriter } from \"./writer/loader.js\";\n\nexport interface StepStack {\n  clear(): void;\n\n  removeRoot(rootUuid: string): void;\n\n  currentStep(rootUuid: string): string | undefined;\n\n  addStep(rootUuid: string, stepUuid: string): void;\n\n  removeStep(stepUuid: string): void;\n}\n\n/**\n * Simpler steps stack implementation that contains only the current steps without root nodes\n * Useful, when you need to create steps without binding them to a specific test or fixture\n * @example\n * ```js\n * const stack = new ShallowStepsStack();\n *\n * stack.startStep({ name: \"step 1\" });\n * stack.startStep({ name: \"step 1.1\" });\n * stack.stopStep({ status: Status.FAILED });\n * stack.stopStep({ status: Status.PASSED });\n * stack.steps // => [{ name: \"step 1\", status: Status.PASSED, steps: [{ name: \"step 1.1\", status: Status.FAILED }] }]\n * ```\n */\nexport class ShallowStepsStack {\n  steps: StepResult[] = [];\n\n  #runningSteps: StepResult[] = [];\n\n  get #currentStep() {\n    return this.#runningSteps[this.#runningSteps.length - 1];\n  }\n\n  currentStep() {\n    return this.#currentStep;\n  }\n\n  startStep(step: Partial<StepResult>) {\n    const stepResult: StepResult = {\n      ...createStepResult(),\n      ...step,\n    };\n\n    if (this.#currentStep) {\n      this.#currentStep.steps.push(stepResult);\n    } else {\n      this.steps.push(stepResult);\n    }\n\n    this.#runningSteps.push(stepResult);\n  }\n\n  updateStep(updateFunc: (result: StepResult) => void) {\n    if (!this.#currentStep) {\n      // eslint-disable-next-line no-console\n      console.error(\"There is no running step in the stack to update!\");\n      return;\n    }\n\n    updateFunc(this.#currentStep);\n  }\n\n  stopStep(opts?: { stop?: number; duration?: number }) {\n    if (!this.#currentStep) {\n      // eslint-disable-next-line no-console\n      console.error(\"There is no running step in the stack to stop!\");\n      return;\n    }\n\n    const { stop, duration = 0 } = opts ?? {};\n\n    this.updateStep((result) => {\n      result.stop = stop ?? result.start ? result.start! + duration : undefined;\n    });\n\n    this.#runningSteps.pop();\n  }\n\n  findStepByUuid(uuid: string): StepResult | undefined {\n    const findRecursively = (steps: StepResult[]): StepResult | undefined => {\n      for (const s of steps) {\n        if (s.uuid === uuid) {\n          return s;\n        }\n        const found = findRecursively(s.steps);\n        if (found) {\n          return found;\n        }\n      }\n      return undefined;\n    };\n    return findRecursively(this.steps);\n  }\n\n  addAttachment(attachment: AttachmentOptions, writer: Writer) {\n    const isPath = !!attachment.path;\n    const fileExt = attachment.path ? path.extname(attachment.path) : undefined;\n    const fileName = buildAttachmentFileName({\n      contentType: attachment.contentType,\n      fileExtension: fileExt,\n    });\n    if (isPath) {\n      writer.writeAttachmentFromPath(fileName, attachment.path as string);\n    } else if (attachment.body) {\n      writer.writeAttachment(fileName, attachment.body);\n    }\n    return fileName;\n  }\n}\n\nexport class DefaultStepStack implements StepStack {\n  private stepsByRoot: Map<string, string[]> = new Map();\n  private rootsByStep: Map<string, string> = new Map();\n\n  clear = (): void => {\n    this.stepsByRoot.clear();\n    this.rootsByStep.clear();\n  };\n\n  removeRoot = (rootUuid: string): void => {\n    const maybeValue = this.stepsByRoot.get(rootUuid);\n    this.stepsByRoot.delete(rootUuid);\n    if (maybeValue) {\n      maybeValue.forEach((stepUuid) => this.rootsByStep.delete(stepUuid));\n    }\n  };\n\n  currentStep = (rootUuid: string): string | undefined => {\n    const maybeValue = this.stepsByRoot.get(rootUuid);\n    if (!maybeValue) {\n      return;\n    }\n    return maybeValue[maybeValue.length - 1];\n  };\n\n  addStep = (rootUuid: string, stepUuid: string): void => {\n    const maybeValue = this.stepsByRoot.get(rootUuid);\n    if (!maybeValue) {\n      this.stepsByRoot.set(rootUuid, [stepUuid]);\n    } else {\n      maybeValue.push(stepUuid);\n    }\n    this.rootsByStep.set(stepUuid, rootUuid);\n  };\n\n  removeStep(stepUuid: string) {\n    const rootUuid = this.rootsByStep.get(stepUuid);\n    if (!rootUuid) {\n      return;\n    }\n    const maybeValue = this.stepsByRoot.get(rootUuid);\n    if (!maybeValue) {\n      return;\n    }\n    const newValue = maybeValue.filter((value) => value !== stepUuid);\n    this.stepsByRoot.set(rootUuid, newValue);\n  }\n}\n\nexport class ReporterRuntime {\n  private readonly state = new LifecycleState();\n  private notifier: Notifier;\n  private stepStack: StepStack = new DefaultStepStack();\n  writer: Writer;\n  categories?: Category[];\n  environmentInfo?: EnvironmentInfo;\n  linkConfig?: LinkConfig;\n  globalLabels: Label[] = [];\n\n  constructor({\n    writer,\n    listeners = [],\n    environmentInfo,\n    categories,\n    links,\n    globalLabels = {},\n  }: ReporterRuntimeConfig) {\n    this.writer = resolveWriter(writer);\n    this.notifier = new Notifier({ listeners });\n    this.categories = categories;\n    this.environmentInfo = environmentInfo;\n    this.linkConfig = links;\n\n    if (Array.isArray(globalLabels)) {\n      this.globalLabels = globalLabels;\n    } else if (Object.keys(globalLabels).length) {\n      this.globalLabels = Object.entries(globalLabels).flatMap(([name, value]) => {\n        if (Array.isArray(value)) {\n          return value.map((v) => ({ name, value: v }));\n        }\n\n        return {\n          name,\n          value,\n        };\n      });\n    }\n  }\n\n  startScope = (): string => {\n    const uuid = randomUuid();\n    this.state.setScope(uuid);\n    return uuid;\n  };\n\n  updateScope = (uuid: string, updateFunc: (scope: TestScope) => void): void => {\n    const scope = this.state.getScope(uuid);\n    if (!scope) {\n      // eslint-disable-next-line no-console\n      console.error(`count not update scope: no scope with uuid ${uuid} is found`);\n      return;\n    }\n\n    updateFunc(scope);\n  };\n\n  writeScope = (uuid: string) => {\n    const scope = this.state.getScope(uuid);\n    if (!scope) {\n      // eslint-disable-next-line no-console\n      console.error(`count not write scope: no scope with uuid ${uuid} is found`);\n      return;\n    }\n\n    this.#writeFixturesOfScope(scope);\n    this.state.deleteScope(scope.uuid);\n  };\n\n  startFixture = (scopeUuid: string, type: FixtureType, fixtureResult: Partial<FixtureResult>): string | undefined => {\n    const scope = this.state.getScope(scopeUuid);\n    if (!scope) {\n      // eslint-disable-next-line no-console\n      console.error(`count not start fixture: no scope with uuid ${scopeUuid} is found`);\n      return;\n    }\n\n    const uuid = randomUuid();\n    const wrappedFixture = this.state.setFixtureResult(scopeUuid, uuid, type, {\n      ...createFixtureResult(),\n      start: Date.now(),\n      ...fixtureResult,\n    });\n\n    scope.fixtures.push(wrappedFixture);\n    return uuid;\n  };\n\n  updateFixture = (uuid: string, updateFunc: (result: FixtureResult) => void): void => {\n    const fixture = this.state.getFixtureResult(uuid);\n\n    if (!fixture) {\n      // eslint-disable-next-line no-console\n      console.error(`could not update fixture: no fixture with uuid ${uuid} is found`);\n      return;\n    }\n\n    updateFunc(fixture);\n  };\n\n  stopFixture = (uuid: string, opts?: { stop?: number; duration?: number }): void => {\n    const fixture = this.state.getFixtureResult(uuid);\n    if (!fixture) {\n      // eslint-disable-next-line no-console\n      console.error(`could not stop fixture: no fixture with uuid ${uuid} is found`);\n      return;\n    }\n\n    const startStop = this.#calculateTimings(fixture.start, opts?.stop, opts?.duration);\n    fixture.start = startStop.start;\n    fixture.stop = startStop.stop;\n\n    fixture.stage = Stage.FINISHED;\n  };\n\n  startTest = (result: Partial<TestResult>, scopeUuids: string[] = []): string => {\n    const uuid = randomUuid();\n    const testResult: TestResult = {\n      ...createTestResult(uuid),\n      start: Date.now(),\n      ...deepClone(result),\n    };\n\n    this.notifier.beforeTestResultStart(testResult);\n\n    scopeUuids.forEach((scopeUuid) => {\n      const scope = this.state.getScope(scopeUuid);\n      if (!scope) {\n        // eslint-disable-next-line no-console\n        console.error(`count not link test to the scope: no scope with uuid ${uuid} is found`);\n        return;\n      }\n      scope.tests.push(uuid);\n    });\n\n    this.state.setTestResult(uuid, testResult, scopeUuids);\n    this.notifier.afterTestResultStart(testResult);\n    return uuid;\n  };\n\n  updateTest = (uuid: string, updateFunc: (result: TestResult) => void): void => {\n    const testResult = this.state.getTestResult(uuid);\n\n    if (!testResult) {\n      // eslint-disable-next-line no-console\n      console.error(`could not update test result: no test with uuid ${uuid}) is found`);\n      return;\n    }\n\n    this.notifier.beforeTestResultUpdate(testResult);\n    updateFunc(testResult);\n    this.notifier.afterTestResultUpdate(testResult);\n  };\n\n  stopTest = (uuid: string, opts?: { stop?: number; duration?: number }) => {\n    const wrapped = this.state.getWrappedTestResult(uuid);\n    if (!wrapped) {\n      // eslint-disable-next-line no-console\n      console.error(`could not stop test result: no test with uuid ${uuid}) is found`);\n      return;\n    }\n\n    const testResult = wrapped.value;\n\n    this.notifier.beforeTestResultStop(testResult);\n\n    const scopeUuids = wrapped.scopeUuids;\n\n    scopeUuids.forEach((scopeUuid) => {\n      const scope = this.state.getScope(scopeUuid);\n\n      if (scope?.labels) {\n        testResult.labels = [...testResult.labels, ...scope.labels];\n      }\n\n      if (scope?.links) {\n        testResult.links = [...testResult.links, ...scope.links];\n      }\n\n      if (scope?.parameters) {\n        testResult.parameters = [...testResult.parameters, ...scope.parameters];\n      }\n\n      if (scope?.description) {\n        testResult.description = testResult.description ?? scope.description;\n      }\n\n      if (scope?.descriptionHtml) {\n        testResult.descriptionHtml = testResult.descriptionHtml ?? scope.descriptionHtml;\n      }\n    });\n\n    testResult.labels = [...this.globalLabels, ...testResult.labels];\n\n    testResult.testCaseId ??= getTestResultTestCaseId(testResult);\n    testResult.historyId ??= getTestResultHistoryId(testResult);\n\n    const startStop = this.#calculateTimings(testResult.start, opts?.stop, opts?.duration);\n\n    testResult.start = startStop.start;\n    testResult.stop = startStop.stop;\n\n    this.notifier.afterTestResultStop(testResult);\n  };\n\n  writeTest = (uuid: string) => {\n    const testResult = this.state.getTestResult(uuid);\n    if (!testResult) {\n      // eslint-disable-next-line no-console\n      console.error(`could not write test result: no test with uuid ${uuid} is found`);\n      return;\n    }\n\n    if (hasSkipLabel(testResult.labels)) {\n      this.state.deleteTestResult(uuid);\n      return;\n    }\n\n    this.notifier.beforeTestResultWrite(testResult);\n\n    this.writer.writeResult(testResult);\n    this.state.deleteTestResult(uuid);\n\n    this.notifier.afterTestResultWrite(testResult);\n  };\n\n  currentStep = (rootUuid: string): string | undefined => {\n    return this.stepStack.currentStep(rootUuid);\n  };\n\n  startStep = (\n    rootUuid: string,\n    parentStepUuid: string | null | undefined,\n    result: Partial<StepResult>,\n  ): string | undefined => {\n    const parent = this.#findParent(rootUuid, parentStepUuid);\n    if (!parent) {\n      // eslint-disable-next-line no-console\n      console.error(\n        `could not start test step: no context for root ${rootUuid} and parentStepUuid ${JSON.stringify(parentStepUuid)} is found`,\n      );\n      return;\n    }\n    const stepResult: StepResult = {\n      ...createStepResult(),\n      start: Date.now(),\n      ...result,\n    };\n    parent.steps.push(stepResult);\n    const stepUuid = randomUuid();\n    this.state.setStepResult(stepUuid, stepResult);\n\n    this.stepStack.addStep(rootUuid, stepUuid);\n\n    return stepUuid;\n  };\n\n  updateStep = (uuid: string, updateFunc: (stepResult: StepResult) => void) => {\n    const step = this.state.getStepResult(uuid)!;\n    if (!step) {\n      // eslint-disable-next-line no-console\n      console.error(`could not update test step: no step with uuid ${uuid} is found`);\n      return;\n    }\n\n    updateFunc(step);\n  };\n\n  stopStep = (uuid: string, opts?: { stop?: number; duration?: number }) => {\n    const step = this.state.getStepResult(uuid);\n    if (!step) {\n      // eslint-disable-next-line no-console\n      console.error(`could not stop test step: no step with uuid ${uuid} is found`);\n      return;\n    }\n\n    this.notifier.beforeStepStop(step);\n\n    const startStop = this.#calculateTimings(step.start, opts?.stop, opts?.duration);\n    step.start = startStop.start;\n    step.stop = startStop.stop;\n\n    step.stage = Stage.FINISHED;\n\n    this.stepStack.removeStep(uuid);\n\n    this.notifier.afterStepStop(step);\n  };\n\n  writeAttachment = (\n    rootUuid: string,\n    parentStepUuid: string | null | undefined,\n    attachmentName: string,\n    attachmentContentOrPath: Buffer | string,\n    options: AttachmentOptions & { wrapInStep?: boolean; timestamp?: number },\n  ) => {\n    const parent = this.#findParent(rootUuid, parentStepUuid);\n    if (!parent) {\n      // eslint-disable-next-line no-console\n      console.error(\n        `could not write test attachment: no context for root ${rootUuid} and parentStepUuid ${JSON.stringify(parentStepUuid)} is found`,\n      );\n      return;\n    }\n\n    const isPath = typeof attachmentContentOrPath === \"string\";\n    const fileExtension = options.fileExtension ?? (isPath ? extname(attachmentContentOrPath) : undefined);\n    const attachmentFileName = buildAttachmentFileName({\n      contentType: options.contentType,\n      fileExtension,\n    });\n\n    if (isPath) {\n      this.writer.writeAttachmentFromPath(attachmentFileName, attachmentContentOrPath);\n    } else {\n      this.writer.writeAttachment(attachmentFileName, attachmentContentOrPath);\n    }\n\n    const attachment: Attachment = {\n      name: attachmentName,\n      source: attachmentFileName,\n      type: options.contentType,\n    };\n\n    if (options.wrapInStep) {\n      const { timestamp = Date.now() } = options;\n      parent.steps.push({\n        name: attachmentName,\n        attachments: [attachment],\n        start: timestamp,\n        stop: timestamp,\n      } as StepResult);\n    } else {\n      parent.attachments.push(attachment);\n    }\n  };\n\n  writeEnvironmentInfo = () => {\n    if (!this.environmentInfo) {\n      return;\n    }\n\n    this.writer.writeEnvironmentInfo(this.environmentInfo);\n  };\n\n  writeCategoriesDefinitions = () => {\n    if (!this.categories) {\n      return;\n    }\n\n    const serializedCategories = this.categories.map((c) => {\n      if (c.messageRegex instanceof RegExp) {\n        c.messageRegex = c.messageRegex.source;\n      }\n\n      if (c.traceRegex instanceof RegExp) {\n        c.traceRegex = c.traceRegex.source;\n      }\n\n      return c;\n    });\n\n    this.writer.writeCategoriesDefinitions(serializedCategories);\n  };\n\n  applyRuntimeMessages = (rootUuid: string, messages: RuntimeMessage[]) => {\n    messages.forEach((message) => {\n      switch (message.type) {\n        case \"metadata\":\n          this.#handleMetadataMessage(rootUuid, message.data);\n          return;\n        case \"step_metadata\":\n          this.#handleStepMetadataMessage(rootUuid, message.data);\n          return;\n        case \"step_start\":\n          this.#handleStartStepMessage(rootUuid, message.data);\n          return;\n        case \"step_stop\":\n          this.#handleStopStepMessage(rootUuid, message.data);\n          return;\n        case \"attachment_content\":\n          this.#handleAttachmentContentMessage(rootUuid, message.data);\n          return;\n        case \"attachment_path\":\n          this.#handleAttachmentPathMessage(rootUuid, message.data);\n          return;\n        default:\n          // eslint-disable-next-line no-console\n          console.error(`could not apply runtime messages: unknown message ${JSON.stringify(message)}`);\n          return;\n      }\n    });\n  };\n\n  #handleMetadataMessage = (rootUuid: string, message: RuntimeMetadataMessage[\"data\"]) => {\n    // only display name could be set to fixture.\n    const fixtureResult = this.state.getWrappedFixtureResult(rootUuid);\n    const { links, labels, parameters, displayName, testCaseId, historyId, description, descriptionHtml } = message;\n\n    if (fixtureResult) {\n      if (displayName) {\n        this.updateFixture(rootUuid, (result) => {\n          result.name = displayName;\n        });\n      }\n\n      if (historyId) {\n        // eslint-disable-next-line no-console\n        console.error(\"historyId can't be changed within test fixtures\");\n      }\n      if (testCaseId) {\n        // eslint-disable-next-line no-console\n        console.error(\"testCaseId can't be changed within test fixtures\");\n      }\n\n      if (links || labels || parameters || description || descriptionHtml) {\n        // in some frameworks, afterEach methods can be executed before test stop event, while\n        // in others after. To remove the possible undetermined behaviour we only allow\n        // using runtime metadata API in before hooks.\n        if (fixtureResult.type === \"after\") {\n          // eslint-disable-next-line no-console\n          console.error(\"metadata messages isn't supported for after test fixtures\");\n          return;\n        }\n\n        this.updateScope(fixtureResult.scopeUuid, (scope) => {\n          if (links) {\n            scope.links = [...scope.links, ...(this.linkConfig ? formatLinks(this.linkConfig, links) : links)];\n          }\n          if (labels) {\n            scope.labels = [...scope.labels, ...labels];\n          }\n          if (parameters) {\n            scope.parameters = [...scope.parameters, ...parameters];\n          }\n          if (description) {\n            scope.description = description;\n          }\n          if (descriptionHtml) {\n            scope.descriptionHtml = descriptionHtml;\n          }\n        });\n      }\n\n      return;\n    }\n\n    this.updateTest(rootUuid, (result) => {\n      if (links) {\n        result.links = [...result.links, ...(this.linkConfig ? formatLinks(this.linkConfig, links) : links)];\n      }\n      if (labels) {\n        result.labels = [...result.labels, ...labels];\n      }\n      if (parameters) {\n        result.parameters = [...result.parameters, ...parameters];\n      }\n      if (displayName) {\n        result.name = displayName;\n      }\n      if (testCaseId) {\n        result.testCaseId = testCaseId;\n      }\n      if (historyId) {\n        result.historyId = historyId;\n      }\n      if (description) {\n        result.description = description;\n      }\n      if (descriptionHtml) {\n        result.descriptionHtml = descriptionHtml;\n      }\n    });\n  };\n\n  #handleStepMetadataMessage = (rootUuid: string, message: RuntimeStepMetadataMessage[\"data\"]) => {\n    const stepUuid = this.currentStep(rootUuid);\n    if (!stepUuid) {\n      // eslint-disable-next-line no-console\n      console.error(\"could not handle step metadata message: no step is running\");\n      return;\n    }\n    const { name, parameters } = message;\n    this.updateStep(stepUuid, (stepResult) => {\n      if (name) {\n        stepResult.name = name;\n      }\n      if (parameters) {\n        stepResult.parameters = [...stepResult.parameters, ...parameters];\n      }\n    });\n  };\n\n  #handleStartStepMessage = (rootUuid: string, message: RuntimeStartStepMessage[\"data\"]) => {\n    this.startStep(rootUuid, undefined, { ...message });\n  };\n\n  #handleStopStepMessage = (rootUuid: string, message: RuntimeStopStepMessage[\"data\"]) => {\n    const stepUuid = this.currentStep(rootUuid);\n    if (!stepUuid) {\n      // eslint-disable-next-line no-console\n      console.error(\"could not handle step stop message: no step is running\");\n      return;\n    }\n    this.updateStep(stepUuid, (result) => {\n      if (message.status && !result.status) {\n        result.status = message.status;\n      }\n      if (message.statusDetails) {\n        result.statusDetails = { ...result.statusDetails, ...message.statusDetails };\n      }\n    });\n    this.stopStep(stepUuid, { stop: message.stop });\n  };\n\n  #handleAttachmentContentMessage = (rootUuid: string, message: RuntimeAttachmentContentMessage[\"data\"]) => {\n    this.writeAttachment(rootUuid, undefined, message.name, Buffer.from(message.content, message.encoding), {\n      encoding: message.encoding,\n      contentType: message.contentType,\n      fileExtension: message.fileExtension,\n      wrapInStep: message.wrapInStep,\n      timestamp: message.timestamp,\n    });\n  };\n\n  #handleAttachmentPathMessage = (rootUuid: string, message: RuntimeAttachmentPathMessage[\"data\"]) => {\n    this.writeAttachment(rootUuid, undefined, message.name, message.path, {\n      contentType: message.contentType,\n      fileExtension: message.fileExtension,\n      wrapInStep: message.wrapInStep,\n      timestamp: message.timestamp,\n    });\n  };\n\n  #findParent = (\n    rootUuid: string,\n    parentStepUuid: string | null | undefined,\n  ): FixtureResult | TestResult | StepResult | undefined => {\n    const root = this.state.getExecutionItem(rootUuid);\n    if (!root) {\n      return;\n    }\n\n    if (parentStepUuid === null) {\n      return root;\n    } else if (parentStepUuid === undefined) {\n      const stepUuid = this.currentStep(rootUuid);\n      return stepUuid ? this.state.getStepResult(stepUuid) : root;\n    } else {\n      return this.state.getStepResult(parentStepUuid);\n    }\n  };\n\n  #writeFixturesOfScope = ({ fixtures, tests }: TestScope) => {\n    const writtenFixtures = new Set<string>();\n    if (tests.length) {\n      for (const wrappedFixture of fixtures) {\n        if (!writtenFixtures.has(wrappedFixture.uuid)) {\n          this.#writeContainer(tests, wrappedFixture);\n          this.state.deleteFixtureResult(wrappedFixture.uuid);\n          writtenFixtures.add(wrappedFixture.uuid);\n        }\n      }\n    }\n  };\n\n  #writeContainer = (tests: string[], wrappedFixture: FixtureResultWrapper) => {\n    const fixture = wrappedFixture.value;\n    const befores = wrappedFixture.type === \"before\" ? [wrappedFixture.value] : [];\n    const afters = wrappedFixture.type === \"after\" ? [wrappedFixture.value] : [];\n    this.writer.writeGroup({\n      uuid: wrappedFixture.uuid,\n      name: fixture.name,\n      children: [...new Set(tests)],\n      befores,\n      afters,\n    });\n  };\n\n  #calculateTimings = (\n    start?: number,\n    stop?: number,\n    duration?: number,\n  ): {\n    start?: number;\n    stop?: number;\n  } => {\n    const result: { start?: number; stop?: number } = { start, stop };\n    if (duration) {\n      const normalisedDuration = Math.max(0, duration);\n      if (result.stop !== undefined) {\n        result.start = result.stop - normalisedDuration;\n      } else if (result.start !== undefined) {\n        result.stop = result.start + normalisedDuration;\n      } else {\n        result.stop = Date.now();\n        result.start = result.stop - normalisedDuration;\n      }\n    } else {\n      if (result.stop === undefined) {\n        result.stop = Date.now();\n      }\n      if (result.start === undefined) {\n        result.start = result.stop;\n      }\n    }\n    return {\n      start: result.start ? Math.round(result.start) : undefined,\n      stop: result.stop ? Math.round(result.stop) : undefined,\n    };\n  };\n}\n"], "mappings": ";;;;;;;;;;;AAAA;AACA,OAAOA,IAAI,MAAM,WAAW;AAC5B,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAKEC,KAAK,QAGA,gBAAgB;AAYvB,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,mBAAmB,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,cAAc;AACtF,SAASC,YAAY,QAAQ,eAAe;AAS5C,SAASC,SAAS,EAAEC,WAAW,EAAEC,sBAAsB,EAAEC,uBAAuB,EAAEC,UAAU,QAAQ,YAAY;AAChH,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,SAASC,aAAa,QAAQ,oBAAoB;AAAC,IAAAC,aAAA,oBAAAC,OAAA;AAAA,IAAAC,wBAAA,oBAAAC,OAAA;AAcnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,CAAC;EAAAC,YAAA;IAAAC,2BAAA,OAAAJ,wBAAA;IAAAK,eAAA,gBACP,EAAE;IAExBC,0BAAA,OAAAR,aAAa,EAAiB,EAAE;EAAC;EAMjCS,WAAWA,CAAA,EAAG;IACZ,OAAOC,mBAAA,CAAAR,wBAAA,MAAI,EAACS,gBAAW,CAAC;EAC1B;EAEAC,SAASA,CAACC,IAAyB,EAAE;IACnC,IAAMC,UAAsB,GAAAC,aAAA,CAAAA,aAAA,KACvBzB,gBAAgB,CAAC,CAAC,GAClBuB,IAAI,CACR;IAED,IAAIH,mBAAA,CAAAR,wBAAA,MAAI,EAACS,gBAAW,CAAC,EAAE;MACrBD,mBAAA,CAAAR,wBAAA,MAAI,EAACS,gBAAW,CAAC,CAACK,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAACE,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC;IAC7B;IAEAI,qBAAA,CAAKlB,aAAa,EAAlB,IAAiB,CAAC,CAACiB,IAAI,CAACH,UAAU,CAAC;EACrC;EAEAK,UAAUA,CAACC,UAAwC,EAAE;IACnD,IAAI,CAACV,mBAAA,CAAAR,wBAAA,MAAI,EAACS,gBAAW,CAAC,EAAE;MACtB;MACAU,OAAO,CAACC,KAAK,CAAC,kDAAkD,CAAC;MACjE;IACF;IAEAF,UAAU,CAACV,mBAAA,CAAAR,wBAAA,MAAI,EAACS,gBAAW,CAAC,CAAC;EAC/B;EAEAY,QAAQA,CAACC,IAA2C,EAAE;IACpD,IAAI,CAACd,mBAAA,CAAAR,wBAAA,MAAI,EAACS,gBAAW,CAAC,EAAE;MACtB;MACAU,OAAO,CAACC,KAAK,CAAC,gDAAgD,CAAC;MAC/D;IACF;IAEA,IAAM;MAAEG,IAAI;MAAEC,QAAQ,GAAG;IAAE,CAAC,GAAGF,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,CAAC,CAAC;IAEzC,IAAI,CAACL,UAAU,CAAEQ,MAAM,IAAK;MAC1BA,MAAM,CAACF,IAAI,GAAG,CAAAA,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAIE,MAAM,CAACC,KAAK,IAAGD,MAAM,CAACC,KAAK,GAAIF,QAAQ,GAAGG,SAAS;IAC3E,CAAC,CAAC;IAEFX,qBAAA,CAAKlB,aAAa,EAAlB,IAAiB,CAAC,CAAC8B,GAAG,CAAC,CAAC;EAC1B;EAEAC,cAAcA,CAACC,IAAY,EAA0B;IACnD,IAAMC,eAAe,GAAIjB,KAAmB,IAA6B;MACvE,KAAK,IAAMkB,CAAC,IAAIlB,KAAK,EAAE;QACrB,IAAIkB,CAAC,CAACF,IAAI,KAAKA,IAAI,EAAE;UACnB,OAAOE,CAAC;QACV;QACA,IAAMC,KAAK,GAAGF,eAAe,CAACC,CAAC,CAAClB,KAAK,CAAC;QACtC,IAAImB,KAAK,EAAE;UACT,OAAOA,KAAK;QACd;MACF;MACA,OAAON,SAAS;IAClB,CAAC;IACD,OAAOI,eAAe,CAAC,IAAI,CAACjB,KAAK,CAAC;EACpC;EAEAoB,aAAaA,CAACC,UAA6B,EAAEC,MAAc,EAAE;IAC3D,IAAMC,MAAM,GAAG,CAAC,CAACF,UAAU,CAACrD,IAAI;IAChC,IAAMwD,OAAO,GAAGH,UAAU,CAACrD,IAAI,GAAGA,IAAI,CAACC,OAAO,CAACoD,UAAU,CAACrD,IAAI,CAAC,GAAG6C,SAAS;IAC3E,IAAMY,QAAQ,GAAG3C,uBAAuB,CAAC;MACvC4C,WAAW,EAAEL,UAAU,CAACK,WAAW;MACnCC,aAAa,EAAEH;IACjB,CAAC,CAAC;IACF,IAAID,MAAM,EAAE;MACVD,MAAM,CAACM,uBAAuB,CAACH,QAAQ,EAAEJ,UAAU,CAACrD,IAAc,CAAC;IACrE,CAAC,MAAM,IAAIqD,UAAU,CAACQ,IAAI,EAAE;MAC1BP,MAAM,CAACQ,eAAe,CAACL,QAAQ,EAAEJ,UAAU,CAACQ,IAAI,CAAC;IACnD;IACA,OAAOJ,QAAQ;EACjB;AACF;AAAC,SAAA9B,iBAAAoC,KAAA,EA/EoB;EACjB,OAAO7B,qBAAA,CAAKlB,aAAa,EAAA+C,KAAD,CAAC,CAAC7B,qBAAA,CAAKlB,aAAa,EAAA+C,KAAD,CAAC,CAACC,MAAM,GAAG,CAAC,CAAC;AAC1D;AA+EF,OAAO,MAAMC,gBAAgB,CAAsB;EAAA5C,YAAA;IAAAE,eAAA,sBACJ,IAAI2C,GAAG,CAAC,CAAC;IAAA3C,eAAA,sBACX,IAAI2C,GAAG,CAAC,CAAC;IAAA3C,eAAA,gBAE5C,MAAY;MAClB,IAAI,CAAC4C,WAAW,CAACC,KAAK,CAAC,CAAC;MACxB,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC,CAAC;IAC1B,CAAC;IAAA7C,eAAA,qBAEa+C,QAAgB,IAAW;MACvC,IAAMC,UAAU,GAAG,IAAI,CAACJ,WAAW,CAACK,GAAG,CAACF,QAAQ,CAAC;MACjD,IAAI,CAACH,WAAW,CAACM,MAAM,CAACH,QAAQ,CAAC;MACjC,IAAIC,UAAU,EAAE;QACdA,UAAU,CAACG,OAAO,CAAEC,QAAQ,IAAK,IAAI,CAACN,WAAW,CAACI,MAAM,CAACE,QAAQ,CAAC,CAAC;MACrE;IACF,CAAC;IAAApD,eAAA,sBAEc+C,QAAgB,IAAyB;MACtD,IAAMC,UAAU,GAAG,IAAI,CAACJ,WAAW,CAACK,GAAG,CAACF,QAAQ,CAAC;MACjD,IAAI,CAACC,UAAU,EAAE;QACf;MACF;MACA,OAAOA,UAAU,CAACA,UAAU,CAACP,MAAM,GAAG,CAAC,CAAC;IAC1C,CAAC;IAAAzC,eAAA,kBAES,CAAC+C,QAAgB,EAAEK,QAAgB,KAAW;MACtD,IAAMJ,UAAU,GAAG,IAAI,CAACJ,WAAW,CAACK,GAAG,CAACF,QAAQ,CAAC;MACjD,IAAI,CAACC,UAAU,EAAE;QACf,IAAI,CAACJ,WAAW,CAACS,GAAG,CAACN,QAAQ,EAAE,CAACK,QAAQ,CAAC,CAAC;MAC5C,CAAC,MAAM;QACLJ,UAAU,CAACtC,IAAI,CAAC0C,QAAQ,CAAC;MAC3B;MACA,IAAI,CAACN,WAAW,CAACO,GAAG,CAACD,QAAQ,EAAEL,QAAQ,CAAC;IAC1C,CAAC;EAAA;EAEDO,UAAUA,CAACF,QAAgB,EAAE;IAC3B,IAAML,QAAQ,GAAG,IAAI,CAACD,WAAW,CAACG,GAAG,CAACG,QAAQ,CAAC;IAC/C,IAAI,CAACL,QAAQ,EAAE;MACb;IACF;IACA,IAAMC,UAAU,GAAG,IAAI,CAACJ,WAAW,CAACK,GAAG,CAACF,QAAQ,CAAC;IACjD,IAAI,CAACC,UAAU,EAAE;MACf;IACF;IACA,IAAMO,QAAQ,GAAGP,UAAU,CAACQ,MAAM,CAAEC,KAAK,IAAKA,KAAK,KAAKL,QAAQ,CAAC;IACjE,IAAI,CAACR,WAAW,CAACS,GAAG,CAACN,QAAQ,EAAEQ,QAAQ,CAAC;EAC1C;AACF;AAAC,IAAAG,sBAAA,oBAAAhE,OAAA;AAAA,IAAAiE,0BAAA,oBAAAjE,OAAA;AAAA,IAAAkE,uBAAA,oBAAAlE,OAAA;AAAA,IAAAmE,sBAAA,oBAAAnE,OAAA;AAAA,IAAAoE,+BAAA,oBAAApE,OAAA;AAAA,IAAAqE,4BAAA,oBAAArE,OAAA;AAAA,IAAAsE,WAAA,oBAAAtE,OAAA;AAAA,IAAAuE,qBAAA,oBAAAvE,OAAA;AAAA,IAAAwE,eAAA,oBAAAxE,OAAA;AAAA,IAAAyE,iBAAA,oBAAAzE,OAAA;AAED,OAAO,MAAM0E,eAAe,CAAC;EAU3BtE,WAAWA,CAAAuE,IAAA,EAOe;IAAA,IAAAC,MAAA;IAAA,IAPd;MACVvC,MAAM;MACNwC,SAAS,GAAG,EAAE;MACdC,eAAe;MACfC,UAAU;MACVC,KAAK,EAALA,MAAK;MACLC,YAAY,GAAG,CAAC;IACK,CAAC,GAAAN,IAAA;IAAArE,eAAA,gBAhBC,IAAIpB,cAAc,CAAC,CAAC;IAAAoB,eAAA;IAAAA,eAAA,oBAEd,IAAI0C,gBAAgB,CAAC,CAAC;IAAA1C,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA;IAAAA,eAAA,uBAK7B,EAAE;IAAAA,eAAA,qBAgCb,MAAc;MACzB,IAAMyB,IAAI,GAAGnC,UAAU,CAAC,CAAC;MACzB,IAAI,CAACsF,KAAK,CAACC,QAAQ,CAACpD,IAAI,CAAC;MACzB,OAAOA,IAAI;IACb,CAAC;IAAAzB,eAAA,sBAEa,CAACyB,IAAY,EAAEZ,UAAsC,KAAW;MAC5E,IAAMiE,KAAK,GAAG,IAAI,CAACF,KAAK,CAACG,QAAQ,CAACtD,IAAI,CAAC;MACvC,IAAI,CAACqD,KAAK,EAAE;QACV;QACAhE,OAAO,CAACC,KAAK,+CAAAiE,MAAA,CAA+CvD,IAAI,cAAW,CAAC;QAC5E;MACF;MAEAZ,UAAU,CAACiE,KAAK,CAAC;IACnB,CAAC;IAAA9E,eAAA,qBAEayB,IAAY,IAAK;MAC7B,IAAMqD,KAAK,GAAG,IAAI,CAACF,KAAK,CAACG,QAAQ,CAACtD,IAAI,CAAC;MACvC,IAAI,CAACqD,KAAK,EAAE;QACV;QACAhE,OAAO,CAACC,KAAK,8CAAAiE,MAAA,CAA8CvD,IAAI,cAAW,CAAC;QAC3E;MACF;MAEAd,qBAAA,CAAKsD,qBAAqB,EAA1B,IAAyB,CAAC,CAAAgB,IAAA,CAA1B,IAAI,EAAuBH,KAAK;MAChC,IAAI,CAACF,KAAK,CAACM,WAAW,CAACJ,KAAK,CAACrD,IAAI,CAAC;IACpC,CAAC;IAAAzB,eAAA,uBAEc,CAACmF,SAAiB,EAAEC,IAAiB,EAAEC,aAAqC,KAAyB;MAClH,IAAMP,KAAK,GAAG,IAAI,CAACF,KAAK,CAACG,QAAQ,CAACI,SAAS,CAAC;MAC5C,IAAI,CAACL,KAAK,EAAE;QACV;QACAhE,OAAO,CAACC,KAAK,gDAAAiE,MAAA,CAAgDG,SAAS,cAAW,CAAC;QAClF;MACF;MAEA,IAAM1D,IAAI,GAAGnC,UAAU,CAAC,CAAC;MACzB,IAAMgG,cAAc,GAAG,IAAI,CAACV,KAAK,CAACW,gBAAgB,CAACJ,SAAS,EAAE1D,IAAI,EAAE2D,IAAI,EAAA5E,aAAA,CAAAA,aAAA,KACnE1B,mBAAmB,CAAC,CAAC;QACxBuC,KAAK,EAAEmE,IAAI,CAACC,GAAG,CAAC;MAAC,GACdJ,aAAa,CACjB,CAAC;MAEFP,KAAK,CAACY,QAAQ,CAAChF,IAAI,CAAC4E,cAAc,CAAC;MACnC,OAAO7D,IAAI;IACb,CAAC;IAAAzB,eAAA,wBAEe,CAACyB,IAAY,EAAEZ,UAA2C,KAAW;MACnF,IAAM8E,OAAO,GAAG,IAAI,CAACf,KAAK,CAACgB,gBAAgB,CAACnE,IAAI,CAAC;MAEjD,IAAI,CAACkE,OAAO,EAAE;QACZ;QACA7E,OAAO,CAACC,KAAK,mDAAAiE,MAAA,CAAmDvD,IAAI,cAAW,CAAC;QAChF;MACF;MAEAZ,UAAU,CAAC8E,OAAO,CAAC;IACrB,CAAC;IAAA3F,eAAA,sBAEa,CAACyB,IAAY,EAAER,IAA2C,KAAW;MACjF,IAAM0E,OAAO,GAAG,IAAI,CAACf,KAAK,CAACgB,gBAAgB,CAACnE,IAAI,CAAC;MACjD,IAAI,CAACkE,OAAO,EAAE;QACZ;QACA7E,OAAO,CAACC,KAAK,iDAAAiE,MAAA,CAAiDvD,IAAI,cAAW,CAAC;QAC9E;MACF;MAEA,IAAMoE,SAAS,GAAGlF,qBAAA,CAAKwD,iBAAiB,EAAtB,IAAqB,CAAC,CAAAc,IAAA,CAAtB,IAAI,EAAmBU,OAAO,CAACtE,KAAK,EAAEJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,EAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,QAAQ,CAAC;MACnFwE,OAAO,CAACtE,KAAK,GAAGwE,SAAS,CAACxE,KAAK;MAC/BsE,OAAO,CAACzE,IAAI,GAAG2E,SAAS,CAAC3E,IAAI;MAE7ByE,OAAO,CAACG,KAAK,GAAGnH,KAAK,CAACoH,QAAQ;IAChC,CAAC;IAAA/F,eAAA,oBAEW,UAACoB,MAA2B,EAAwC;MAAA,IAAtC4E,UAAoB,GAAAC,SAAA,CAAAxD,MAAA,QAAAwD,SAAA,QAAA3E,SAAA,GAAA2E,SAAA,MAAG,EAAE;MACjE,IAAMxE,IAAI,GAAGnC,UAAU,CAAC,CAAC;MACzB,IAAM4G,UAAsB,GAAA1F,aAAA,CAAAA,aAAA,KACvBxB,gBAAgB,CAACyC,IAAI,CAAC;QACzBJ,KAAK,EAAEmE,IAAI,CAACC,GAAG,CAAC;MAAC,GACdvG,SAAS,CAACkC,MAAM,CAAC,CACrB;MAEDkD,MAAI,CAAC6B,QAAQ,CAACC,qBAAqB,CAACF,UAAU,CAAC;MAE/CF,UAAU,CAAC7C,OAAO,CAAEgC,SAAS,IAAK;QAChC,IAAML,KAAK,GAAGR,MAAI,CAACM,KAAK,CAACG,QAAQ,CAACI,SAAS,CAAC;QAC5C,IAAI,CAACL,KAAK,EAAE;UACV;UACAhE,OAAO,CAACC,KAAK,yDAAAiE,MAAA,CAAyDvD,IAAI,cAAW,CAAC;UACtF;QACF;QACAqD,KAAK,CAACuB,KAAK,CAAC3F,IAAI,CAACe,IAAI,CAAC;MACxB,CAAC,CAAC;MAEF6C,MAAI,CAACM,KAAK,CAAC0B,aAAa,CAAC7E,IAAI,EAAEyE,UAAU,EAAEF,UAAU,CAAC;MACtD1B,MAAI,CAAC6B,QAAQ,CAACI,oBAAoB,CAACL,UAAU,CAAC;MAC9C,OAAOzE,IAAI;IACb,CAAC;IAAAzB,eAAA,qBAEY,CAACyB,IAAY,EAAEZ,UAAwC,KAAW;MAC7E,IAAMqF,UAAU,GAAG,IAAI,CAACtB,KAAK,CAAC4B,aAAa,CAAC/E,IAAI,CAAC;MAEjD,IAAI,CAACyE,UAAU,EAAE;QACf;QACApF,OAAO,CAACC,KAAK,oDAAAiE,MAAA,CAAoDvD,IAAI,eAAY,CAAC;QAClF;MACF;MAEA,IAAI,CAAC0E,QAAQ,CAACM,sBAAsB,CAACP,UAAU,CAAC;MAChDrF,UAAU,CAACqF,UAAU,CAAC;MACtB,IAAI,CAACC,QAAQ,CAACO,qBAAqB,CAACR,UAAU,CAAC;IACjD,CAAC;IAAAlG,eAAA,mBAEU,CAACyB,IAAY,EAAER,IAA2C,KAAK;MAAA,IAAA0F,qBAAA,EAAAC,qBAAA;MACxE,IAAMC,OAAO,GAAG,IAAI,CAACjC,KAAK,CAACkC,oBAAoB,CAACrF,IAAI,CAAC;MACrD,IAAI,CAACoF,OAAO,EAAE;QACZ;QACA/F,OAAO,CAACC,KAAK,kDAAAiE,MAAA,CAAkDvD,IAAI,eAAY,CAAC;QAChF;MACF;MAEA,IAAMyE,UAAU,GAAGW,OAAO,CAACpD,KAAK;MAEhC,IAAI,CAAC0C,QAAQ,CAACY,oBAAoB,CAACb,UAAU,CAAC;MAE9C,IAAMF,UAAU,GAAGa,OAAO,CAACb,UAAU;MAErCA,UAAU,CAAC7C,OAAO,CAAEgC,SAAS,IAAK;QAChC,IAAML,KAAK,GAAG,IAAI,CAACF,KAAK,CAACG,QAAQ,CAACI,SAAS,CAAC;QAE5C,IAAIL,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEkC,MAAM,EAAE;UACjBd,UAAU,CAACc,MAAM,GAAG,CAAC,GAAGd,UAAU,CAACc,MAAM,EAAE,GAAGlC,KAAK,CAACkC,MAAM,CAAC;QAC7D;QAEA,IAAIlC,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEJ,KAAK,EAAE;UAChBwB,UAAU,CAACxB,KAAK,GAAG,CAAC,GAAGwB,UAAU,CAACxB,KAAK,EAAE,GAAGI,KAAK,CAACJ,KAAK,CAAC;QAC1D;QAEA,IAAII,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEmC,UAAU,EAAE;UACrBf,UAAU,CAACe,UAAU,GAAG,CAAC,GAAGf,UAAU,CAACe,UAAU,EAAE,GAAGnC,KAAK,CAACmC,UAAU,CAAC;QACzE;QAEA,IAAInC,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEoC,WAAW,EAAE;UAAA,IAAAC,qBAAA;UACtBjB,UAAU,CAACgB,WAAW,IAAAC,qBAAA,GAAGjB,UAAU,CAACgB,WAAW,cAAAC,qBAAA,cAAAA,qBAAA,GAAIrC,KAAK,CAACoC,WAAW;QACtE;QAEA,IAAIpC,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEsC,eAAe,EAAE;UAAA,IAAAC,sBAAA;UAC1BnB,UAAU,CAACkB,eAAe,IAAAC,sBAAA,GAAGnB,UAAU,CAACkB,eAAe,cAAAC,sBAAA,cAAAA,sBAAA,GAAIvC,KAAK,CAACsC,eAAe;QAClF;MACF,CAAC,CAAC;MAEFlB,UAAU,CAACc,MAAM,GAAG,CAAC,GAAG,IAAI,CAACrC,YAAY,EAAE,GAAGuB,UAAU,CAACc,MAAM,CAAC;MAEhE,CAAAL,qBAAA,GAAAT,UAAU,CAACoB,UAAU,cAAAX,qBAAA,cAAAA,qBAAA,GAArBT,UAAU,CAACoB,UAAU,GAAKjI,uBAAuB,CAAC6G,UAAU,CAAC;MAC7D,CAAAU,qBAAA,GAAAV,UAAU,CAACqB,SAAS,cAAAX,qBAAA,cAAAA,qBAAA,GAApBV,UAAU,CAACqB,SAAS,GAAKnI,sBAAsB,CAAC8G,UAAU,CAAC;MAE3D,IAAML,SAAS,GAAGlF,qBAAA,CAAKwD,iBAAiB,EAAtB,IAAqB,CAAC,CAAAc,IAAA,CAAtB,IAAI,EAAmBiB,UAAU,CAAC7E,KAAK,EAAEJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,EAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,QAAQ,CAAC;MAEtF+E,UAAU,CAAC7E,KAAK,GAAGwE,SAAS,CAACxE,KAAK;MAClC6E,UAAU,CAAChF,IAAI,GAAG2E,SAAS,CAAC3E,IAAI;MAEhC,IAAI,CAACiF,QAAQ,CAACqB,mBAAmB,CAACtB,UAAU,CAAC;IAC/C,CAAC;IAAAlG,eAAA,oBAEYyB,IAAY,IAAK;MAC5B,IAAMyE,UAAU,GAAG,IAAI,CAACtB,KAAK,CAAC4B,aAAa,CAAC/E,IAAI,CAAC;MACjD,IAAI,CAACyE,UAAU,EAAE;QACf;QACApF,OAAO,CAACC,KAAK,mDAAAiE,MAAA,CAAmDvD,IAAI,cAAW,CAAC;QAChF;MACF;MAEA,IAAIxC,YAAY,CAACiH,UAAU,CAACc,MAAM,CAAC,EAAE;QACnC,IAAI,CAACpC,KAAK,CAAC6C,gBAAgB,CAAChG,IAAI,CAAC;QACjC;MACF;MAEA,IAAI,CAAC0E,QAAQ,CAACuB,qBAAqB,CAACxB,UAAU,CAAC;MAE/C,IAAI,CAACnE,MAAM,CAAC4F,WAAW,CAACzB,UAAU,CAAC;MACnC,IAAI,CAACtB,KAAK,CAAC6C,gBAAgB,CAAChG,IAAI,CAAC;MAEjC,IAAI,CAAC0E,QAAQ,CAACyB,oBAAoB,CAAC1B,UAAU,CAAC;IAChD,CAAC;IAAAlG,eAAA,sBAEc+C,QAAgB,IAAyB;MACtD,OAAO,IAAI,CAAC8E,SAAS,CAAC3H,WAAW,CAAC6C,QAAQ,CAAC;IAC7C,CAAC;IAAA/C,eAAA,oBAEW,CACV+C,QAAgB,EAChB+E,cAAyC,EACzC1G,MAA2B,KACJ;MACvB,IAAM2G,MAAM,GAAGpH,qBAAA,CAAKqD,WAAW,EAAhB,IAAe,CAAC,CAAAiB,IAAA,CAAhB,IAAI,EAAalC,QAAQ,EAAE+E,cAAc,CAAC;MACzD,IAAI,CAACC,MAAM,EAAE;QACX;QACAjH,OAAO,CAACC,KAAK,mDAAAiE,MAAA,CACuCjC,QAAQ,0BAAAiC,MAAA,CAAuBgD,IAAI,CAACC,SAAS,CAACH,cAAc,CAAC,cACjH,CAAC;QACD;MACF;MACA,IAAMvH,UAAsB,GAAAC,aAAA,CAAAA,aAAA,KACvBzB,gBAAgB,CAAC,CAAC;QACrBsC,KAAK,EAAEmE,IAAI,CAACC,GAAG,CAAC;MAAC,GACdrE,MAAM,CACV;MACD2G,MAAM,CAACtH,KAAK,CAACC,IAAI,CAACH,UAAU,CAAC;MAC7B,IAAM6C,QAAQ,GAAG9D,UAAU,CAAC,CAAC;MAC7B,IAAI,CAACsF,KAAK,CAACsD,aAAa,CAAC9E,QAAQ,EAAE7C,UAAU,CAAC;MAE9C,IAAI,CAACsH,SAAS,CAACM,OAAO,CAACpF,QAAQ,EAAEK,QAAQ,CAAC;MAE1C,OAAOA,QAAQ;IACjB,CAAC;IAAApD,eAAA,qBAEY,CAACyB,IAAY,EAAEZ,UAA4C,KAAK;MAC3E,IAAMP,IAAI,GAAG,IAAI,CAACsE,KAAK,CAACwD,aAAa,CAAC3G,IAAI,CAAE;MAC5C,IAAI,CAACnB,IAAI,EAAE;QACT;QACAQ,OAAO,CAACC,KAAK,kDAAAiE,MAAA,CAAkDvD,IAAI,cAAW,CAAC;QAC/E;MACF;MAEAZ,UAAU,CAACP,IAAI,CAAC;IAClB,CAAC;IAAAN,eAAA,mBAEU,CAACyB,IAAY,EAAER,IAA2C,KAAK;MACxE,IAAMX,IAAI,GAAG,IAAI,CAACsE,KAAK,CAACwD,aAAa,CAAC3G,IAAI,CAAC;MAC3C,IAAI,CAACnB,IAAI,EAAE;QACT;QACAQ,OAAO,CAACC,KAAK,gDAAAiE,MAAA,CAAgDvD,IAAI,cAAW,CAAC;QAC7E;MACF;MAEA,IAAI,CAAC0E,QAAQ,CAACkC,cAAc,CAAC/H,IAAI,CAAC;MAElC,IAAMuF,SAAS,GAAGlF,qBAAA,CAAKwD,iBAAiB,EAAtB,IAAqB,CAAC,CAAAc,IAAA,CAAtB,IAAI,EAAmB3E,IAAI,CAACe,KAAK,EAAEJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,EAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,QAAQ,CAAC;MAChFb,IAAI,CAACe,KAAK,GAAGwE,SAAS,CAACxE,KAAK;MAC5Bf,IAAI,CAACY,IAAI,GAAG2E,SAAS,CAAC3E,IAAI;MAE1BZ,IAAI,CAACwF,KAAK,GAAGnH,KAAK,CAACoH,QAAQ;MAE3B,IAAI,CAAC8B,SAAS,CAACvE,UAAU,CAAC7B,IAAI,CAAC;MAE/B,IAAI,CAAC0E,QAAQ,CAACmC,aAAa,CAAChI,IAAI,CAAC;IACnC,CAAC;IAAAN,eAAA,0BAEiB,CAChB+C,QAAgB,EAChB+E,cAAyC,EACzCS,cAAsB,EACtBC,uBAAwC,EACxCC,OAAyE,KACtE;MAAA,IAAAC,qBAAA;MACH,IAAMX,MAAM,GAAGpH,qBAAA,CAAKqD,WAAW,EAAhB,IAAe,CAAC,CAAAiB,IAAA,CAAhB,IAAI,EAAalC,QAAQ,EAAE+E,cAAc,CAAC;MACzD,IAAI,CAACC,MAAM,EAAE;QACX;QACAjH,OAAO,CAACC,KAAK,yDAAAiE,MAAA,CAC6CjC,QAAQ,0BAAAiC,MAAA,CAAuBgD,IAAI,CAACC,SAAS,CAACH,cAAc,CAAC,cACvH,CAAC;QACD;MACF;MAEA,IAAM9F,MAAM,GAAG,OAAOwG,uBAAuB,KAAK,QAAQ;MAC1D,IAAMpG,aAAa,IAAAsG,qBAAA,GAAGD,OAAO,CAACrG,aAAa,cAAAsG,qBAAA,cAAAA,qBAAA,GAAK1G,MAAM,GAAGtD,OAAO,CAAC8J,uBAAuB,CAAC,GAAGlH,SAAU;MACtG,IAAMqH,kBAAkB,GAAGpJ,uBAAuB,CAAC;QACjD4C,WAAW,EAAEsG,OAAO,CAACtG,WAAW;QAChCC;MACF,CAAC,CAAC;MAEF,IAAIJ,MAAM,EAAE;QACV,IAAI,CAACD,MAAM,CAACM,uBAAuB,CAACsG,kBAAkB,EAAEH,uBAAuB,CAAC;MAClF,CAAC,MAAM;QACL,IAAI,CAACzG,MAAM,CAACQ,eAAe,CAACoG,kBAAkB,EAAEH,uBAAuB,CAAC;MAC1E;MAEA,IAAM1G,UAAsB,GAAG;QAC7B8G,IAAI,EAAEL,cAAc;QACpBM,MAAM,EAAEF,kBAAkB;QAC1BvD,IAAI,EAAEqD,OAAO,CAACtG;MAChB,CAAC;MAED,IAAIsG,OAAO,CAACK,UAAU,EAAE;QACtB,IAAM;UAAEC,SAAS,GAAGvD,IAAI,CAACC,GAAG,CAAC;QAAE,CAAC,GAAGgD,OAAO;QAC1CV,MAAM,CAACtH,KAAK,CAACC,IAAI,CAAC;UAChBkI,IAAI,EAAEL,cAAc;UACpBS,WAAW,EAAE,CAAClH,UAAU,CAAC;UACzBT,KAAK,EAAE0H,SAAS;UAChB7H,IAAI,EAAE6H;QACR,CAAe,CAAC;MAClB,CAAC,MAAM;QACLhB,MAAM,CAACiB,WAAW,CAACtI,IAAI,CAACoB,UAAU,CAAC;MACrC;IACF,CAAC;IAAA9B,eAAA,+BAEsB,MAAM;MAC3B,IAAI,CAAC,IAAI,CAACwE,eAAe,EAAE;QACzB;MACF;MAEA,IAAI,CAACzC,MAAM,CAACkH,oBAAoB,CAAC,IAAI,CAACzE,eAAe,CAAC;IACxD,CAAC;IAAAxE,eAAA,qCAE4B,MAAM;MACjC,IAAI,CAAC,IAAI,CAACyE,UAAU,EAAE;QACpB;MACF;MAEA,IAAMyE,oBAAoB,GAAG,IAAI,CAACzE,UAAU,CAAC0E,GAAG,CAAEC,CAAC,IAAK;QACtD,IAAIA,CAAC,CAACC,YAAY,YAAYC,MAAM,EAAE;UACpCF,CAAC,CAACC,YAAY,GAAGD,CAAC,CAACC,YAAY,CAACR,MAAM;QACxC;QAEA,IAAIO,CAAC,CAACG,UAAU,YAAYD,MAAM,EAAE;UAClCF,CAAC,CAACG,UAAU,GAAGH,CAAC,CAACG,UAAU,CAACV,MAAM;QACpC;QAEA,OAAOO,CAAC;MACV,CAAC,CAAC;MAEF,IAAI,CAACrH,MAAM,CAACyH,0BAA0B,CAACN,oBAAoB,CAAC;IAC9D,CAAC;IAAAlJ,eAAA,+BAEsB,CAAC+C,QAAgB,EAAE0G,QAA0B,KAAK;MACvEA,QAAQ,CAACtG,OAAO,CAAEuG,OAAO,IAAK;QAC5B,QAAQA,OAAO,CAACtE,IAAI;UAClB,KAAK,UAAU;YACbzE,qBAAA,CAAK+C,sBAAsB,EAA3B,IAA0B,CAAC,CAAAuB,IAAA,CAA3B,IAAI,EAAwBlC,QAAQ,EAAE2G,OAAO,CAACC,IAAI;YAClD;UACF,KAAK,eAAe;YAClBhJ,qBAAA,CAAKgD,0BAA0B,EAA/B,IAA8B,CAAC,CAAAsB,IAAA,CAA/B,IAAI,EAA4BlC,QAAQ,EAAE2G,OAAO,CAACC,IAAI;YACtD;UACF,KAAK,YAAY;YACfhJ,qBAAA,CAAKiD,uBAAuB,EAA5B,IAA2B,CAAC,CAAAqB,IAAA,CAA5B,IAAI,EAAyBlC,QAAQ,EAAE2G,OAAO,CAACC,IAAI;YACnD;UACF,KAAK,WAAW;YACdhJ,qBAAA,CAAKkD,sBAAsB,EAA3B,IAA0B,CAAC,CAAAoB,IAAA,CAA3B,IAAI,EAAwBlC,QAAQ,EAAE2G,OAAO,CAACC,IAAI;YAClD;UACF,KAAK,oBAAoB;YACvBhJ,qBAAA,CAAKmD,+BAA+B,EAApC,IAAmC,CAAC,CAAAmB,IAAA,CAApC,IAAI,EAAiClC,QAAQ,EAAE2G,OAAO,CAACC,IAAI;YAC3D;UACF,KAAK,iBAAiB;YACpBhJ,qBAAA,CAAKoD,4BAA4B,EAAjC,IAAgC,CAAC,CAAAkB,IAAA,CAAjC,IAAI,EAA8BlC,QAAQ,EAAE2G,OAAO,CAACC,IAAI;YACxD;UACF;YACE;YACA7I,OAAO,CAACC,KAAK,sDAAAiE,MAAA,CAAsDgD,IAAI,CAACC,SAAS,CAACyB,OAAO,CAAC,CAAE,CAAC;YAC7F;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAEDzJ,0BAAA,OAAAyD,sBAAsB,EAAG,CAACX,QAAgB,EAAE2G,OAAuC,KAAK;MACtF;MACA,IAAMrE,aAAa,GAAG,IAAI,CAACT,KAAK,CAACgF,uBAAuB,CAAC7G,QAAQ,CAAC;MAClE,IAAM;QAAE2B,KAAK;QAAEsC,MAAM;QAAEC,UAAU;QAAE4C,WAAW;QAAEvC,UAAU;QAAEC,SAAS;QAAEL,WAAW;QAAEE;MAAgB,CAAC,GAAGsC,OAAO;MAE/G,IAAIrE,aAAa,EAAE;QACjB,IAAIwE,WAAW,EAAE;UACf,IAAI,CAACC,aAAa,CAAC/G,QAAQ,EAAG3B,MAAM,IAAK;YACvCA,MAAM,CAACwH,IAAI,GAAGiB,WAAW;UAC3B,CAAC,CAAC;QACJ;QAEA,IAAItC,SAAS,EAAE;UACb;UACAzG,OAAO,CAACC,KAAK,CAAC,iDAAiD,CAAC;QAClE;QACA,IAAIuG,UAAU,EAAE;UACd;UACAxG,OAAO,CAACC,KAAK,CAAC,kDAAkD,CAAC;QACnE;QAEA,IAAI2D,KAAK,IAAIsC,MAAM,IAAIC,UAAU,IAAIC,WAAW,IAAIE,eAAe,EAAE;UACnE;UACA;UACA;UACA,IAAI/B,aAAa,CAACD,IAAI,KAAK,OAAO,EAAE;YAClC;YACAtE,OAAO,CAACC,KAAK,CAAC,2DAA2D,CAAC;YAC1E;UACF;UAEA,IAAI,CAACgJ,WAAW,CAAC1E,aAAa,CAACF,SAAS,EAAGL,KAAK,IAAK;YACnD,IAAIJ,KAAK,EAAE;cACTI,KAAK,CAACJ,KAAK,GAAG,CAAC,GAAGI,KAAK,CAACJ,KAAK,EAAE,IAAI,IAAI,CAACsF,UAAU,GAAG7K,WAAW,CAAC,IAAI,CAAC6K,UAAU,EAAEtF,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC;YACpG;YACA,IAAIsC,MAAM,EAAE;cACVlC,KAAK,CAACkC,MAAM,GAAG,CAAC,GAAGlC,KAAK,CAACkC,MAAM,EAAE,GAAGA,MAAM,CAAC;YAC7C;YACA,IAAIC,UAAU,EAAE;cACdnC,KAAK,CAACmC,UAAU,GAAG,CAAC,GAAGnC,KAAK,CAACmC,UAAU,EAAE,GAAGA,UAAU,CAAC;YACzD;YACA,IAAIC,WAAW,EAAE;cACfpC,KAAK,CAACoC,WAAW,GAAGA,WAAW;YACjC;YACA,IAAIE,eAAe,EAAE;cACnBtC,KAAK,CAACsC,eAAe,GAAGA,eAAe;YACzC;UACF,CAAC,CAAC;QACJ;QAEA;MACF;MAEA,IAAI,CAAC6C,UAAU,CAAClH,QAAQ,EAAG3B,MAAM,IAAK;QACpC,IAAIsD,KAAK,EAAE;UACTtD,MAAM,CAACsD,KAAK,GAAG,CAAC,GAAGtD,MAAM,CAACsD,KAAK,EAAE,IAAI,IAAI,CAACsF,UAAU,GAAG7K,WAAW,CAAC,IAAI,CAAC6K,UAAU,EAAEtF,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC;QACtG;QACA,IAAIsC,MAAM,EAAE;UACV5F,MAAM,CAAC4F,MAAM,GAAG,CAAC,GAAG5F,MAAM,CAAC4F,MAAM,EAAE,GAAGA,MAAM,CAAC;QAC/C;QACA,IAAIC,UAAU,EAAE;UACd7F,MAAM,CAAC6F,UAAU,GAAG,CAAC,GAAG7F,MAAM,CAAC6F,UAAU,EAAE,GAAGA,UAAU,CAAC;QAC3D;QACA,IAAI4C,WAAW,EAAE;UACfzI,MAAM,CAACwH,IAAI,GAAGiB,WAAW;QAC3B;QACA,IAAIvC,UAAU,EAAE;UACdlG,MAAM,CAACkG,UAAU,GAAGA,UAAU;QAChC;QACA,IAAIC,SAAS,EAAE;UACbnG,MAAM,CAACmG,SAAS,GAAGA,SAAS;QAC9B;QACA,IAAIL,WAAW,EAAE;UACf9F,MAAM,CAAC8F,WAAW,GAAGA,WAAW;QAClC;QACA,IAAIE,eAAe,EAAE;UACnBhG,MAAM,CAACgG,eAAe,GAAGA,eAAe;QAC1C;MACF,CAAC,CAAC;IACJ,CAAC;IAEDnH,0BAAA,OAAA0D,0BAA0B,EAAG,CAACZ,QAAgB,EAAE2G,OAA2C,KAAK;MAC9F,IAAMtG,QAAQ,GAAG,IAAI,CAAClD,WAAW,CAAC6C,QAAQ,CAAC;MAC3C,IAAI,CAACK,QAAQ,EAAE;QACb;QACAtC,OAAO,CAACC,KAAK,CAAC,4DAA4D,CAAC;QAC3E;MACF;MACA,IAAM;QAAE6H,IAAI;QAAE3B;MAAW,CAAC,GAAGyC,OAAO;MACpC,IAAI,CAAC9I,UAAU,CAACwC,QAAQ,EAAG7C,UAAU,IAAK;QACxC,IAAIqI,IAAI,EAAE;UACRrI,UAAU,CAACqI,IAAI,GAAGA,IAAI;QACxB;QACA,IAAI3B,UAAU,EAAE;UACd1G,UAAU,CAAC0G,UAAU,GAAG,CAAC,GAAG1G,UAAU,CAAC0G,UAAU,EAAE,GAAGA,UAAU,CAAC;QACnE;MACF,CAAC,CAAC;IACJ,CAAC;IAEDhH,0BAAA,OAAA2D,uBAAuB,EAAG,CAACb,QAAgB,EAAE2G,OAAwC,KAAK;MACxF,IAAI,CAACrJ,SAAS,CAAC0C,QAAQ,EAAEzB,SAAS,EAAAd,aAAA,KAAOkJ,OAAO,CAAE,CAAC;IACrD,CAAC;IAEDzJ,0BAAA,OAAA4D,sBAAsB,EAAG,CAACd,QAAgB,EAAE2G,OAAuC,KAAK;MACtF,IAAMtG,QAAQ,GAAG,IAAI,CAAClD,WAAW,CAAC6C,QAAQ,CAAC;MAC3C,IAAI,CAACK,QAAQ,EAAE;QACb;QACAtC,OAAO,CAACC,KAAK,CAAC,wDAAwD,CAAC;QACvE;MACF;MACA,IAAI,CAACH,UAAU,CAACwC,QAAQ,EAAGhC,MAAM,IAAK;QACpC,IAAIsI,OAAO,CAACQ,MAAM,IAAI,CAAC9I,MAAM,CAAC8I,MAAM,EAAE;UACpC9I,MAAM,CAAC8I,MAAM,GAAGR,OAAO,CAACQ,MAAM;QAChC;QACA,IAAIR,OAAO,CAACS,aAAa,EAAE;UACzB/I,MAAM,CAAC+I,aAAa,GAAA3J,aAAA,CAAAA,aAAA,KAAQY,MAAM,CAAC+I,aAAa,GAAKT,OAAO,CAACS,aAAa,CAAE;QAC9E;MACF,CAAC,CAAC;MACF,IAAI,CAACnJ,QAAQ,CAACoC,QAAQ,EAAE;QAAElC,IAAI,EAAEwI,OAAO,CAACxI;MAAK,CAAC,CAAC;IACjD,CAAC;IAEDjB,0BAAA,OAAA6D,+BAA+B,EAAG,CAACf,QAAgB,EAAE2G,OAAgD,KAAK;MACxG,IAAI,CAACnH,eAAe,CAACQ,QAAQ,EAAEzB,SAAS,EAAEoI,OAAO,CAACd,IAAI,EAAEwB,MAAM,CAACC,IAAI,CAACX,OAAO,CAACY,OAAO,EAAEZ,OAAO,CAACa,QAAQ,CAAC,EAAE;QACtGA,QAAQ,EAAEb,OAAO,CAACa,QAAQ;QAC1BpI,WAAW,EAAEuH,OAAO,CAACvH,WAAW;QAChCC,aAAa,EAAEsH,OAAO,CAACtH,aAAa;QACpC0G,UAAU,EAAEY,OAAO,CAACZ,UAAU;QAC9BC,SAAS,EAAEW,OAAO,CAACX;MACrB,CAAC,CAAC;IACJ,CAAC;IAED9I,0BAAA,OAAA8D,4BAA4B,EAAG,CAAChB,QAAgB,EAAE2G,OAA6C,KAAK;MAClG,IAAI,CAACnH,eAAe,CAACQ,QAAQ,EAAEzB,SAAS,EAAEoI,OAAO,CAACd,IAAI,EAAEc,OAAO,CAACjL,IAAI,EAAE;QACpE0D,WAAW,EAAEuH,OAAO,CAACvH,WAAW;QAChCC,aAAa,EAAEsH,OAAO,CAACtH,aAAa;QACpC0G,UAAU,EAAEY,OAAO,CAACZ,UAAU;QAC9BC,SAAS,EAAEW,OAAO,CAACX;MACrB,CAAC,CAAC;IACJ,CAAC;IAED9I,0BAAA,OAAA+D,WAAW,EAAG,CACZjB,QAAgB,EAChB+E,cAAyC,KACe;MACxD,IAAM0C,IAAI,GAAG,IAAI,CAAC5F,KAAK,CAAC6F,gBAAgB,CAAC1H,QAAQ,CAAC;MAClD,IAAI,CAACyH,IAAI,EAAE;QACT;MACF;MAEA,IAAI1C,cAAc,KAAK,IAAI,EAAE;QAC3B,OAAO0C,IAAI;MACb,CAAC,MAAM,IAAI1C,cAAc,KAAKxG,SAAS,EAAE;QACvC,IAAM8B,SAAQ,GAAG,IAAI,CAAClD,WAAW,CAAC6C,QAAQ,CAAC;QAC3C,OAAOK,SAAQ,GAAG,IAAI,CAACwB,KAAK,CAACwD,aAAa,CAAChF,SAAQ,CAAC,GAAGoH,IAAI;MAC7D,CAAC,MAAM;QACL,OAAO,IAAI,CAAC5F,KAAK,CAACwD,aAAa,CAACN,cAAc,CAAC;MACjD;IACF,CAAC;IAED7H,0BAAA,OAAAgE,qBAAqB,EAAGyG,KAAA,IAAoC;MAAA,IAAnC;QAAEhF,QAAQ;QAAEW;MAAiB,CAAC,GAAAqE,KAAA;MACrD,IAAMC,eAAe,GAAG,IAAIC,GAAG,CAAS,CAAC;MACzC,IAAIvE,KAAK,CAAC5D,MAAM,EAAE;QAChB,KAAK,IAAM6C,cAAc,IAAII,QAAQ,EAAE;UACrC,IAAI,CAACiF,eAAe,CAACE,GAAG,CAACvF,cAAc,CAAC7D,IAAI,CAAC,EAAE;YAC7Cd,qBAAA,CAAKuD,eAAe,EAApB,IAAmB,CAAC,CAAAe,IAAA,CAApB,IAAI,EAAiBoB,KAAK,EAAEf,cAAc;YAC1C,IAAI,CAACV,KAAK,CAACkG,mBAAmB,CAACxF,cAAc,CAAC7D,IAAI,CAAC;YACnDkJ,eAAe,CAACI,GAAG,CAACzF,cAAc,CAAC7D,IAAI,CAAC;UAC1C;QACF;MACF;IACF,CAAC;IAEDxB,0BAAA,OAAAiE,eAAe,EAAG,CAACmC,KAAe,EAAEf,cAAoC,KAAK;MAC3E,IAAMK,OAAO,GAAGL,cAAc,CAAC7B,KAAK;MACpC,IAAMuH,OAAO,GAAG1F,cAAc,CAACF,IAAI,KAAK,QAAQ,GAAG,CAACE,cAAc,CAAC7B,KAAK,CAAC,GAAG,EAAE;MAC9E,IAAMwH,MAAM,GAAG3F,cAAc,CAACF,IAAI,KAAK,OAAO,GAAG,CAACE,cAAc,CAAC7B,KAAK,CAAC,GAAG,EAAE;MAC5E,IAAI,CAAC1B,MAAM,CAACmJ,UAAU,CAAC;QACrBzJ,IAAI,EAAE6D,cAAc,CAAC7D,IAAI;QACzBmH,IAAI,EAAEjD,OAAO,CAACiD,IAAI;QAClBuC,QAAQ,EAAE,CAAC,GAAG,IAAIP,GAAG,CAACvE,KAAK,CAAC,CAAC;QAC7B2E,OAAO;QACPC;MACF,CAAC,CAAC;IACJ,CAAC;IAEDhL,0BAAA,OAAAkE,iBAAiB,EAAG,CAClB9C,KAAc,EACdH,IAAa,EACbC,QAAiB,KAId;MACH,IAAMC,MAAyC,GAAG;QAAEC,KAAK;QAAEH;MAAK,CAAC;MACjE,IAAIC,QAAQ,EAAE;QACZ,IAAMiK,kBAAkB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEnK,QAAQ,CAAC;QAChD,IAAIC,MAAM,CAACF,IAAI,KAAKI,SAAS,EAAE;UAC7BF,MAAM,CAACC,KAAK,GAAGD,MAAM,CAACF,IAAI,GAAGkK,kBAAkB;QACjD,CAAC,MAAM,IAAIhK,MAAM,CAACC,KAAK,KAAKC,SAAS,EAAE;UACrCF,MAAM,CAACF,IAAI,GAAGE,MAAM,CAACC,KAAK,GAAG+J,kBAAkB;QACjD,CAAC,MAAM;UACLhK,MAAM,CAACF,IAAI,GAAGsE,IAAI,CAACC,GAAG,CAAC,CAAC;UACxBrE,MAAM,CAACC,KAAK,GAAGD,MAAM,CAACF,IAAI,GAAGkK,kBAAkB;QACjD;MACF,CAAC,MAAM;QACL,IAAIhK,MAAM,CAACF,IAAI,KAAKI,SAAS,EAAE;UAC7BF,MAAM,CAACF,IAAI,GAAGsE,IAAI,CAACC,GAAG,CAAC,CAAC;QAC1B;QACA,IAAIrE,MAAM,CAACC,KAAK,KAAKC,SAAS,EAAE;UAC9BF,MAAM,CAACC,KAAK,GAAGD,MAAM,CAACF,IAAI;QAC5B;MACF;MACA,OAAO;QACLG,KAAK,EAAED,MAAM,CAACC,KAAK,GAAGgK,IAAI,CAACE,KAAK,CAACnK,MAAM,CAACC,KAAK,CAAC,GAAGC,SAAS;QAC1DJ,IAAI,EAAEE,MAAM,CAACF,IAAI,GAAGmK,IAAI,CAACE,KAAK,CAACnK,MAAM,CAACF,IAAI,CAAC,GAAGI;MAChD,CAAC;IACH,CAAC;IAhlBC,IAAI,CAACS,MAAM,GAAGvC,aAAa,CAACuC,MAAM,CAAC;IACnC,IAAI,CAACoE,QAAQ,GAAG,IAAItH,QAAQ,CAAC;MAAE0F;IAAU,CAAC,CAAC;IAC3C,IAAI,CAACE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACD,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACwF,UAAU,GAAGtF,MAAK;IAEvB,IAAI8G,KAAK,CAACC,OAAO,CAAC9G,YAAY,CAAC,EAAE;MAC/B,IAAI,CAACA,YAAY,GAAGA,YAAY;IAClC,CAAC,MAAM,IAAI+G,MAAM,CAACC,IAAI,CAAChH,YAAY,CAAC,CAAClC,MAAM,EAAE;MAC3C,IAAI,CAACkC,YAAY,GAAG+G,MAAM,CAACE,OAAO,CAACjH,YAAY,CAAC,CAACkH,OAAO,CAACC,KAAA,IAAmB;QAAA,IAAlB,CAAClD,IAAI,EAAEnF,KAAK,CAAC,GAAAqI,KAAA;QACrE,IAAIN,KAAK,CAACC,OAAO,CAAChI,KAAK,CAAC,EAAE;UACxB,OAAOA,KAAK,CAAC0F,GAAG,CAAE4C,CAAC,KAAM;YAAEnD,IAAI;YAAEnF,KAAK,EAAEsI;UAAE,CAAC,CAAC,CAAC;QAC/C;QAEA,OAAO;UACLnD,IAAI;UACJnF;QACF,CAAC;MACH,CAAC,CAAC;IACJ;EACF;AA6jBF", "ignoreList": []}