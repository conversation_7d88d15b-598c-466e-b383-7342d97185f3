{"version": 3, "file": "loader.js", "names": ["FileSystemWriter", "InMemoryWriter", "MessageWriter", "wellKnownWriters", "resolveWriter", "value", "createWriter", "Array", "slice", "nameOr<PERSON>ath", "_getKnownWriterCtor", "args", "arguments", "length", "undefined", "ctorOrInstance", "getKnownWriterCtor", "requireWriterCtor", "name", "modulePath", "require"], "sources": ["../../../../../src/sdk/reporter/writer/loader.ts"], "sourcesContent": ["import type { Writer, WriterDescriptor } from \"../types.js\";\nimport { FileSystemWriter } from \"./FileSystemWriter.js\";\nimport { InMemoryWriter } from \"./InMemoryWriter.js\";\nimport { MessageWriter } from \"./MessageWriter.js\";\n\ntype WellKnownWriters = {\n  [key: string]: (new (...args: readonly any[]) => Writer) | undefined;\n};\n\nconst wellKnownWriters: WellKnownWriters = {\n  InMemoryWriter: InMemoryWriter,\n  FileSystemWriter: FileSystemWriter,\n  MessageWriter: MessageWriter,\n};\n\nexport const resolveWriter = (value: Writer | WriterDescriptor): Writer => {\n  if (typeof value === \"string\") {\n    return createWriter(value);\n  } else if (value instanceof Array) {\n    return createWriter(value[0], value.slice(1));\n  }\n  return value;\n};\n\nconst createWriter = (nameOrPath: string, args: readonly unknown[] = []) => {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports,@typescript-eslint/no-var-requires\n  const ctorOrInstance = getKnownWriterCtor(nameOrPath) ?? requireWriterCtor(nameOrPath);\n  return typeof ctorOrInstance === \"function\" ? new ctorOrInstance(...args) : ctorOrInstance;\n};\n\nconst getKnownWriterCtor = (name: string) =>\n  (wellKnownWriters as unknown as { [key: string]: Writer | undefined })[name];\n\nconst requireWriterCtor = (modulePath: string): (new (...args: readonly unknown[]) => Writer) | Writer => {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports,@typescript-eslint/no-var-requires\n  return require(modulePath);\n};\n"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,aAAa,QAAQ,oBAAoB;AAMlD,IAAMC,gBAAkC,GAAG;EACzCF,cAAc,EAAEA,cAAc;EAC9BD,gBAAgB,EAAEA,gBAAgB;EAClCE,aAAa,EAAEA;AACjB,CAAC;AAED,OAAO,IAAME,aAAa,GAAIC,KAAgC,IAAa;EACzE,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOC,YAAY,CAACD,KAAK,CAAC;EAC5B,CAAC,MAAM,IAAIA,KAAK,YAAYE,KAAK,EAAE;IACjC,OAAOD,YAAY,CAACD,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/C;EACA,OAAOH,KAAK;AACd,CAAC;AAED,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIG,UAAkB,EAAoC;EAAA,IAAAC,mBAAA;EAAA,IAAlCC,IAAwB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACrE;EACA,IAAMG,cAAc,IAAAL,mBAAA,GAAGM,kBAAkB,CAACP,UAAU,CAAC,cAAAC,mBAAA,cAAAA,mBAAA,GAAIO,iBAAiB,CAACR,UAAU,CAAC;EACtF,OAAO,OAAOM,cAAc,KAAK,UAAU,GAAG,IAAIA,cAAc,CAAC,GAAGJ,IAAI,CAAC,GAAGI,cAAc;AAC5F,CAAC;AAED,IAAMC,kBAAkB,GAAIE,IAAY,IACrCf,gBAAgB,CAAsDe,IAAI,CAAC;AAE9E,IAAMD,iBAAiB,GAAIE,UAAkB,IAA6D;EACxG;EACA,OAAOC,OAAO,CAACD,UAAU,CAAC;AAC5B,CAAC", "ignoreList": []}