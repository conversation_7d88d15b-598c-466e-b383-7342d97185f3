import { expect, Locator } from '@playwright/test';
import { logger } from './logger';

/**
 * Custom matchers for enhanced assertions
 */
export class CustomMatchers {
  /**
   * Wait for element to be visible within specified timeout
   */
  static async toBeVisibleWithinTimeout(locator: Locator, timeout: number = 30000): Promise<void> {
    try {
      await locator.waitFor({ state: 'visible', timeout });
      await expect(locator).toBeVisible();
      logger.assertion(`✅ Element is visible within ${timeout}ms`);
    } catch (error) {
      logger.error(`❌ Element not visible within ${timeout}ms: ${error}`);
      throw error;
    }
  }

  /**
   * Check if element text contains specified text
   */
  static async toHaveTextContaining(locator: Locator, text: string): Promise<void> {
    try {
      const elementText = await locator.textContent();
      if (!elementText || !elementText.includes(text)) {
        throw new Error(`Expected element to contain text "${text}", but got "${elementText}"`);
      }
      logger.assertion(`✅ Element contains text: "${text}"`);
    } catch (error) {
      logger.error(`❌ Element does not contain expected text: ${error}`);
      throw error;
    }
  }

  /**
   * Wait for element to be enabled within specified timeout
   */
  static async toBeEnabledWithinTimeout(locator: Locator, timeout: number = 30000): Promise<void> {
    try {
      await locator.waitFor({ state: 'visible', timeout });
      await expect(locator).toBeEnabled();
      logger.assertion(`✅ Element is enabled within ${timeout}ms`);
    } catch (error) {
      logger.error(`❌ Element not enabled within ${timeout}ms: ${error}`);
      throw error;
    }
  }

  /**
   * Check if element has specific attribute value
   */
  static async toHaveAttributeValue(locator: Locator, attribute: string, value: string): Promise<void> {
    try {
      await expect(locator).toHaveAttribute(attribute, value);
      logger.assertion(`✅ Element has attribute ${attribute}="${value}"`);
    } catch (error) {
      logger.error(`❌ Element does not have expected attribute value: ${error}`);
      throw error;
    }
  }

  /**
   * Check if element has specific CSS class
   */
  static async toHaveClass(locator: Locator, className: string): Promise<void> {
    try {
      await expect(locator).toHaveClass(new RegExp(className));
      logger.assertion(`✅ Element has CSS class: ${className}`);
    } catch (error) {
      logger.error(`❌ Element does not have expected CSS class: ${error}`);
      throw error;
    }
  }

  /**
   * Check if element count matches expected number
   */
  static async toHaveCount(locator: Locator, count: number): Promise<void> {
    try {
      await expect(locator).toHaveCount(count);
      logger.assertion(`✅ Element count matches expected: ${count}`);
    } catch (error) {
      logger.error(`❌ Element count does not match expected: ${error}`);
      throw error;
    }
  }

  /**
   * Check if element has specific value
   */
  static async toHaveValue(locator: Locator, value: string): Promise<void> {
    try {
      await expect(locator).toHaveValue(value);
      logger.assertion(`✅ Element has expected value: ${value}`);
    } catch (error) {
      logger.error(`❌ Element does not have expected value: ${error}`);
      throw error;
    }
  }

  /**
   * Check if element is checked (for checkboxes/radio buttons)
   */
  static async toBeChecked(locator: Locator): Promise<void> {
    try {
      await expect(locator).toBeChecked();
      logger.assertion('✅ Element is checked');
    } catch (error) {
      logger.error(`❌ Element is not checked: ${error}`);
      throw error;
    }
  }

  /**
   * Check if element is not checked (for checkboxes/radio buttons)
   */
  static async toBeUnchecked(locator: Locator): Promise<void> {
    try {
      await expect(locator).not.toBeChecked();
      logger.assertion('✅ Element is unchecked');
    } catch (error) {
      logger.error(`❌ Element is checked when expected to be unchecked: ${error}`);
      throw error;
    }
  }

  /**
   * Check if element is focused
   */
  static async toBeFocused(locator: Locator): Promise<void> {
    try {
      await expect(locator).toBeFocused();
      logger.assertion('✅ Element is focused');
    } catch (error) {
      logger.error(`❌ Element is not focused: ${error}`);
      throw error;
    }
  }

  /**
   * Check if element is editable
   */
  static async toBeEditable(locator: Locator): Promise<void> {
    try {
      await expect(locator).toBeEditable();
      logger.assertion('✅ Element is editable');
    } catch (error) {
      logger.error(`❌ Element is not editable: ${error}`);
      throw error;
    }
  }

  /**
   * Check if element is empty
   */
  static async toBeEmpty(locator: Locator): Promise<void> {
    try {
      await expect(locator).toBeEmpty();
      logger.assertion('✅ Element is empty');
    } catch (error) {
      logger.error(`❌ Element is not empty: ${error}`);
      throw error;
    }
  }

  /**
   * Check if page has specific title
   */
  static async toHaveTitle(page: any, title: string | RegExp): Promise<void> {
    try {
      await expect(page).toHaveTitle(title);
      logger.assertion(`✅ Page has expected title: ${title}`);
    } catch (error) {
      logger.error(`❌ Page does not have expected title: ${error}`);
      throw error;
    }
  }

  /**
   * Check if page has specific URL
   */
  static async toHaveURL(page: any, url: string | RegExp): Promise<void> {
    try {
      await expect(page).toHaveURL(url);
      logger.assertion(`✅ Page has expected URL: ${url}`);
    } catch (error) {
      logger.error(`❌ Page does not have expected URL: ${error}`);
      throw error;
    }
  }

  /**
   * Wait for network response with specific URL pattern
   */
  static async toReceiveResponse(page: any, urlPattern: string | RegExp, timeout: number = 30000): Promise<void> {
    try {
      const response = await page.waitForResponse(urlPattern, { timeout });
      if (!response.ok()) {
        throw new Error(`Response not OK: ${response.status()} ${response.statusText()}`);
      }
      logger.assertion(`✅ Received successful response for: ${urlPattern}`);
    } catch (error) {
      logger.error(`❌ Did not receive expected response: ${error}`);
      throw error;
    }
  }

  /**
   * Check if element appears within viewport
   */
  static async toBeInViewport(locator: Locator): Promise<void> {
    try {
      await expect(locator).toBeInViewport();
      logger.assertion('✅ Element is in viewport');
    } catch (error) {
      logger.error(`❌ Element is not in viewport: ${error}`);
      throw error;
    }
  }
}
