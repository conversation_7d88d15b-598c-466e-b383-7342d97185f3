{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:50:49:5049"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:50:49:5049"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:50:49:5049"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:50:49:5049"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:51:19:5119"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:51:19:5119"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:51:19:5119"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:51:19:5119"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:52:17:5217"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:52:18:5218"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:52:18:5218"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:52:18:5218"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-01: Login page loads","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"error","message":"❌ Scenario failed: LGI-01: Login page loads","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-01:-Login-page-loads-2025-08-12T08-52-29-873Z.png","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-01:-Login-page-loads-2025-08-12T08-52-29-873Z.png","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-01: Login page loads","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page is properly loaded","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"🔸 STEP: Verifying logo is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ ASSERTION: ✅ Logo is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🔸 STEP: Verifying page title is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ ASSERTION: ✅ Page title is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🔸 STEP: Verifying email input is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ ASSERTION: ✅ Email input is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🔸 STEP: Verifying password input is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ ASSERTION: ✅ Password input is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🔸 STEP: Verifying submit button is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ ASSERTION: ✅ Submit button is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ Scenario passed: LGI-01: Login page loads","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:57:19:5719"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:57:19:5719"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:57:19:5719"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:57:19:5719"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:57:30:5730"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:57:30:5730"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-01: Login page loads","timestamp":"2025-08-12 15:57:30:5730"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:57:30:5730"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:57:30:5730"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:57:30:5730"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:57:30:5730"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:57:30:5730"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:57:30:5730"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@a4362d02a3a2caeb3ccfe3c4db5f01da\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@a4362d02a3a2caeb3ccfe3c4db5f01da\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page is properly loaded","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🔸 STEP: Verifying logo is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@a4362d02a3a2caeb3ccfe3c4db5f01da\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ ASSERTION: ✅ Logo is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🔸 STEP: Verifying page title is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@a4362d02a3a2caeb3ccfe3c4db5f01da\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ ASSERTION: ✅ Page title is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🔸 STEP: Verifying email input is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@a4362d02a3a2caeb3ccfe3c4db5f01da\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ ASSERTION: ✅ Email input is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🔸 STEP: Verifying password input is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@a4362d02a3a2caeb3ccfe3c4db5f01da\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ ASSERTION: ✅ Password input is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🔸 STEP: Verifying submit button is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@a4362d02a3a2caeb3ccfe3c4db5f01da\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@64b5957b3749fcd883b3e3236637b983\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ ASSERTION: ✅ Submit button is visible","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Scenario passed: LGI-01: Login page loads","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-02: User can login with valid credentials","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:57:35:5735"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@be2444e12be2f94fdc52a335cf549a06\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"🔸 STEP: Entering valid credentials","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@be2444e12be2f94fdc52a335cf549a06\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"⌨️ Input filled with: vietnam5963","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"🎯 ACTION: Valid credentials entered","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@be2444e12be2f94fdc52a335cf549a06\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@4bfd0bd148caf5b3951b2c2722359edd\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"🖱️ Element clicked","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"🎯 ACTION: Login form submitted","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"🎯 ACTION: Login form submitted","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"info","message":"🔸 STEP: Verifying redirect to dashboard or home","timestamp":"2025-08-12 15:57:38:5738"}
{"level":"error","message":"❌ Scenario failed: LGI-02: User can login with valid credentials","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-02:-User-can-login-with-valid-credentials-2025-08-12T08-57-43-216Z.png","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-02:-User-can-login-with-valid-credentials-2025-08-12T08-57-43-216Z.png","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-03: Password empty shows required message","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@128c4532ecfcb14f04ca08399766c273\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"🔸 STEP: Entering valid email","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@128c4532ecfcb14f04ca08399766c273\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"🎯 ACTION: Valid email entered","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"🔸 STEP: Leaving password field empty","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@128c4532ecfcb14f04ca08399766c273\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"⌨️ Input filled with: ","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"🎯 ACTION: Password field left empty","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@128c4532ecfcb14f04ca08399766c273\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@1642bfb01a32c166d1b6dcaf0137fe48\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:45:5745"}
{"level":"error","message":"❌ Scenario failed: LGI-03: Password empty shows required message","timestamp":"2025-08-12 15:57:50:5750"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-03:-Password-empty-shows-required-message-2025-08-12T08-57-50-965Z.png","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-03:-Password-empty-shows-required-message-2025-08-12T08-57-50-965Z.png","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-04: Password shorter than 6 shows min-length message","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:57:51:5751"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@ce6e9e71832d85618d3ccc9ed30e4fa0\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"🔸 STEP: Entering valid email","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@ce6e9e71832d85618d3ccc9ed30e4fa0\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"🎯 ACTION: Valid email entered","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"🔸 STEP: Entering password: 12345","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@ce6e9e71832d85618d3ccc9ed30e4fa0\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"⌨️ Input filled with: 12345","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"🎯 ACTION: Password entered","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@ce6e9e71832d85618d3ccc9ed30e4fa0\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@30d9fe29fd333e2b24234a84a8112f08\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:57:53:5753"}
{"level":"error","message":"❌ Scenario failed: LGI-04: Password shorter than 6 shows min-length message","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-04:-Password-shorter-than-6-shows-min-length-message-2025-08-12T08-57-58-365Z.png","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-04:-Password-shorter-than-6-shows-min-length-message-2025-08-12T08-57-58-365Z.png","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-05: Password with disallowed characters shows policy message","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:58:03:583"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@5bbf5cace580cc363f804680fff859cb\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"🔸 STEP: Entering valid email","timestamp":"2025-08-12 15:58:03:583"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@5bbf5cace580cc363f804680fff859cb\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"🎯 ACTION: Valid email entered","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"🔸 STEP: Entering password: abcDEF!@#","timestamp":"2025-08-12 15:58:03:583"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@5bbf5cace580cc363f804680fff859cb\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"⌨️ Input filled with: abcDEF!@#","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"🎯 ACTION: Password entered","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:58:03:583"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@5bbf5cace580cc363f804680fff859cb\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@b1be155ffa58f68e36a9344e469a49c0\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:58:03:583"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:03:583"}
{"level":"error","message":"❌ Scenario failed: LGI-05: Password with disallowed characters shows policy message","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-05:-Password-with-disallowed-characters-shows-policy-message-2025-08-12T08-58-08-399Z.png","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-05:-Password-with-disallowed-characters-shows-policy-message-2025-08-12T08-58-08-399Z.png","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-06: Password longer than 16 shows max-length message","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:08:588"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@278fb41a70f06db69975d6480002f29e\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"🔸 STEP: Entering valid email","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@278fb41a70f06db69975d6480002f29e\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"🎯 ACTION: Valid email entered","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"🔸 STEP: Entering password: abcdefghijklmnopq","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@278fb41a70f06db69975d6480002f29e\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"⌨️ Input filled with: abcdefghijklmnopq","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"🎯 ACTION: Password entered","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@278fb41a70f06db69975d6480002f29e\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@88d0960afb9d2e3d821b8d5e1662ef84\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:10:5810"}
{"level":"error","message":"❌ Scenario failed: LGI-06: Password longer than 16 shows max-length message","timestamp":"2025-08-12 15:58:15:5815"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-06:-Password-longer-than-16-shows-max-length-message-2025-08-12T08-58-15-791Z.png","timestamp":"2025-08-12 15:58:15:5815"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-06:-Password-longer-than-16-shows-max-length-message-2025-08-12T08-58-15-791Z.png","timestamp":"2025-08-12 15:58:15:5815"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:58:15:5815"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:58:16:5816"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:58:16:5816"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-07: Invalid email or password shows auth error","timestamp":"2025-08-12 15:58:16:5816"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:58:16:5816"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:58:16:5816"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:58:16:5816"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:58:16:5816"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:16:5816"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:16:5816"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@f1b8f5de1cf069a4aa3c1fdff32636cc\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@f1b8f5de1cf069a4aa3c1fdff32636cc\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🎯 ACTION: Email entered","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🔸 STEP: Entering password: wrongpassword","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@f1b8f5de1cf069a4aa3c1fdff32636cc\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"⌨️ Input filled with: wrongpassword","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🎯 ACTION: Password entered","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@f1b8f5de1cf069a4aa3c1fdff32636cc\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🖱️ Element clicked","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🎯 ACTION: Login form submitted","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🎯 ACTION: Login form submitted","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"info","message":"🔸 STEP: Verifying error message: メールアドレスが存在しません。","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@f1b8f5de1cf069a4aa3c1fdff32636cc\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@d8f2a6042cd1c75b96504c1d50a4ab45\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"error","message":"Browser console error: Failed to load resource: the server responded with a status of 400 ()","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"error","message":"❌ Scenario failed: LGI-07: Invalid email or password shows auth error","timestamp":"2025-08-12 15:58:25:5825"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-07:-Invalid-email-or-password-shows-auth-error-2025-08-12T08-58-25-681Z.png","timestamp":"2025-08-12 15:58:25:5825"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-07:-Invalid-email-or-password-shows-auth-error-2025-08-12T08-58-25-681Z.png","timestamp":"2025-08-12 15:58:25:5825"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:58:25:5825"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:58:25:5825"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:58:25:5825"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-08: Show/Hide password toggle works","timestamp":"2025-08-12 15:58:25:5825"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:58:26:5826"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:58:26:5826"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:58:26:5826"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:58:26:5826"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:26:5826"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:26:5826"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@fa9e0575964548ae115d2b664327fb36\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"🔸 STEP: Entering password: testpassword","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@fa9e0575964548ae115d2b664327fb36\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"⌨️ Input filled with: testpassword","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"🎯 ACTION: Password entered","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"🔸 STEP: Verifying password is hidden","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@fa9e0575964548ae115d2b664327fb36\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"Password visibility: false","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"✅ ASSERTION: ✅ Password is hidden","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"🔸 STEP: Clicking show password toggle","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@fa9e0575964548ae115d2b664327fb36\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ee2dddb40688f5227906bbda030826d1\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"info","message":"🔸 STEP: Toggling password visibility","timestamp":"2025-08-12 15:58:28:5828"}
{"level":"error","message":"❌ Scenario failed: LGI-08: Show/Hide password toggle works","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"warn","message":"Password toggle not found on page","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"🎯 ACTION: Password toggle clicked","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"error","message":"Failed to take screenshot: Error: ENOENT: no such file or directory, open 'D:\\Automation\\SmoothContact\\logs\\failed-LGI-08:-Show\\Hide-password-toggle-works-2025-08-12T08-58-33-153Z.png'","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-09: Remember me persists session across reload","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@b2cbc52a1c6d205ba5cc1704d40a8749\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"🔸 STEP: Entering valid credentials","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@b2cbc52a1c6d205ba5cc1704d40a8749\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"⌨️ Input filled with: vietnam5963","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"🎯 ACTION: Valid credentials entered","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"🔸 STEP: Checking remember me checkbox","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@b2cbc52a1c6d205ba5cc1704d40a8749\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@3564eae56fd2fb9c929e36bc5c6c7123\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"info","message":"🔸 STEP: Setting remember me to: true","timestamp":"2025-08-12 15:58:35:5835"}
{"level":"error","message":"❌ Scenario failed: LGI-09: Remember me persists session across reload","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"warn","message":"Remember me checkbox not found on page","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"🎯 ACTION: Remember me checkbox checked","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-09:-Remember-me-persists-session-across-reload-2025-08-12T08-58-40-582Z.png","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-09:-Remember-me-persists-session-across-reload-2025-08-12T08-58-40-582Z.png","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-10: Logout invalidates session and returns to login","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@d687042ab874927c25eaaef090f16fa3\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"🔸 STEP: Logging in with valid credentials","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@d687042ab874927c25eaaef090f16fa3\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@5005604dece480ee64661e10d634bb81\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"🔸 STEP: Attempting login with email: <EMAIL>","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"⌨️ Input filled with: vietnam5963","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"🖱️ Element clicked","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"🎯 ACTION: Login form submitted","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"info","message":"🔸 STEP: Waiting for successful login redirect","timestamp":"2025-08-12 15:58:43:5843"}
{"level":"error","message":"❌ Scenario failed: LGI-10: Logout invalidates session and returns to login","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-10:-Logout-invalidates-session-and-returns-to-login-2025-08-12T08-58-48-108Z.png","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-10:-Logout-invalidates-session-and-returns-to-login-2025-08-12T08-58-48-108Z.png","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-11: Visual regression baseline for login page","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"error","message":"❌ Scenario failed: LGI-11: Visual regression baseline for login page","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-11:-Visual-regression-baseline-for-login-page-2025-08-12T08-58-53-491Z.png","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-11:-Visual-regression-baseline-for-login-page-2025-08-12T08-58-53-491Z.png","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-12: Accessibility smoke on login page","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"error","message":"❌ Scenario failed: LGI-12: Accessibility smoke on login page","timestamp":"2025-08-12 15:58:58:5858"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-12:-Accessibility-smoke-on-login-page-2025-08-12T08-58-58-994Z.png","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-12:-Accessibility-smoke-on-login-page-2025-08-12T08-58-58-994Z.png","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: Password validation messages","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:58:59:5859"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:59:04:594"}
{"level":"error","message":"❌ Scenario failed: Password validation messages","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:59:04:594"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@23acd6a0884af1a54ec1190592c10c2c\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@004d1694b172ece5401c3de984fca172\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@004d1694b172ece5401c3de984fca172\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@004d1694b172ece5401c3de984fca172\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@004d1694b172ece5401c3de984fca172\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@004d1694b172ece5401c3de984fca172\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@004d1694b172ece5401c3de984fca172\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@004d1694b172ece5401c3de984fca172\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@004d1694b172ece5401c3de984fca172\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"📸 Screenshot saved: failed-Password-validation-messages-2025-08-12T08-59-04-347Z.png","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-Password-validation-messages-2025-08-12T08-59-04-347Z.png","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: Password validation messages","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:59:04:594"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:59:04:594"}
{"level":"error","message":"❌ Scenario failed: Password validation messages","timestamp":"2025-08-12 15:59:09:599"}
{"level":"info","message":"📸 Screenshot saved: failed-Password-validation-messages-2025-08-12T08-59-09-678Z.png","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-Password-validation-messages-2025-08-12T08-59-09-678Z.png","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: Password validation messages","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:59:10:5910"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@bde88c004f39744bac7d332dcd77fa2e\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"🔸 STEP: Entering credentials and submitting: <EMAIL>","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@bde88c004f39744bac7d332dcd77fa2e\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@92abced375be30f5a435a64e14fbafde\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"⌨️ Input filled with: abcDEF!@#","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:12:5912"}
{"level":"error","message":"❌ Scenario failed: Password validation messages","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"📸 Screenshot saved: failed-Password-validation-messages-2025-08-12T08-59-17-465Z.png","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-Password-validation-messages-2025-08-12T08-59-17-465Z.png","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: Password validation messages","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@8fb1d48559170822c291721427450098\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"🔸 STEP: Entering credentials and submitting: <EMAIL>","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@8fb1d48559170822c291721427450098\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@a4e3d4cc0a028d4a2d5101e1e0d2631b\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"⌨️ Input filled with: abcdefghijklmnopq","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:22:5922"}
{"level":"error","message":"❌ Scenario failed: Password validation messages","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"📸 Screenshot saved: failed-Password-validation-messages-2025-08-12T08-59-27-206Z.png","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-Password-validation-messages-2025-08-12T08-59-27-206Z.png","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: Password validation messages","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@75ac679d353e38f0c48469a0319f9ac9\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"🔸 STEP: Entering credentials and submitting: <EMAIL>","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@75ac679d353e38f0c48469a0319f9ac9\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"⌨️ Input filled with: wrongpassword","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"🖱️ Element clicked","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"🎯 ACTION: Login form submitted","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"🎯 ACTION: Credentials entered and form submitted","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"info","message":"🔸 STEP: Verifying error message: メールアドレスが存在しません。","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@75ac679d353e38f0c48469a0319f9ac9\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@ce2e7d665e421bb37e8a18c8fa706458\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:59:29:5929"}
{"level":"error","message":"Browser console error: Failed to load resource: the server responded with a status of 400 ()","timestamp":"2025-08-12 15:59:30:5930"}
{"level":"error","message":"❌ Scenario failed: Password validation messages","timestamp":"2025-08-12 15:59:34:5934"}
{"level":"info","message":"📸 Screenshot saved: failed-Password-validation-messages-2025-08-12T08-59-34-915Z.png","timestamp":"2025-08-12 15:59:35:5935"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-Password-validation-messages-2025-08-12T08-59-34-915Z.png","timestamp":"2025-08-12 15:59:35:5935"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:59:35:5935"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:59:35:5935"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:59:35:5935"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:59:35:5935"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:59:35:5935"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 16:10:24:1024"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 16:10:24:1024"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-01: Login page loads","timestamp":"2025-08-12 16:10:24:1024"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 16:10:25:1025"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 16:10:25:1025"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 16:10:25:1025"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 16:10:25:1025"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 16:10:25:1025"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 16:10:25:1025"}
{"level":"error","message":"❌ Scenario failed: LGI-01: Login page loads","timestamp":"2025-08-12 16:10:30:1030"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-01:-Login-page-loads-2025-08-12T09-10-30-167Z.png","timestamp":"2025-08-12 16:10:30:1030"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-01:-Login-page-loads-2025-08-12T09-10-30-167Z.png","timestamp":"2025-08-12 16:10:30:1030"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 16:10:30:1030"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 16:10:30:1030"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 16:10:30:1030"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 16:10:30:1030"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 16:10:30:1030"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 16:11:28:1128"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 16:11:28:1128"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-01: Login page loads","timestamp":"2025-08-12 16:11:28:1128"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 16:11:28:1128"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 16:11:28:1128"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 16:11:28:1128"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 16:11:28:1128"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 16:11:28:1128"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 16:11:28:1128"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@e19923181ab9925a9401f6be17f60565\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"email\\\"] >> nth=0\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"pwd\\\"] >> nth=0\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"button[type=\\\"submit\\\"] >> nth=0\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".MuiAlert-message, .error, [role=\\\"alert\\\"] >> nth=0\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"img[alt=\\\"logo\\\"] >> nth=0\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"h1.MuiTypography-h5 >> nth=0\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@e19923181ab9925a9401f6be17f60565\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"email\\\"] >> nth=0\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"pwd\\\"] >> nth=0\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"button[type=\\\"submit\\\"] >> nth=0\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".MuiAlert-message, .error, [role=\\\"alert\\\"] >> nth=0\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"img[alt=\\\"logo\\\"] >> nth=0\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"h1.MuiTypography-h5 >> nth=0\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page is properly loaded","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🔸 STEP: Verifying logo is visible","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@e19923181ab9925a9401f6be17f60565\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"email\\\"] >> nth=0\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"pwd\\\"] >> nth=0\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"button[type=\\\"submit\\\"] >> nth=0\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".MuiAlert-message, .error, [role=\\\"alert\\\"] >> nth=0\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"img[alt=\\\"logo\\\"] >> nth=0\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"h1.MuiTypography-h5 >> nth=0\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ ASSERTION: ✅ Logo is visible","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🔸 STEP: Verifying page title is visible","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@e19923181ab9925a9401f6be17f60565\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"email\\\"] >> nth=0\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"pwd\\\"] >> nth=0\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"button[type=\\\"submit\\\"] >> nth=0\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".MuiAlert-message, .error, [role=\\\"alert\\\"] >> nth=0\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"img[alt=\\\"logo\\\"] >> nth=0\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"h1.MuiTypography-h5 >> nth=0\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ ASSERTION: ✅ Page title is visible","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🔸 STEP: Verifying email input is visible","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@e19923181ab9925a9401f6be17f60565\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"email\\\"] >> nth=0\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"pwd\\\"] >> nth=0\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"button[type=\\\"submit\\\"] >> nth=0\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".MuiAlert-message, .error, [role=\\\"alert\\\"] >> nth=0\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"img[alt=\\\"logo\\\"] >> nth=0\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"h1.MuiTypography-h5 >> nth=0\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ ASSERTION: ✅ Email input is visible","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🔸 STEP: Verifying password input is visible","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@e19923181ab9925a9401f6be17f60565\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"email\\\"] >> nth=0\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"pwd\\\"] >> nth=0\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"button[type=\\\"submit\\\"] >> nth=0\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".MuiAlert-message, .error, [role=\\\"alert\\\"] >> nth=0\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"img[alt=\\\"logo\\\"] >> nth=0\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"h1.MuiTypography-h5 >> nth=0\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ ASSERTION: ✅ Password input is visible","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🔸 STEP: Verifying submit button is visible","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@e19923181ab9925a9401f6be17f60565\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"email\\\"] >> nth=0\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[name=\\\"pwd\\\"] >> nth=0\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"button[type=\\\"submit\\\"] >> nth=0\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".MuiAlert-message, .error, [role=\\\"alert\\\"] >> nth=0\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"img[alt=\\\"logo\\\"] >> nth=0\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"h1.MuiTypography-h5 >> nth=0\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@e29b088f81ab5aaf5ee2f2e4d8410f04\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ ASSERTION: ✅ Submit button is visible","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"✅ Scenario passed: LGI-01: Login page loads","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 16:11:34:1134"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 16:11:46:1146"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 16:11:46:1146"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-02: User can login with valid credentials","timestamp":"2025-08-12 16:11:46:1146"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 16:11:47:1147"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 16:11:47:1147"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 16:11:47:1147"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 16:11:47:1147"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 16:11:47:1147"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 16:11:47:1147"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@b7d7a45d60d7f29db37de445564a5b7e\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"input[name=\\\"email\\\"] >> nth=0\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"input[name=\\\"pwd\\\"] >> nth=0\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"button[type=\\\"submit\\\"] >> nth=0\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\".MuiAlert-message, .error, [role=\\\"alert\\\"] >> nth=0\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"img[alt=\\\"logo\\\"] >> nth=0\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"h1.MuiTypography-h5 >> nth=0\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"🔸 STEP: Entering valid credentials","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@b7d7a45d60d7f29db37de445564a5b7e\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"input[name=\\\"email\\\"] >> nth=0\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"input[name=\\\"pwd\\\"] >> nth=0\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"button[type=\\\"submit\\\"] >> nth=0\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\".MuiAlert-message, .error, [role=\\\"alert\\\"] >> nth=0\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"img[alt=\\\"logo\\\"] >> nth=0\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"h1.MuiTypography-h5 >> nth=0\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"🔸 STEP: Entering email: <EMAIL>","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"⌨️ Input filled with: <EMAIL>","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"🔸 STEP: Entering password","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"⌨️ Input filled with: vietnam5963","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"🎯 ACTION: Valid credentials entered","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@b7d7a45d60d7f29db37de445564a5b7e\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"input[name=\\\"email\\\"] >> nth=0\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"input[name=\\\"pwd\\\"] >> nth=0\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"button[type=\\\"submit\\\"] >> nth=0\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\".MuiAlert-message, .error, [role=\\\"alert\\\"] >> nth=0\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"img[alt=\\\"logo\\\"] >> nth=0\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"h1.MuiTypography-h5 >> nth=0\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@21aebb36f3de09ca622fc896f1ad4ba5\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"🔸 STEP: Submitting login form","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"🖱️ Element clicked","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"🎯 ACTION: Login form submitted","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"🎯 ACTION: Login form submitted","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"🔸 STEP: Verifying successful login and dashboard access","timestamp":"2025-08-12 16:11:49:1149"}
{"level":"info","message":"✅ ASSERTION: ✅ Login success message found","timestamp":"2025-08-12 16:11:50:1150"}
{"level":"info","message":"✅ ASSERTION: ✅ Successfully authenticated and on dashboard","timestamp":"2025-08-12 16:11:50:1150"}
{"level":"info","message":"🔸 STEP: Verifying authenticated state","timestamp":"2025-08-12 16:11:50:1150"}
{"level":"info","message":"✅ ASSERTION: ✅ Authenticated indicator found","timestamp":"2025-08-12 16:11:50:1150"}
{"level":"info","message":"✅ Scenario passed: LGI-02: User can login with valid credentials","timestamp":"2025-08-12 16:11:50:1150"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 16:11:50:1150"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 16:11:50:1150"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 16:11:50:1150"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 16:11:50:1150"}
