{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:50:49:5049"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:50:49:5049"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:50:49:5049"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:50:49:5049"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:51:19:5119"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:51:19:5119"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:51:19:5119"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:51:19:5119"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:52:17:5217"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:52:18:5218"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:52:18:5218"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:52:18:5218"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-01: Login page loads","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"error","message":"❌ Scenario failed: LGI-01: Login page loads","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"info","message":"📸 Screenshot saved: failed-LGI-01:-Login-page-loads-2025-08-12T08-52-29-873Z.png","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"📸 Failure screenshot saved: logs/failed-LGI-01:-Login-page-loads-2025-08-12T08-52-29-873Z.png","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"💾 Page HTML saved for debugging","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:52:30:5230"}
{"level":"info","message":"🚀 Starting test suite...","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"🌐 Browser launched successfully","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"🎬 SCENARIO: Starting scenario: LGI-01: Login page loads","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"🌐 Browser instance set","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"📋 Browser context set","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"📄 Page instance set","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"📄 New page context created for scenario","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"🔸 STEP: Navigating to login page","timestamp":"2025-08-12 15:52:56:5256"}
{"level":"info","message":"✅ Page loaded successfully","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Successfully navigated to login page: https://smoothcontact-web.bindec-app-stage.web-life.co.jp/","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"debug","message":"💾 Test data set: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Successfully on login page","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"🔸 STEP: Verifying login page is loaded","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"👁️ Element is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page loaded - Logo: true, Title: true","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ ASSERTION: ✅ Login page is properly loaded","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"🔸 STEP: Verifying logo is visible","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:58:5258"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ ASSERTION: ✅ Logo is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🔸 STEP: Verifying page title is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ ASSERTION: ✅ Page title is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🔸 STEP: Verifying email input is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ ASSERTION: ✅ Email input is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🔸 STEP: Verifying password input is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ ASSERTION: ✅ Password input is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🔸 STEP: Verifying submit button is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"debug","message":"📖 Test data retrieved: loginPage = {\"page\":{\"_type\":\"Page\",\"_guid\":\"page@7ec6beee13eb718efc1a0b7eb1af04dd\"},\"url\":\"/\",\"emailInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"email\\\"], input[name=\\\"email\\\"], #email\"},\"passwordInput\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"password\\\"], input[name=\\\"password\\\"], #password\"},\"submitButton\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"button[type=\\\"submit\\\"], input[type=\\\"submit\\\"], button:has-text(\\\"ログイン\\\")\"},\"errorAlert\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".error, .alert-error, [role=\\\"alert\\\"], .error-message\"},\"logo\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".logo, [data-testid=\\\"logo\\\"], img[alt*=\\\"logo\\\"]\"},\"pageTitle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"h1, .page-title, [data-testid=\\\"page-title\\\"]\"},\"showPasswordToggle\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\".password-toggle, [data-testid=\\\"show-password\\\"], .eye-icon\"},\"rememberMe\":{\"_frame\":{\"_type\":\"Frame\",\"_guid\":\"frame@0f91f98c865d52b7bc3c6630e9a194e9\"},\"_selector\":\"input[type=\\\"checkbox\\\"][name*=\\\"remember\\\"], #remember-me\"}}","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ Element visibility verified","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ ASSERTION: ✅ Submit button is visible","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"✅ Scenario passed: LGI-01: Login page loads","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🧹 Page and context closed","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🧹 Test data cleared","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🏁 Test suite completed","timestamp":"2025-08-12 15:52:59:5259"}
{"level":"info","message":"🌐 Browser closed","timestamp":"2025-08-12 15:52:59:5259"}
