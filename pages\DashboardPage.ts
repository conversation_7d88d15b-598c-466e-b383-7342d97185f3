import { Page, Locator } from '@playwright/test';
import { BasePage } from './BasePage';
import { logger } from '../utils/logger';

export class DashboardPage extends BasePage {
  // Locators
  private readonly welcomeMessage: Locator;
  private readonly userProfileDropdown: Locator;
  private readonly logoutButton: Locator;
  private readonly navigationMenu: Locator;
  private readonly contactsLink: Locator;
  private readonly reportsLink: Locator;
  private readonly settingsLink: Locator;
  private readonly searchInput: Locator;
  private readonly notificationBell: Locator;
  private readonly dashboardStats: Locator;
  private readonly recentActivities: Locator;

  constructor(page: Page) {
    super(page, '/dashboard');
    
    // Initialize locators
    this.welcomeMessage = page.locator('[data-testid="welcome-message"]');
    this.userProfileDropdown = page.locator('[data-testid="user-profile-dropdown"]');
    this.logoutButton = page.locator('[data-testid="logout-button"]');
    this.navigationMenu = page.locator('[data-testid="navigation-menu"]');
    this.contactsLink = page.locator('[data-testid="contacts-link"]');
    this.reportsLink = page.locator('[data-testid="reports-link"]');
    this.settingsLink = page.locator('[data-testid="settings-link"]');
    this.searchInput = page.locator('[data-testid="search-input"]');
    this.notificationBell = page.locator('[data-testid="notification-bell"]');
    this.dashboardStats = page.locator('[data-testid="dashboard-stats"]');
    this.recentActivities = page.locator('[data-testid="recent-activities"]');
  }

  /**
   * Verify dashboard is loaded
   */
  async verifyDashboardLoaded(): Promise<void> {
    await this.waitForElement(this.welcomeMessage);
    await this.verifyElementVisible(this.navigationMenu);
    await this.verifyElementVisible(this.userProfileDropdown);
    logger.assertion('Dashboard loaded successfully');
  }

  /**
   * Get welcome message text
   */
  async getWelcomeMessage(): Promise<string> {
    const message = await this.getElementText(this.welcomeMessage);
    logger.info(`Welcome message: ${message}`);
    return message;
  }

  /**
   * Navigate to contacts page
   */
  async navigateToContacts(): Promise<void> {
    await this.clickElement(this.contactsLink);
    await this.page.waitForURL('**/contacts');
    logger.action('Navigated to contacts page');
  }

  /**
   * Navigate to reports page
   */
  async navigateToReports(): Promise<void> {
    await this.clickElement(this.reportsLink);
    await this.page.waitForURL('**/reports');
    logger.action('Navigated to reports page');
  }

  /**
   * Navigate to settings page
   */
  async navigateToSettings(): Promise<void> {
    await this.clickElement(this.settingsLink);
    await this.page.waitForURL('**/settings');
    logger.action('Navigated to settings page');
  }

  /**
   * Perform search
   */
  async search(query: string): Promise<void> {
    await this.fillInput(this.searchInput, query);
    await this.page.keyboard.press('Enter');
    logger.action(`Searched for: ${query}`);
  }

  /**
   * Open user profile dropdown
   */
  async openUserProfileDropdown(): Promise<void> {
    await this.clickElement(this.userProfileDropdown);
    logger.action('User profile dropdown opened');
  }

  /**
   * Logout from application
   */
  async logout(): Promise<void> {
    await this.openUserProfileDropdown();
    await this.clickElement(this.logoutButton);
    await this.page.waitForURL('**/login');
    logger.action('Logged out successfully');
  }

  /**
   * Check notification count
   */
  async getNotificationCount(): Promise<number> {
    const notificationBadge = this.notificationBell.locator('.notification-count');
    
    if (await this.isElementVisible(notificationBadge)) {
      const countText = await this.getElementText(notificationBadge);
      const count = parseInt(countText) || 0;
      logger.info(`Notification count: ${count}`);
      return count;
    }
    
    logger.info('No notifications');
    return 0;
  }

  /**
   * Click notification bell
   */
  async clickNotificationBell(): Promise<void> {
    await this.clickElement(this.notificationBell);
    logger.action('Notification bell clicked');
  }

  /**
   * Get dashboard statistics
   */
  async getDashboardStats(): Promise<{ [key: string]: string }> {
    await this.waitForElement(this.dashboardStats);
    
    const statElements = await this.dashboardStats.locator('.stat-item').all();
    const stats: { [key: string]: string } = {};
    
    for (const element of statElements) {
      const label = await element.locator('.stat-label').textContent() || '';
      const value = await element.locator('.stat-value').textContent() || '';
      stats[label] = value;
    }
    
    logger.info(`Dashboard stats: ${JSON.stringify(stats)}`);
    return stats;
  }

  /**
   * Get recent activities
   */
  async getRecentActivities(): Promise<string[]> {
    await this.waitForElement(this.recentActivities);
    
    const activityElements = await this.recentActivities.locator('.activity-item').all();
    const activities: string[] = [];
    
    for (const element of activityElements) {
      const activity = await element.textContent() || '';
      activities.push(activity.trim());
    }
    
    logger.info(`Recent activities count: ${activities.length}`);
    return activities;
  }

  /**
   * Verify navigation menu items
   */
  async verifyNavigationMenuItems(): Promise<void> {
    await this.verifyElementVisible(this.contactsLink);
    await this.verifyElementVisible(this.reportsLink);
    await this.verifyElementVisible(this.settingsLink);
    logger.assertion('Navigation menu items verified');
  }

  /**
   * Check if user is logged in
   */
  async isUserLoggedIn(): Promise<boolean> {
    const isWelcomeVisible = await this.isElementVisible(this.welcomeMessage);
    const isProfileDropdownVisible = await this.isElementVisible(this.userProfileDropdown);
    
    const isLoggedIn = isWelcomeVisible && isProfileDropdownVisible;
    logger.assertion(`User logged in: ${isLoggedIn}`);
    
    return isLoggedIn;
  }

  /**
   * Wait for dashboard data to load
   */
  async waitForDashboardDataLoad(): Promise<void> {
    await this.waitForElement(this.dashboardStats);
    await this.waitForElement(this.recentActivities);
    logger.info('Dashboard data loaded');
  }
}
