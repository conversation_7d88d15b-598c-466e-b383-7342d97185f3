# SmoothContact Automation Framework

A comprehensive Playwright + TypeScript automation testing framework with Cucumber integration, following Page Object Model (POM) and Object-Oriented Programming principles.

## 🚀 Features

- **Playwright + TypeScript**: Modern, fast, and reliable browser automation
- **Cucumber Integration**: Gherkin syntax for behavior-driven development
- **Page Object Model**: Organized, maintainable page classes
- **Multi-Environment Support**: Development, staging, and production configurations
- **Rich Reporting**: Allure reports with screenshots, videos, and traces
- **Comprehensive Logging**: Winston logger with multiple levels and file rotation
- **CI/CD Ready**: GitHub Actions workflow with parallel execution
- **Cross-Browser Testing**: Chromium, Firefox, and WebKit support
- **Custom Matchers**: Enhanced assertions for better test reliability
- **Test Data Management**: JSON-based test data with environment-specific configurations

## 📁 Project Structure

```
├── .github/workflows/     # CI/CD pipeline configurations
├── config/               # Environment-specific Playwright configs
├── features/             # Cucumber feature files (.feature)
├── fixtures/             # Test data and configuration files
├── logs/                 # Test execution logs and screenshots
├── pages/                # Page Object Model classes
├── reports/              # Test reports (Allure, HTML, JSON)
├── src/types/            # TypeScript type definitions
├── step-definitions/     # Cucumber step implementations
├── tests/e2e/           # Playwright test files
├── utils/               # Helper functions and utilities
├── cucumber.js          # Cucumber configuration
├── playwright.config.ts # Main Playwright configuration
└── tsconfig.json        # TypeScript configuration
```

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd SmoothContact
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install Playwright browsers**
   ```bash
   npx playwright install
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

## 🎯 Quick Start

### Running Tests

```bash
# Run all tests
npm run test

# Run tests in headless mode
npm run test:headless

# Run tests with specific tags
npm run test:tag -- "@smoke"

# Run tests in parallel
npm run test:parallel

# Run tests for specific environment
npm run test:dev
npm run test:staging
npm run test:prod

# Run login tests specifically
npm run test:login
```

### Generate Reports

```bash
# Generate and open Allure report
npm run report

# Generate Allure report
npm run report:allure

# Open Playwright HTML report
npm run report:html

# Serve Allure report
npm run report:serve
```

### Code Quality

```bash
# Run linting
npm run lint

# Format code
npm run format

# Type checking
npm run type-check
```

## 🔐 Login Test Suite

### Comprehensive Login Testing (LGI-01 to LGI-12)

The framework includes a complete login test suite for the SmoothContact staging environment:

**Target URL**: `https://smoothcontact-web.bindec-app-stage.web-life.co.jp/`

**Test Scenarios**:
- **LGI-01**: Login page loads (@smoke @login)
- **LGI-02**: Valid credentials login (@smoke @login)
- **LGI-03**: Empty password validation (@login)
- **LGI-04**: Password length validation (@login)
- **LGI-05**: Password character policy (@login)
- **LGI-06**: Password max length validation (@login)
- **LGI-07**: Invalid credentials handling (@regression @login)
- **LGI-08**: Show/hide password toggle (@login)
- **LGI-09**: Remember me functionality (@regression @login)
- **LGI-10**: Logout functionality (@regression @login)
- **LGI-11**: Visual regression testing (@visual @login)
- **LGI-12**: Accessibility testing (@a11y @login)

**Japanese Error Messages** (with Unicode normalization):
- Empty password: `パスワードが必須`
- Min length: `パスワードは6文字以上で入力してください`
- Character policy: `パスワードに使える文字は半角のアルファベット、_＆-記号`
- Max length: `パスワードは16文字以内で入力してください`
- Invalid auth: `メールアドレスが存在しません。`

**Running Login Tests**:
```bash
# Run all login tests
npm run test:login

# Run specific login scenarios
npx cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --name "LGI-01" features/login/login.feature

# Run smoke login tests only
npx cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --tags "@smoke and @login" features/login/login.feature
```

## 📝 Writing Tests

### 1. Creating Feature Files

Create `.feature` files in the `features/` directory using Gherkin syntax:

```gherkin
@login @smoke
Feature: User Login
  As a user of SmoothContact
  I want to be able to login to the application
  So that I can access my account

  @positive @critical
  Scenario: Successful login with valid credentials
    Given I am on the login page
    When I login with valid credentials
    Then I should be redirected to the dashboard
```

### 2. Creating Page Objects

Create page classes in the `pages/` directory extending `BasePage`:

```typescript
import { Page, Locator } from '@playwright/test';
import { BasePage } from './BasePage';

export class LoginPage extends BasePage {
  private readonly usernameInput: Locator;
  private readonly passwordInput: Locator;
  private readonly loginButton: Locator;

  constructor(page: Page) {
    super(page, '/login');
    this.usernameInput = page.locator('[data-testid="username-input"]');
    this.passwordInput = page.locator('[data-testid="password-input"]');
    this.loginButton = page.locator('[data-testid="login-button"]');
  }

  async login(username: string, password: string): Promise<void> {
    await this.fillInput(this.usernameInput, username);
    await this.fillInput(this.passwordInput, password);
    await this.clickElement(this.loginButton);
  }
}
```

### 3. Creating Step Definitions

Create step implementations in the `step-definitions/` directory:

```typescript
import { Given, When, Then } from '@cucumber/cucumber';
import { TestContext } from '../utils/TestContext';
import { LoginPage } from '../pages/LoginPage';

const testContext = TestContext.getInstance();

Given('I am on the login page', async function () {
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  await loginPage.navigate();
});

When('I login with valid credentials', async function () {
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  await loginPage.login('<EMAIL>', 'password123');
});
```

## 🏷️ Tagging Strategy

Use tags to organize and filter tests:

- `@smoke` - Critical functionality tests
- `@regression` - Full regression test suite
- `@positive` - Happy path scenarios
- `@negative` - Error handling scenarios
- `@critical` - Must-pass tests
- `@dev-only` - Development environment only
- `@staging-only` - Staging environment only
- `@prod-only` - Production environment only
- `@skip` - Temporarily disabled tests

## 🌍 Environment Configuration

### Environment Files

- `fixtures/urls.development.json` - Development URLs
- `fixtures/urls.staging.json` - Staging URLs  
- `fixtures/urls.production.json` - Production URLs
- `fixtures/config.default.json` - Default configuration

### Environment Variables

Key environment variables in `.env`:

```bash
NODE_ENV=development
HEADLESS=false
DEV_BASE_URL=http://localhost:3000
STAGING_BASE_URL=https://staging.smoothcontact.com
PROD_BASE_URL=https://smoothcontact.com
LOG_LEVEL=info
```

## 📊 Reporting

### Allure Reports

Rich HTML reports with:
- Test execution timeline
- Screenshots and videos for failures
- Step-by-step execution details
- Environment information
- Historical trends

### Cucumber Reports

- JSON reports for CI/CD integration
- HTML reports for human-readable results
- JUnit XML for test result parsing

## 🔧 Utilities

### TestContext

Singleton class for managing test state:

```typescript
const testContext = TestContext.getInstance();
testContext.setTestData('username', 'testuser');
const username = testContext.getTestData('username');
```

### TestDataManager

Centralized test data management:

```typescript
const dataManager = TestDataManager.getInstance();
const userData = dataManager.getUserData('admin');
const url = dataManager.getUrl('loginPage', 'staging');
```

### Logger

Structured logging with multiple levels:

```typescript
import { logger } from '../utils/logger';

logger.info('Test step executed');
logger.error('Test failed', { error: errorObject });
logger.step('User clicks login button');
logger.assertion('Login successful');
```

## 🚀 CI/CD Integration

### GitHub Actions

The framework includes a comprehensive GitHub Actions workflow:

- **Multi-browser testing** (Chromium, Firefox, WebKit)
- **Parallel execution** for faster feedback
- **Environment-specific runs** (dev, staging, prod)
- **Artifact collection** (reports, screenshots, videos)
- **GitHub Pages deployment** for reports
- **PR comments** with test results

### Manual Workflow Dispatch

Trigger tests manually with custom parameters:
- Environment selection
- Tag filtering
- Browser selection

## 🎨 Best Practices

### Page Objects

1. **Extend BasePage** for common functionality
2. **Use data-testid attributes** for reliable element selection
3. **Implement meaningful method names** that describe user actions
4. **Add proper error handling** and logging
5. **Keep page objects focused** on a single page/component

### Step Definitions

1. **Keep steps reusable** across multiple scenarios
2. **Use descriptive step names** that match Gherkin language
3. **Implement proper assertions** with meaningful error messages
4. **Handle async operations** properly with await
5. **Use TestContext** for sharing data between steps

### Test Data

1. **Use JSON files** for structured test data
2. **Separate data by environment** for flexibility
3. **Generate dynamic data** when needed for uniqueness
4. **Avoid hardcoded values** in test code
5. **Use meaningful data** that reflects real-world scenarios

## 🐛 Debugging

### Local Debugging

1. **Set HEADLESS=false** to see browser actions
2. **Add breakpoints** in step definitions
3. **Use logger.debug()** for detailed information
4. **Take screenshots** at specific points
5. **Enable slow motion** with SLOW_MO environment variable

### CI Debugging

1. **Check artifacts** for screenshots and videos
2. **Review logs** in GitHub Actions output
3. **Use trace viewer** for detailed execution analysis
4. **Enable debug logging** with LOG_LEVEL=debug

## 📚 Additional Resources

- [Playwright Documentation](https://playwright.dev/)
- [Cucumber.js Documentation](https://cucumber.io/docs/cucumber/)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Allure Framework](https://docs.qameta.io/allure/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
