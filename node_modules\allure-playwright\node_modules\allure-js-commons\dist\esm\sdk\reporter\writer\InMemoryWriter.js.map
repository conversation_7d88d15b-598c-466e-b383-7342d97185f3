{"version": 3, "file": "InMemoryWriter.js", "names": ["readFileSync", "InMemoryWriter", "constructor", "_defineProperty", "writeGroup", "result", "groups", "push", "writeResult", "tests", "writeAttachment", "distFileName", "content", "attachments", "writeAttachmentFromPath", "from", "writeCategoriesDefinitions", "categories", "writeEnvironmentInfo", "envInfo"], "sources": ["../../../../../src/sdk/reporter/writer/InMemoryWriter.ts"], "sourcesContent": ["import { readFileSync } from \"fs\";\nimport type { TestResult, TestResultContainer } from \"../../../model.js\";\nimport type { AllureResults, Category, EnvironmentInfo } from \"../../types.js\";\nimport type { Writer } from \"../types.js\";\n\nexport class InMemoryWriter implements Writer, AllureResults {\n  public groups: TestResultContainer[] = [];\n  public tests: TestResult[] = [];\n  public attachments: Record<string, Buffer> = {};\n  public categories?: Category[];\n  public envInfo?: Record<string, string | undefined>;\n\n  public writeGroup(result: TestResultContainer): void {\n    this.groups.push(result);\n  }\n\n  public writeResult(result: TestResult): void {\n    this.tests.push(result);\n  }\n\n  public writeAttachment(distFileName: string, content: Buffer): void {\n    this.attachments[distFileName] = content;\n  }\n\n  public writeAttachmentFromPath(distFileName: string, from: string): void {\n    this.attachments[distFileName] = readFileSync(from);\n  }\n\n  public writeCategoriesDefinitions(categories: Category[]): void {\n    this.categories = categories;\n  }\n\n  public writeEnvironmentInfo(envInfo: EnvironmentInfo): void {\n    this.envInfo = envInfo;\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,YAAY,QAAQ,IAAI;AAKjC,OAAO,MAAMC,cAAc,CAAkC;EAAAC,YAAA;IAAAC,eAAA,iBACpB,EAAE;IAAAA,eAAA,gBACZ,EAAE;IAAAA,eAAA,sBACc,CAAC,CAAC;IAAAA,eAAA;IAAAA,eAAA;EAAA;EAIxCC,UAAUA,CAACC,MAA2B,EAAQ;IACnD,IAAI,CAACC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC;EAC1B;EAEOG,WAAWA,CAACH,MAAkB,EAAQ;IAC3C,IAAI,CAACI,KAAK,CAACF,IAAI,CAACF,MAAM,CAAC;EACzB;EAEOK,eAAeA,CAACC,YAAoB,EAAEC,OAAe,EAAQ;IAClE,IAAI,CAACC,WAAW,CAACF,YAAY,CAAC,GAAGC,OAAO;EAC1C;EAEOE,uBAAuBA,CAACH,YAAoB,EAAEI,IAAY,EAAQ;IACvE,IAAI,CAACF,WAAW,CAACF,YAAY,CAAC,GAAGX,YAAY,CAACe,IAAI,CAAC;EACrD;EAEOC,0BAA0BA,CAACC,UAAsB,EAAQ;IAC9D,IAAI,CAACA,UAAU,GAAGA,UAAU;EAC9B;EAEOC,oBAAoBA,CAACC,OAAwB,EAAQ;IAC1D,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;AACF", "ignoreList": []}