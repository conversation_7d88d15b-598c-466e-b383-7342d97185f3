# Environment Configuration
NODE_ENV=development

# Base URLs for different environments
DEV_BASE_URL=http://localhost:3000
STAGING_BASE_URL=https://staging.smoothcontact.com
PROD_BASE_URL=https://smoothcontact.com

# Browser Configuration
HEADLESS=false
SLOW_MO=0
VIEWPORT_WIDTH=1280
VIEWPORT_HEIGHT=720

# Test Configuration
TIMEOUT=30000
RETRIES=0
WORKERS=1

# Logging Configuration
LOG_LEVEL=info

# Test Data
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=UserPass123!
TEST_ADMIN_EMAIL=<EMAIL>
TEST_ADMIN_PASSWORD=AdminPass123!

# API Configuration (if needed)
API_BASE_URL=http://localhost:3001/api
API_TIMEOUT=10000

# Database Configuration (if needed for test data setup)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=smoothcontact_test
DB_USER=test_user
DB_PASSWORD=test_password

# External Services (if needed)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=smtp_password

# Feature Flags (for conditional test execution)
ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_PERFORMANCE_TESTS=false
ENABLE_ACCESSIBILITY_TESTS=true

# CI/CD Configuration
CI=false
BUILD_NUMBER=local
BRANCH_NAME=main

# Report Configuration
ALLURE_RESULTS_DIR=reports/allure-results
CUCUMBER_REPORT_DIR=reports/cucumber-report
