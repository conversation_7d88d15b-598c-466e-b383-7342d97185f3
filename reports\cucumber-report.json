[{"description": "  As a user of SmoothContact\n  I want to be able to login to the application\n  So that I can access my account and manage my contacts", "elements": [{"description": "", "id": "login-functionality;lgi-01:-login-page-loads", "keyword": "<PERSON><PERSON><PERSON>", "line": 11, "name": "LGI-01: Login page loads", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": *********}}, {"arguments": [], "keyword": "Given ", "line": 8, "name": "I am on the login page", "match": {"location": "step-definitions\\login.steps.ts:24"}, "result": {"status": "passed", "duration": **********}}, {"arguments": [], "keyword": "Then ", "line": 12, "name": "the login page should be loaded", "match": {"location": "step-definitions\\login.steps.ts:40"}, "result": {"status": "passed", "duration": ********}}, {"arguments": [], "keyword": "And ", "line": 13, "name": "the logo should be visible", "match": {"location": "step-definitions\\login.steps.ts:50"}, "result": {"status": "passed", "duration": 8169600}}, {"arguments": [], "keyword": "And ", "line": 14, "name": "the page title should be visible", "match": {"location": "step-definitions\\login.steps.ts:64"}, "result": {"status": "passed", "duration": 5948199}}, {"arguments": [], "keyword": "And ", "line": 15, "name": "the email input should be visible", "match": {"location": "step-definitions\\login.steps.ts:78"}, "result": {"status": "passed", "duration": 3673799}}, {"arguments": [], "keyword": "And ", "line": 16, "name": "the password input should be visible", "match": {"location": "step-definitions\\login.steps.ts:87"}, "result": {"status": "passed", "duration": 5493300}}, {"arguments": [], "keyword": "And ", "line": 17, "name": "the submit button should be visible", "match": {"location": "step-definitions\\login.steps.ts:96"}, "result": {"status": "passed", "duration": 3778299}}, {"keyword": "After", "hidden": true, "result": {"status": "passed", "duration": 20141400}}], "tags": [{"name": "@login", "line": 1}, {"name": "@smoke", "line": 10}, {"name": "@login", "line": 10}], "type": "scenario"}], "id": "login-functionality", "line": 2, "keyword": "Feature", "name": "Login Functionality", "tags": [{"name": "@login", "line": 1}], "uri": "features\\login\\login.feature"}]