{"version": 3, "file": "attachments.js", "names": ["randomUuid", "EXTENSIONS_BY_TYPE", "typeToExtension", "options", "fileExtension", "startsWith", "concat", "contentType", "buildAttachmentFileName", "attachmentUuid", "attachmentExtension"], "sources": ["../../../../../src/sdk/reporter/utils/attachments.ts"], "sourcesContent": ["import type { AttachmentOptions } from \"../../../model.js\";\nimport { randomUuid } from \"../utils.js\";\nimport { EXTENSIONS_BY_TYPE } from \"./extensions.js\";\n\nexport const typeToExtension = (options: AttachmentOptions): string => {\n  if (options.fileExtension) {\n    return options.fileExtension.startsWith(\".\") ? options.fileExtension : `.${options.fileExtension}`;\n  }\n\n  return EXTENSIONS_BY_TYPE[options.contentType] || \"\";\n};\n\nexport const buildAttachmentFileName = (options: Pick<AttachmentOptions, \"fileExtension\" | \"contentType\">): string => {\n  const attachmentUuid = randomUuid();\n  const attachmentExtension = typeToExtension({\n    fileExtension: options.fileExtension,\n    contentType: options.contentType,\n  });\n\n  return `${attachmentUuid}-attachment${attachmentExtension}`;\n};\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,aAAa;AACxC,SAASC,kBAAkB,QAAQ,iBAAiB;AAEpD,OAAO,IAAMC,eAAe,GAAIC,OAA0B,IAAa;EACrE,IAAIA,OAAO,CAACC,aAAa,EAAE;IACzB,OAAOD,OAAO,CAACC,aAAa,CAACC,UAAU,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACC,aAAa,OAAAE,MAAA,CAAOH,OAAO,CAACC,aAAa,CAAE;EACpG;EAEA,OAAOH,kBAAkB,CAACE,OAAO,CAACI,WAAW,CAAC,IAAI,EAAE;AACtD,CAAC;AAED,OAAO,IAAMC,uBAAuB,GAAIL,OAAiE,IAAa;EACpH,IAAMM,cAAc,GAAGT,UAAU,CAAC,CAAC;EACnC,IAAMU,mBAAmB,GAAGR,eAAe,CAAC;IAC1CE,aAAa,EAAED,OAAO,CAACC,aAAa;IACpCG,WAAW,EAAEJ,OAAO,CAACI;EACvB,CAAC,CAAC;EAEF,UAAAD,MAAA,CAAUG,cAAc,iBAAAH,MAAA,CAAcI,mBAAmB;AAC3D,CAAC", "ignoreList": []}