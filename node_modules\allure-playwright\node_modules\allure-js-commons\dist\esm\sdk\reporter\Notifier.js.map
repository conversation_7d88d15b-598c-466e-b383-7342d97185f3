{"version": 3, "file": "Notifier.js", "names": ["Notifier", "constructor", "_ref", "listeners", "_defineProperty", "listenerName", "result", "listener", "_listener$listenerNam", "call", "err", "console", "error", "concat", "callListeners"], "sources": ["../../../../src/sdk/reporter/Notifier.ts"], "sourcesContent": ["import type { StepResult, TestResult } from \"../../model.js\";\nimport type { LifecycleListener } from \"./types.js\";\n\ntype ListenerKey = keyof LifecycleListener;\n\ntype ListenerTarget<T extends keyof LifecycleListener> = LifecycleListener[T] extends\n  | ((_: infer TResult) => void)\n  | undefined\n  ? TResult\n  : never;\n\nexport class Notifier implements LifecycleListener {\n  listeners: LifecycleListener[];\n\n  constructor({ listeners }: { listeners: LifecycleListener[] }) {\n    this.listeners = [...listeners];\n  }\n\n  private callListeners = <TKey extends ListenerKey>(listenerName: TKey, result: ListenerTarget<TKey>) => {\n    for (const listener of this.listeners) {\n      try {\n        // @ts-ignore\n        listener?.[listenerName]?.(result);\n      } catch (err) {\n        // eslint-disable-next-line no-console\n        console.error(`${listenerName} listener handler can't be executed due an error: `, err);\n      }\n    }\n  };\n\n  beforeTestResultStart = (result: TestResult) => {\n    this.callListeners(\"beforeTestResultStart\", result);\n  };\n\n  afterTestResultStart = (result: TestResult) => {\n    this.callListeners(\"afterTestResultStart\", result);\n  };\n\n  beforeTestResultStop = (result: TestResult) => {\n    this.callListeners(\"beforeTestResultStop\", result);\n  };\n\n  afterTestResultStop = (result: TestResult) => {\n    this.callListeners(\"afterTestResultStop\", result);\n  };\n\n  beforeTestResultUpdate = (result: TestResult) => {\n    this.callListeners(\"beforeTestResultUpdate\", result);\n  };\n\n  afterTestResultUpdate = (result: TestResult) => {\n    this.callListeners(\"afterTestResultUpdate\", result);\n  };\n\n  beforeTestResultWrite = (result: TestResult) => {\n    this.callListeners(\"beforeTestResultWrite\", result);\n  };\n\n  afterTestResultWrite = (result: TestResult) => {\n    this.callListeners(\"afterTestResultWrite\", result);\n  };\n\n  beforeStepStop = (result: StepResult) => {\n    this.callListeners(\"beforeStepStop\", result);\n  };\n\n  afterStepStop = (result: StepResult) => {\n    this.callListeners(\"afterStepStop\", result);\n  };\n}\n"], "mappings": ";;;AAWA,OAAO,MAAMA,QAAQ,CAA8B;EAGjDC,WAAWA,CAAAC,IAAA,EAAoD;IAAA,IAAnD;MAAEC;IAA8C,CAAC,GAAAD,IAAA;IAAAE,eAAA;IAAAA,eAAA,wBAIrC,CAA2BC,YAAkB,EAAEC,MAA4B,KAAK;MACtG,KAAK,IAAMC,QAAQ,IAAI,IAAI,CAACJ,SAAS,EAAE;QACrC,IAAI;UAAA,IAAAK,qBAAA;UACF;UACAD,QAAQ,aAARA,QAAQ,gBAAAC,qBAAA,GAARD,QAAQ,CAAGF,YAAY,CAAC,cAAAG,qBAAA,eAAxBA,qBAAA,CAAAC,IAAA,CAAAF,QAAQ,EAAmBD,MAAM,CAAC;QACpC,CAAC,CAAC,OAAOI,GAAG,EAAE;UACZ;UACAC,OAAO,CAACC,KAAK,IAAAC,MAAA,CAAIR,YAAY,yDAAsDK,GAAG,CAAC;QACzF;MACF;IACF,CAAC;IAAAN,eAAA,gCAEwBE,MAAkB,IAAK;MAC9C,IAAI,CAACQ,aAAa,CAAC,uBAAuB,EAAER,MAAM,CAAC;IACrD,CAAC;IAAAF,eAAA,+BAEuBE,MAAkB,IAAK;MAC7C,IAAI,CAACQ,aAAa,CAAC,sBAAsB,EAAER,MAAM,CAAC;IACpD,CAAC;IAAAF,eAAA,+BAEuBE,MAAkB,IAAK;MAC7C,IAAI,CAACQ,aAAa,CAAC,sBAAsB,EAAER,MAAM,CAAC;IACpD,CAAC;IAAAF,eAAA,8BAEsBE,MAAkB,IAAK;MAC5C,IAAI,CAACQ,aAAa,CAAC,qBAAqB,EAAER,MAAM,CAAC;IACnD,CAAC;IAAAF,eAAA,iCAEyBE,MAAkB,IAAK;MAC/C,IAAI,CAACQ,aAAa,CAAC,wBAAwB,EAAER,MAAM,CAAC;IACtD,CAAC;IAAAF,eAAA,gCAEwBE,MAAkB,IAAK;MAC9C,IAAI,CAACQ,aAAa,CAAC,uBAAuB,EAAER,MAAM,CAAC;IACrD,CAAC;IAAAF,eAAA,gCAEwBE,MAAkB,IAAK;MAC9C,IAAI,CAACQ,aAAa,CAAC,uBAAuB,EAAER,MAAM,CAAC;IACrD,CAAC;IAAAF,eAAA,+BAEuBE,MAAkB,IAAK;MAC7C,IAAI,CAACQ,aAAa,CAAC,sBAAsB,EAAER,MAAM,CAAC;IACpD,CAAC;IAAAF,eAAA,yBAEiBE,MAAkB,IAAK;MACvC,IAAI,CAACQ,aAAa,CAAC,gBAAgB,EAAER,MAAM,CAAC;IAC9C,CAAC;IAAAF,eAAA,wBAEgBE,MAAkB,IAAK;MACtC,IAAI,CAACQ,aAAa,CAAC,eAAe,EAAER,MAAM,CAAC;IAC7C,CAAC;IArDC,IAAI,CAACH,SAAS,GAAG,CAAC,GAAGA,SAAS,CAAC;EACjC;AAqDF", "ignoreList": []}