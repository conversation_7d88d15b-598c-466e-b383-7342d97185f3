import { Page, Locator, expect } from '@playwright/test';
import { logger } from '../utils/logger';

export abstract class BasePage {
  protected page: Page;
  protected url: string;

  constructor(page: Page, url: string = '') {
    this.page = page;
    this.url = url;
  }

  /**
   * Navigate to the page
   */
  async navigate(): Promise<void> {
    logger.info(`🌐 Navigating to: ${this.url}`);
    await this.page.goto(this.url);
    await this.waitForPageLoad();
  }

  /**
   * Wait for page to load completely
   */
  async waitForPageLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
    logger.info('✅ Page loaded successfully');
  }

  /**
   * Get page title
   */
  async getTitle(): Promise<string> {
    const title = await this.page.title();
    logger.info(`📄 Page title: ${title}`);
    return title;
  }

  /**
   * Get current URL
   */
  async getCurrentUrl(): Promise<string> {
    const url = this.page.url();
    logger.info(`🔗 Current URL: ${url}`);
    return url;
  }

  /**
   * Wait for element to be visible
   */
  async waitForElement(locator: Locator, timeout: number = 30000): Promise<void> {
    await locator.waitFor({ state: 'visible', timeout });
    logger.info('👁️ Element is visible');
  }

  /**
   * Click element with retry logic
   */
  async clickElement(locator: Locator, options?: { timeout?: number; force?: boolean }): Promise<void> {
    await this.waitForElement(locator);
    await locator.click(options);
    logger.info('🖱️ Element clicked');
  }

  /**
   * Fill input field
   */
  async fillInput(locator: Locator, value: string, options?: { clear?: boolean }): Promise<void> {
    await this.waitForElement(locator);
    if (options?.clear) {
      await locator.clear();
    }
    await locator.fill(value);
    logger.info(`⌨️ Input filled with: ${value}`);
  }

  /**
   * Select option from dropdown
   */
  async selectOption(
    locator: Locator,
    value: string | { label?: string; value?: string; index?: number },
  ): Promise<void> {
    await this.waitForElement(locator);
    await locator.selectOption(value);
    logger.info(`📋 Option selected: ${JSON.stringify(value)}`);
  }

  /**
   * Get text content of element
   */
  async getElementText(locator: Locator): Promise<string> {
    await this.waitForElement(locator);
    const text = await locator.textContent() || '';
    logger.info(`📝 Element text: ${text}`);
    return text;
  }

  /**
   * Check if element is visible
   */
  async isElementVisible(locator: Locator): Promise<boolean> {
    try {
      await locator.waitFor({ state: 'visible', timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Check if element is enabled
   */
  async isElementEnabled(locator: Locator): Promise<boolean> {
    await this.waitForElement(locator);
    return await locator.isEnabled();
  }

  /**
   * Scroll element into view
   */
  async scrollToElement(locator: Locator): Promise<void> {
    await locator.scrollIntoViewIfNeeded();
    logger.info('📜 Scrolled to element');
  }

  /**
   * Take screenshot
   */
  async takeScreenshot(name: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${name}-${timestamp}.png`;
    await this.page.screenshot({ path: `logs/${filename}`, fullPage: true });
    logger.info(`📸 Screenshot saved: ${filename}`);
  }

  /**
   * Wait for specific time
   */
  async wait(milliseconds: number): Promise<void> {
    await this.page.waitForTimeout(milliseconds);
    logger.info(`⏱️ Waited for ${milliseconds}ms`);
  }

  /**
   * Verify page title
   */
  async verifyTitle(expectedTitle: string): Promise<void> {
    await expect(this.page).toHaveTitle(expectedTitle);
    logger.info(`✅ Title verified: ${expectedTitle}`);
  }

  /**
   * Verify current URL
   */
  async verifyUrl(expectedUrl: string | RegExp): Promise<void> {
    await expect(this.page).toHaveURL(expectedUrl);
    logger.info(`✅ URL verified: ${expectedUrl}`);
  }

  /**
   * Verify element text
   */
  async verifyElementText(locator: Locator, expectedText: string | RegExp): Promise<void> {
    await expect(locator).toHaveText(expectedText);
    logger.info(`✅ Element text verified: ${expectedText}`);
  }

  /**
   * Verify element is visible
   */
  async verifyElementVisible(locator: Locator): Promise<void> {
    await expect(locator).toBeVisible();
    logger.info('✅ Element visibility verified');
  }

  /**
   * Verify element is hidden
   */
  async verifyElementHidden(locator: Locator): Promise<void> {
    await expect(locator).toBeHidden();
    logger.info('✅ Element hidden state verified');
  }
}
