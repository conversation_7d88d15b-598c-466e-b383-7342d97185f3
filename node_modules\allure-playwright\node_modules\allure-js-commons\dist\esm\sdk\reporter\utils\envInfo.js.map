{"version": 3, "file": "envInfo.js", "names": ["EOL", "process", "platform", "stringifyEnvInfo", "envInfo", "lines", "key", "value", "Object", "entries", "undefined", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "escapedValue", "escapeValue", "push", "concat", "join", "parseEnvInfo", "input", "escape", "skipSpace", "isCommentLine", "newLine", "multiLine", "is<PERSON>ey", "unicode", "unicodeRemaining", "escapingUnicode", "keySpace", "sep", "line", "decodeString", "output", "char", "hex", "String", "fromCharCode", "isWhitespace", "isComment", "isSeparator", "Error", "mapcatChars", "code", "escape<PERSON><PERSON><PERSON>", "escapeWhitespace", "fn", "result", "i", "length", "charCodeAt", "isAsciiPrintable", "escapeUnicode", "toString", "prefix", "repeat"], "sources": ["../../../../../src/sdk/reporter/utils/envInfo.ts"], "sourcesContent": ["/**\n * The implementation is based on https://github.com/gagle/node-properties.\n */\nimport type { EnvironmentInfo } from \"../../types.js\";\n\nconst EOL = process.platform === \"win32\" ? \"\\r\\n\" : \"\\n\";\n\nexport const stringifyEnvInfo = (envInfo: EnvironmentInfo) => {\n  const lines: string[] = [];\n\n  for (const [key, value] of Object.entries(envInfo)) {\n    if (value !== null && value !== undefined) {\n      const escapedKey = escapeKey(key);\n      const escapedValue = escapeValue(value ?? \"\");\n      lines.push(`${escapedKey}=${escapedValue}`);\n    }\n  }\n\n  return lines.join(EOL);\n};\n\nexport const parseEnvInfo = (input: string): EnvironmentInfo => {\n  if (!input) {\n    return {};\n  }\n\n  const envInfo: EnvironmentInfo = {};\n  let escape = false;\n  let skipSpace = true;\n  let isCommentLine = false;\n  let newLine = true;\n  let multiLine = false;\n  let isKey = true;\n  let key = \"\";\n  let value = \"\";\n  let unicode: number;\n  let unicodeRemaining = 0;\n  let escapingUnicode = false;\n  let keySpace = false;\n  let sep = false;\n\n  const line = () => {\n    if (key || value || sep) {\n      envInfo[key] = value;\n      key = \"\";\n      value = \"\";\n      sep = false;\n    }\n  };\n\n  const decodeString = (output: string, char: string) => {\n    if (escapingUnicode && unicodeRemaining) {\n      // eslint-disable-next-line no-bitwise\n      unicode = (unicode << 4) + hex(char);\n      if (--unicodeRemaining) {\n        return output;\n      }\n      escape = false;\n      escapingUnicode = false;\n      return output + String.fromCharCode(unicode);\n    }\n\n    if (char === \"u\") {\n      unicode = 0;\n      escapingUnicode = true;\n      unicodeRemaining = 4;\n      return output;\n    }\n\n    escape = false;\n\n    switch (char) {\n      case \"t\":\n        return `${output}\\t`;\n      case \"r\":\n        return `${output}\\r`;\n      case \"n\":\n        return `${output}\\n`;\n      case \"f\":\n        return `${output}\\f`;\n    }\n\n    return output + char;\n  };\n\n  for (const char of input) {\n    if (char === \"\\r\") {\n      continue;\n    }\n\n    if (isCommentLine) {\n      if (char === \"\\n\") {\n        isCommentLine = false;\n        newLine = true;\n        skipSpace = true;\n      }\n      continue;\n    }\n\n    if (skipSpace) {\n      if (isWhitespace(char)) {\n        continue;\n      }\n\n      if (!multiLine && char === \"\\n\") {\n        isKey = true;\n        keySpace = false;\n        newLine = true;\n        line();\n        continue;\n      }\n      skipSpace = false;\n      multiLine = false;\n    }\n\n    if (newLine) {\n      newLine = false;\n      if (isComment(char)) {\n        isCommentLine = true;\n        continue;\n      }\n    }\n\n    if (char !== \"\\n\") {\n      if (!escape && isKey && isSeparator(char)) {\n        sep = true;\n        isKey = false;\n        keySpace = false;\n        skipSpace = true;\n        continue;\n      }\n\n      if (char === \"\\\\\") {\n        if (escape) {\n          if (escapingUnicode) {\n            continue;\n          }\n\n          if (keySpace) {\n            keySpace = false;\n            isKey = false;\n          }\n\n          if (isKey) {\n            key += \"\\\\\";\n          } else {\n            value += \"\\\\\";\n          }\n        }\n        escape = !escape;\n      } else {\n        if (keySpace) {\n          keySpace = false;\n          isKey = false;\n        }\n\n        if (isKey) {\n          if (escape) {\n            key = decodeString(key, char);\n          } else {\n            if (isWhitespace(char)) {\n              keySpace = true;\n              skipSpace = true;\n              continue;\n            }\n            key += char;\n          }\n        } else {\n          if (escape) {\n            value = decodeString(value, char);\n          } else {\n            value += char;\n          }\n        }\n      }\n    } else {\n      if (escape) {\n        if (!escapingUnicode) {\n          escape = false;\n        }\n        skipSpace = true;\n        multiLine = true;\n      } else {\n        newLine = true;\n        skipSpace = true;\n        isKey = true;\n\n        line();\n      }\n    }\n  }\n\n  line();\n\n  return envInfo;\n};\n\nconst hex = (char: string) => {\n  switch (char) {\n    case \"0\":\n      return 0;\n    case \"1\":\n      return 1;\n    case \"2\":\n      return 2;\n    case \"3\":\n      return 3;\n    case \"4\":\n      return 4;\n    case \"5\":\n      return 5;\n    case \"6\":\n      return 6;\n    case \"7\":\n      return 7;\n    case \"8\":\n      return 8;\n    case \"9\":\n      return 9;\n    case \"a\":\n    case \"A\":\n      return 10;\n    case \"b\":\n    case \"B\":\n      return 11;\n    case \"c\":\n    case \"C\":\n      return 12;\n    case \"d\":\n    case \"D\":\n      return 13;\n    case \"e\":\n    case \"E\":\n      return 14;\n    case \"f\":\n    case \"F\":\n      return 15;\n  }\n  throw new Error(`Non-hex char ${char}`);\n};\n\nconst escapeKey = (key: string) => {\n  return mapcatChars(key, (char, code) => {\n    if (isSeparator(char)) {\n      return `\\\\${char}`;\n    }\n    return escapeCharacter(char, code, true);\n  });\n};\n\nconst escapeValue = (value: string) => {\n  let escapeWhitespace = true;\n  return mapcatChars(value, (char, code) => {\n    if (!isWhitespace(char)) {\n      escapeWhitespace = false;\n    }\n    return escapeCharacter(char, code, escapeWhitespace);\n  });\n};\n\nconst mapcatChars = (value: string, fn: (char: string, code: number) => string) => {\n  let result = \"\";\n  for (let i = 0; i < value.length; i++) {\n    const char = value[i];\n    const code = value.charCodeAt(i);\n\n    result += fn(char, code);\n  }\n  return result;\n};\n\nconst escapeCharacter = (char: string, code: number, escapeWhitespace: boolean) => {\n  if (isAsciiPrintable(code)) {\n    if (char === \" \" && escapeWhitespace) {\n      return \"\\\\ \";\n    } else if (char === \"\\\\\") {\n      return \"\\\\\\\\\";\n    } else {\n      return char;\n    }\n  } else if (char === \"\\t\") {\n    return \"\\\\t\";\n  } else if (char === \"\\n\") {\n    return \"\\\\n\";\n  } else if (char === \"\\f\") {\n    return \"\\\\f\";\n  } else if (char === \"\\r\") {\n    return \"\\\\r\";\n  } else if (code < 160 || code >= 256) {\n    // Control sets 0 and 1 or non-ASCII characters\n    return escapeUnicode(code);\n  } else {\n    return char;\n  }\n};\n\nconst isWhitespace = (char: string) => {\n  switch (char) {\n    case \"\\t\":\n    case \"\\f\":\n    case \" \":\n      return true;\n  }\n  return false;\n};\n\nconst isAsciiPrintable = (code: number) => code > 31 && code < 127;\n\nconst escapeUnicode = (code: number) => {\n  const unicode = code.toString(16);\n  const prefix = \"0\".repeat(4 - unicode.length);\n  return `\\\\u${prefix}${unicode}`;\n};\n\nconst isSeparator = (char: string) => {\n  switch (char) {\n    case \"=\":\n    case \":\":\n      return true;\n  }\n  return false;\n};\n\nconst isComment = (char: string) => {\n  switch (char) {\n    case \"#\":\n    case \"!\":\n      return true;\n  }\n  return false;\n};\n"], "mappings": "AAAA;AACA;AACA;;AAGA,IAAMA,GAAG,GAAGC,OAAO,CAACC,QAAQ,KAAK,OAAO,GAAG,MAAM,GAAG,IAAI;AAExD,OAAO,IAAMC,gBAAgB,GAAIC,OAAwB,IAAK;EAC5D,IAAMC,KAAe,GAAG,EAAE;EAE1B,KAAK,IAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,OAAO,CAAC,EAAE;IAClD,IAAIG,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKG,SAAS,EAAE;MACzC,IAAMC,UAAU,GAAGC,SAAS,CAACN,GAAG,CAAC;MACjC,IAAMO,YAAY,GAAGC,WAAW,CAACP,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAE,CAAC;MAC7CF,KAAK,CAACU,IAAI,IAAAC,MAAA,CAAIL,UAAU,OAAAK,MAAA,CAAIH,YAAY,CAAE,CAAC;IAC7C;EACF;EAEA,OAAOR,KAAK,CAACY,IAAI,CAACjB,GAAG,CAAC;AACxB,CAAC;AAED,OAAO,IAAMkB,YAAY,GAAIC,KAAa,IAAsB;EAC9D,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,CAAC,CAAC;EACX;EAEA,IAAMf,OAAwB,GAAG,CAAC,CAAC;EACnC,IAAIgB,MAAM,GAAG,KAAK;EAClB,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIC,SAAS,GAAG,KAAK;EACrB,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAInB,GAAG,GAAG,EAAE;EACZ,IAAIC,KAAK,GAAG,EAAE;EACd,IAAImB,OAAe;EACnB,IAAIC,gBAAgB,GAAG,CAAC;EACxB,IAAIC,eAAe,GAAG,KAAK;EAC3B,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIC,GAAG,GAAG,KAAK;EAEf,IAAMC,IAAI,GAAGA,CAAA,KAAM;IACjB,IAAIzB,GAAG,IAAIC,KAAK,IAAIuB,GAAG,EAAE;MACvB1B,OAAO,CAACE,GAAG,CAAC,GAAGC,KAAK;MACpBD,GAAG,GAAG,EAAE;MACRC,KAAK,GAAG,EAAE;MACVuB,GAAG,GAAG,KAAK;IACb;EACF,CAAC;EAED,IAAME,YAAY,GAAGA,CAACC,MAAc,EAAEC,IAAY,KAAK;IACrD,IAAIN,eAAe,IAAID,gBAAgB,EAAE;MACvC;MACAD,OAAO,GAAG,CAACA,OAAO,IAAI,CAAC,IAAIS,GAAG,CAACD,IAAI,CAAC;MACpC,IAAI,EAAEP,gBAAgB,EAAE;QACtB,OAAOM,MAAM;MACf;MACAb,MAAM,GAAG,KAAK;MACdQ,eAAe,GAAG,KAAK;MACvB,OAAOK,MAAM,GAAGG,MAAM,CAACC,YAAY,CAACX,OAAO,CAAC;IAC9C;IAEA,IAAIQ,IAAI,KAAK,GAAG,EAAE;MAChBR,OAAO,GAAG,CAAC;MACXE,eAAe,GAAG,IAAI;MACtBD,gBAAgB,GAAG,CAAC;MACpB,OAAOM,MAAM;IACf;IAEAb,MAAM,GAAG,KAAK;IAEd,QAAQc,IAAI;MACV,KAAK,GAAG;QACN,UAAAlB,MAAA,CAAUiB,MAAM;MAClB,KAAK,GAAG;QACN,UAAAjB,MAAA,CAAUiB,MAAM;MAClB,KAAK,GAAG;QACN,UAAAjB,MAAA,CAAUiB,MAAM;MAClB,KAAK,GAAG;QACN,UAAAjB,MAAA,CAAUiB,MAAM;IACpB;IAEA,OAAOA,MAAM,GAAGC,IAAI;EACtB,CAAC;EAED,KAAK,IAAMA,IAAI,IAAIf,KAAK,EAAE;IACxB,IAAIe,IAAI,KAAK,IAAI,EAAE;MACjB;IACF;IAEA,IAAIZ,aAAa,EAAE;MACjB,IAAIY,IAAI,KAAK,IAAI,EAAE;QACjBZ,aAAa,GAAG,KAAK;QACrBC,OAAO,GAAG,IAAI;QACdF,SAAS,GAAG,IAAI;MAClB;MACA;IACF;IAEA,IAAIA,SAAS,EAAE;MACb,IAAIiB,YAAY,CAACJ,IAAI,CAAC,EAAE;QACtB;MACF;MAEA,IAAI,CAACV,SAAS,IAAIU,IAAI,KAAK,IAAI,EAAE;QAC/BT,KAAK,GAAG,IAAI;QACZI,QAAQ,GAAG,KAAK;QAChBN,OAAO,GAAG,IAAI;QACdQ,IAAI,CAAC,CAAC;QACN;MACF;MACAV,SAAS,GAAG,KAAK;MACjBG,SAAS,GAAG,KAAK;IACnB;IAEA,IAAID,OAAO,EAAE;MACXA,OAAO,GAAG,KAAK;MACf,IAAIgB,SAAS,CAACL,IAAI,CAAC,EAAE;QACnBZ,aAAa,GAAG,IAAI;QACpB;MACF;IACF;IAEA,IAAIY,IAAI,KAAK,IAAI,EAAE;MACjB,IAAI,CAACd,MAAM,IAAIK,KAAK,IAAIe,WAAW,CAACN,IAAI,CAAC,EAAE;QACzCJ,GAAG,GAAG,IAAI;QACVL,KAAK,GAAG,KAAK;QACbI,QAAQ,GAAG,KAAK;QAChBR,SAAS,GAAG,IAAI;QAChB;MACF;MAEA,IAAIa,IAAI,KAAK,IAAI,EAAE;QACjB,IAAId,MAAM,EAAE;UACV,IAAIQ,eAAe,EAAE;YACnB;UACF;UAEA,IAAIC,QAAQ,EAAE;YACZA,QAAQ,GAAG,KAAK;YAChBJ,KAAK,GAAG,KAAK;UACf;UAEA,IAAIA,KAAK,EAAE;YACTnB,GAAG,IAAI,IAAI;UACb,CAAC,MAAM;YACLC,KAAK,IAAI,IAAI;UACf;QACF;QACAa,MAAM,GAAG,CAACA,MAAM;MAClB,CAAC,MAAM;QACL,IAAIS,QAAQ,EAAE;UACZA,QAAQ,GAAG,KAAK;UAChBJ,KAAK,GAAG,KAAK;QACf;QAEA,IAAIA,KAAK,EAAE;UACT,IAAIL,MAAM,EAAE;YACVd,GAAG,GAAG0B,YAAY,CAAC1B,GAAG,EAAE4B,IAAI,CAAC;UAC/B,CAAC,MAAM;YACL,IAAII,YAAY,CAACJ,IAAI,CAAC,EAAE;cACtBL,QAAQ,GAAG,IAAI;cACfR,SAAS,GAAG,IAAI;cAChB;YACF;YACAf,GAAG,IAAI4B,IAAI;UACb;QACF,CAAC,MAAM;UACL,IAAId,MAAM,EAAE;YACVb,KAAK,GAAGyB,YAAY,CAACzB,KAAK,EAAE2B,IAAI,CAAC;UACnC,CAAC,MAAM;YACL3B,KAAK,IAAI2B,IAAI;UACf;QACF;MACF;IACF,CAAC,MAAM;MACL,IAAId,MAAM,EAAE;QACV,IAAI,CAACQ,eAAe,EAAE;UACpBR,MAAM,GAAG,KAAK;QAChB;QACAC,SAAS,GAAG,IAAI;QAChBG,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM;QACLD,OAAO,GAAG,IAAI;QACdF,SAAS,GAAG,IAAI;QAChBI,KAAK,GAAG,IAAI;QAEZM,IAAI,CAAC,CAAC;MACR;IACF;EACF;EAEAA,IAAI,CAAC,CAAC;EAEN,OAAO3B,OAAO;AAChB,CAAC;AAED,IAAM+B,GAAG,GAAID,IAAY,IAAK;EAC5B,QAAQA,IAAI;IACV,KAAK,GAAG;MACN,OAAO,CAAC;IACV,KAAK,GAAG;MACN,OAAO,CAAC;IACV,KAAK,GAAG;MACN,OAAO,CAAC;IACV,KAAK,GAAG;MACN,OAAO,CAAC;IACV,KAAK,GAAG;MACN,OAAO,CAAC;IACV,KAAK,GAAG;MACN,OAAO,CAAC;IACV,KAAK,GAAG;MACN,OAAO,CAAC;IACV,KAAK,GAAG;MACN,OAAO,CAAC;IACV,KAAK,GAAG;MACN,OAAO,CAAC;IACV,KAAK,GAAG;MACN,OAAO,CAAC;IACV,KAAK,GAAG;IACR,KAAK,GAAG;MACN,OAAO,EAAE;IACX,KAAK,GAAG;IACR,KAAK,GAAG;MACN,OAAO,EAAE;IACX,KAAK,GAAG;IACR,KAAK,GAAG;MACN,OAAO,EAAE;IACX,KAAK,GAAG;IACR,KAAK,GAAG;MACN,OAAO,EAAE;IACX,KAAK,GAAG;IACR,KAAK,GAAG;MACN,OAAO,EAAE;IACX,KAAK,GAAG;IACR,KAAK,GAAG;MACN,OAAO,EAAE;EACb;EACA,MAAM,IAAIO,KAAK,iBAAAzB,MAAA,CAAiBkB,IAAI,CAAE,CAAC;AACzC,CAAC;AAED,IAAMtB,SAAS,GAAIN,GAAW,IAAK;EACjC,OAAOoC,WAAW,CAACpC,GAAG,EAAE,CAAC4B,IAAI,EAAES,IAAI,KAAK;IACtC,IAAIH,WAAW,CAACN,IAAI,CAAC,EAAE;MACrB,YAAAlB,MAAA,CAAYkB,IAAI;IAClB;IACA,OAAOU,eAAe,CAACV,IAAI,EAAES,IAAI,EAAE,IAAI,CAAC;EAC1C,CAAC,CAAC;AACJ,CAAC;AAED,IAAM7B,WAAW,GAAIP,KAAa,IAAK;EACrC,IAAIsC,gBAAgB,GAAG,IAAI;EAC3B,OAAOH,WAAW,CAACnC,KAAK,EAAE,CAAC2B,IAAI,EAAES,IAAI,KAAK;IACxC,IAAI,CAACL,YAAY,CAACJ,IAAI,CAAC,EAAE;MACvBW,gBAAgB,GAAG,KAAK;IAC1B;IACA,OAAOD,eAAe,CAACV,IAAI,EAAES,IAAI,EAAEE,gBAAgB,CAAC;EACtD,CAAC,CAAC;AACJ,CAAC;AAED,IAAMH,WAAW,GAAGA,CAACnC,KAAa,EAAEuC,EAA0C,KAAK;EACjF,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,KAAK,CAAC0C,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAMd,IAAI,GAAG3B,KAAK,CAACyC,CAAC,CAAC;IACrB,IAAML,IAAI,GAAGpC,KAAK,CAAC2C,UAAU,CAACF,CAAC,CAAC;IAEhCD,MAAM,IAAID,EAAE,CAACZ,IAAI,EAAES,IAAI,CAAC;EAC1B;EACA,OAAOI,MAAM;AACf,CAAC;AAED,IAAMH,eAAe,GAAGA,CAACV,IAAY,EAAES,IAAY,EAAEE,gBAAyB,KAAK;EACjF,IAAIM,gBAAgB,CAACR,IAAI,CAAC,EAAE;IAC1B,IAAIT,IAAI,KAAK,GAAG,IAAIW,gBAAgB,EAAE;MACpC,OAAO,KAAK;IACd,CAAC,MAAM,IAAIX,IAAI,KAAK,IAAI,EAAE;MACxB,OAAO,MAAM;IACf,CAAC,MAAM;MACL,OAAOA,IAAI;IACb;EACF,CAAC,MAAM,IAAIA,IAAI,KAAK,IAAI,EAAE;IACxB,OAAO,KAAK;EACd,CAAC,MAAM,IAAIA,IAAI,KAAK,IAAI,EAAE;IACxB,OAAO,KAAK;EACd,CAAC,MAAM,IAAIA,IAAI,KAAK,IAAI,EAAE;IACxB,OAAO,KAAK;EACd,CAAC,MAAM,IAAIA,IAAI,KAAK,IAAI,EAAE;IACxB,OAAO,KAAK;EACd,CAAC,MAAM,IAAIS,IAAI,GAAG,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;IACpC;IACA,OAAOS,aAAa,CAACT,IAAI,CAAC;EAC5B,CAAC,MAAM;IACL,OAAOT,IAAI;EACb;AACF,CAAC;AAED,IAAMI,YAAY,GAAIJ,IAAY,IAAK;EACrC,QAAQA,IAAI;IACV,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,GAAG;MACN,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AACd,CAAC;AAED,IAAMiB,gBAAgB,GAAIR,IAAY,IAAKA,IAAI,GAAG,EAAE,IAAIA,IAAI,GAAG,GAAG;AAElE,IAAMS,aAAa,GAAIT,IAAY,IAAK;EACtC,IAAMjB,OAAO,GAAGiB,IAAI,CAACU,QAAQ,CAAC,EAAE,CAAC;EACjC,IAAMC,MAAM,GAAG,GAAG,CAACC,MAAM,CAAC,CAAC,GAAG7B,OAAO,CAACuB,MAAM,CAAC;EAC7C,aAAAjC,MAAA,CAAasC,MAAM,EAAAtC,MAAA,CAAGU,OAAO;AAC/B,CAAC;AAED,IAAMc,WAAW,GAAIN,IAAY,IAAK;EACpC,QAAQA,IAAI;IACV,KAAK,GAAG;IACR,KAAK,GAAG;MACN,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AACd,CAAC;AAED,IAAMK,SAAS,GAAIL,IAAY,IAAK;EAClC,QAAQA,IAAI;IACV,KAAK,GAAG;IACR,KAAK,GAAG;MACN,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AACd,CAAC", "ignoreList": []}