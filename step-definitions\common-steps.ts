import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { TestContext } from '../utils/TestContext';
import { logger } from '../utils/logger';

const testContext = TestContext.getInstance();

// Navigation steps
Given('I am on the {string} page', async (pageName: string) => {
  logger.step(`Navigating to ${pageName} page`);
  
  const page = testContext.getPage();
  const baseUrl = testContext.getConfig().baseUrl;
  
  let url: string;
  switch (pageName.toLowerCase()) {
  case 'login':
    url = `${baseUrl}/login`;
    break;
  case 'dashboard':
    url = `${baseUrl}/dashboard`;
    break;
  case 'contacts':
    url = `${baseUrl}/contacts`;
    break;
  case 'settings':
    url = `${baseUrl}/settings`;
    break;
  case 'home':
  case 'homepage':
    url = baseUrl;
    break;
  default:
    url = `${baseUrl}/${pageName.toLowerCase()}`;
  }
  
  await page.goto(url);
  await page.waitForLoadState('networkidle');
  logger.info(`✅ Successfully navigated to ${pageName} page`);
});

Given('I navigate to {string}', async (url: string) => {
  logger.step(`Navigating to URL: ${url}`);
  
  const page = testContext.getPage();
  const baseUrl = testContext.getConfig().baseUrl;
  
  // Handle relative URLs
  const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`;
  
  await page.goto(fullUrl);
  await page.waitForLoadState('networkidle');
  logger.info(`✅ Successfully navigated to ${fullUrl}`);
});

// Element interaction steps
When('I click on {string}', async (elementName: string) => {
  logger.step(`Clicking on ${elementName}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await locator.waitFor({ state: 'visible' });
  await locator.click();
  logger.info(`✅ Clicked on ${elementName}`);
});

When('I click on the element with text {string}', async (text: string) => {
  logger.step(`Clicking on element with text: ${text}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`text=${text}`);
  
  await locator.waitFor({ state: 'visible' });
  await locator.click();
  logger.info(`✅ Clicked on element with text: ${text}`);
});

When('I fill {string} with {string}', async (fieldName: string, value: string) => {
  logger.step(`Filling ${fieldName} with: ${value}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${fieldName}"]`);
  
  await locator.waitFor({ state: 'visible' });
  await locator.clear();
  await locator.fill(value);
  logger.info(`✅ Filled ${fieldName} with: ${value}`);
});

When('I select {string} from {string}', async (optionValue: string, selectName: string) => {
  logger.step(`Selecting ${optionValue} from ${selectName}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${selectName}"]`);
  
  await locator.waitFor({ state: 'visible' });
  await locator.selectOption(optionValue);
  logger.info(`✅ Selected ${optionValue} from ${selectName}`);
});

// Verification steps
Then('I should see {string}', async (text: string) => {
  logger.step(`Verifying text is visible: ${text}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`text=${text}`);
  
  await expect(locator).toBeVisible();
  logger.assertion(`✅ Text verified: ${text}`);
});

Then('I should not see {string}', async (text: string) => {
  logger.step(`Verifying text is not visible: ${text}`);
  
  const page = testContext.getPage();
  const locator = page.locator(`text=${text}`);
  
  await expect(locator).not.toBeVisible();
  logger.assertion(`✅ Text not visible verified: ${text}`);
});

Then('the {string} element should be visible', async (elementName: string) => {
  logger.step(`Verifying ${elementName} is visible`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await expect(locator).toBeVisible();
  logger.assertion(`✅ Element visible verified: ${elementName}`);
});

Then('the {string} element should not be visible', async (elementName: string) => {
  logger.step(`Verifying ${elementName} is not visible`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await expect(locator).not.toBeVisible();
  logger.assertion(`✅ Element not visible verified: ${elementName}`);
});

Then('the {string} element should be enabled', async (elementName: string) => {
  logger.step(`Verifying ${elementName} is enabled`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await expect(locator).toBeEnabled();
  logger.assertion(`✅ Element enabled verified: ${elementName}`);
});

Then('the {string} element should be disabled', async (elementName: string) => {
  logger.step(`Verifying ${elementName} is disabled`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await expect(locator).toBeDisabled();
  logger.assertion(`✅ Element disabled verified: ${elementName}`);
});

Then('the page title should be {string}', async (expectedTitle: string) => {
  logger.step(`Verifying page title: ${expectedTitle}`);
  
  const page = testContext.getPage();
  await expect(page).toHaveTitle(expectedTitle);
  logger.assertion(`✅ Page title verified: ${expectedTitle}`);
});

Then('the current URL should contain {string}', async (urlPart: string) => {
  logger.step(`Verifying URL contains: ${urlPart}`);
  
  const page = testContext.getPage();
  await expect(page).toHaveURL(new RegExp(urlPart));
  logger.assertion(`✅ URL contains verified: ${urlPart}`);
});

// Wait steps
When('I wait for {int} seconds', async (seconds: number) => {
  logger.step(`Waiting for ${seconds} seconds`);
  
  const page = testContext.getPage();
  await page.waitForTimeout(seconds * 1000);
  logger.info(`✅ Waited for ${seconds} seconds`);
});

When('I wait for {string} to be visible', async (elementName: string) => {
  logger.step(`Waiting for ${elementName} to be visible`);
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${elementName}"]`);
  
  await locator.waitFor({ state: 'visible', timeout: 30000 });
  logger.info(`✅ ${elementName} is now visible`);
});

// Data management steps
Given('I store {string} as {string}', async (value: string, key: string) => {
  logger.step(`Storing value as ${key}: ${value}`);
  
  testContext.setTestData(key, value);
  logger.info(`✅ Stored ${key}: ${value}`);
});

When('I use stored value {string} for {string}', async (key: string, fieldName: string) => {
  logger.step(`Using stored value ${key} for ${fieldName}`);
  
  const value = testContext.getTestData(key);
  if (!value) {
    throw new Error(`No stored value found for key: ${key}`);
  }
  
  const page = testContext.getPage();
  const locator = page.locator(`[data-testid="${fieldName}"]`);
  
  await locator.waitFor({ state: 'visible' });
  await locator.clear();
  await locator.fill(value);
  logger.info(`✅ Used stored value ${key} for ${fieldName}: ${value}`);
});

// Screenshot steps
When('I take a screenshot named {string}', async (screenshotName: string) => {
  logger.step(`Taking screenshot: ${screenshotName}`);
  
  await testContext.takeScreenshot(screenshotName);
  logger.info(`✅ Screenshot taken: ${screenshotName}`);
});
