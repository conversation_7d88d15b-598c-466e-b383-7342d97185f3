{"version": 3, "file": "FileSystemWriter.js", "names": ["copyFileSync", "mkdirSync", "writeFileSync", "join", "stringifyEnvInfo", "writeJson", "path", "data", "JSON", "stringify", "FileSystemWriter", "constructor", "config", "writeAttachment", "distFileName", "content", "buildPath", "writeAttachmentFromPath", "from", "to", "writeEnvironmentInfo", "info", "text", "writeCategoriesDefinitions", "categories", "writeGroup", "result", "concat", "uuid", "writeResult", "name", "resultsDir", "recursive"], "sources": ["../../../../../src/sdk/reporter/writer/FileSystemWriter.ts"], "sourcesContent": ["import { copyFileSync, mkdirSync, writeFileSync } from \"node:fs\";\nimport { join } from \"node:path\";\nimport type { TestResult, TestResultContainer } from \"../../../model.js\";\nimport type { Category, EnvironmentInfo } from \"../../types.js\";\nimport type { Writer } from \"../types.js\";\nimport { stringifyEnvInfo } from \"../utils/envInfo.js\";\n\nconst writeJson = (path: string, data: unknown): void => {\n  writeFileSync(path, JSON.stringify(data), \"utf-8\");\n};\n\nexport class FileSystemWriter implements Writer {\n  constructor(private config: { resultsDir: string }) {}\n\n  writeAttachment(distFileName: string, content: Buffer): void {\n    const path = this.buildPath(distFileName);\n\n    writeFileSync(path, content, \"utf-8\");\n  }\n\n  writeAttachmentFromPath(distFileName: string, from: string): void {\n    const to = this.buildPath(distFileName);\n\n    copyFileSync(from, to);\n  }\n\n  writeEnvironmentInfo(info: EnvironmentInfo): void {\n    const text = stringifyEnvInfo(info);\n    const path = this.buildPath(\"environment.properties\");\n\n    writeFileSync(path, text);\n  }\n\n  writeCategoriesDefinitions(categories: Category[]): void {\n    const path = this.buildPath(\"categories.json\");\n\n    writeJson(path, categories);\n  }\n\n  writeGroup(result: TestResultContainer): void {\n    const path = this.buildPath(`${result.uuid}-container.json`);\n    writeJson(path, result);\n  }\n\n  writeResult(result: TestResult): void {\n    const path = this.buildPath(`${result.uuid}-result.json`);\n    writeJson(path, result);\n  }\n\n  private buildPath(name: string): string {\n    mkdirSync(this.config.resultsDir, {\n      recursive: true,\n    });\n    return join(this.config.resultsDir, name);\n  }\n}\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,SAAS,EAAEC,aAAa,QAAQ,SAAS;AAChE,SAASC,IAAI,QAAQ,WAAW;AAIhC,SAASC,gBAAgB,QAAQ,qBAAqB;AAEtD,IAAMC,SAAS,GAAGA,CAACC,IAAY,EAAEC,IAAa,KAAW;EACvDL,aAAa,CAACI,IAAI,EAAEE,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC,EAAE,OAAO,CAAC;AACpD,CAAC;AAED,OAAO,MAAMG,gBAAgB,CAAmB;EAC9CC,WAAWA,CAASC,MAA8B,EAAE;IAAA,KAAhCA,MAA8B,GAA9BA,MAA8B;EAAG;EAErDC,eAAeA,CAACC,YAAoB,EAAEC,OAAe,EAAQ;IAC3D,IAAMT,IAAI,GAAG,IAAI,CAACU,SAAS,CAACF,YAAY,CAAC;IAEzCZ,aAAa,CAACI,IAAI,EAAES,OAAO,EAAE,OAAO,CAAC;EACvC;EAEAE,uBAAuBA,CAACH,YAAoB,EAAEI,IAAY,EAAQ;IAChE,IAAMC,EAAE,GAAG,IAAI,CAACH,SAAS,CAACF,YAAY,CAAC;IAEvCd,YAAY,CAACkB,IAAI,EAAEC,EAAE,CAAC;EACxB;EAEAC,oBAAoBA,CAACC,IAAqB,EAAQ;IAChD,IAAMC,IAAI,GAAGlB,gBAAgB,CAACiB,IAAI,CAAC;IACnC,IAAMf,IAAI,GAAG,IAAI,CAACU,SAAS,CAAC,wBAAwB,CAAC;IAErDd,aAAa,CAACI,IAAI,EAAEgB,IAAI,CAAC;EAC3B;EAEAC,0BAA0BA,CAACC,UAAsB,EAAQ;IACvD,IAAMlB,IAAI,GAAG,IAAI,CAACU,SAAS,CAAC,iBAAiB,CAAC;IAE9CX,SAAS,CAACC,IAAI,EAAEkB,UAAU,CAAC;EAC7B;EAEAC,UAAUA,CAACC,MAA2B,EAAQ;IAC5C,IAAMpB,IAAI,GAAG,IAAI,CAACU,SAAS,IAAAW,MAAA,CAAID,MAAM,CAACE,IAAI,oBAAiB,CAAC;IAC5DvB,SAAS,CAACC,IAAI,EAAEoB,MAAM,CAAC;EACzB;EAEAG,WAAWA,CAACH,MAAkB,EAAQ;IACpC,IAAMpB,IAAI,GAAG,IAAI,CAACU,SAAS,IAAAW,MAAA,CAAID,MAAM,CAACE,IAAI,iBAAc,CAAC;IACzDvB,SAAS,CAACC,IAAI,EAAEoB,MAAM,CAAC;EACzB;EAEQV,SAASA,CAACc,IAAY,EAAU;IACtC7B,SAAS,CAAC,IAAI,CAACW,MAAM,CAACmB,UAAU,EAAE;MAChCC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,OAAO7B,IAAI,CAAC,IAAI,CAACS,MAAM,CAACmB,UAAU,EAAED,IAAI,CAAC;EAC3C;AACF", "ignoreList": []}