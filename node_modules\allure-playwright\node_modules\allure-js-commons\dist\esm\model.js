// don't use the interface as is, use Results types instead

/* eslint-disable no-shadow */
export var Status = /*#__PURE__*/function (Status) {
  Status["FAILED"] = "failed";
  Status["BROKEN"] = "broken";
  Status["PASSED"] = "passed";
  Status["SKIPPED"] = "skipped";
  return Status;
}({});
export var StatusByPriority = [Status.FAILED, Status.BROKEN, Status.PASSED, Status.SKIPPED];

/* eslint-disable no-shadow */
export var Stage = /*#__PURE__*/function (Stage) {
  Stage["SCHEDULED"] = "scheduled";
  Stage["RUNNING"] = "running";
  Stage["FINISHED"] = "finished";
  Stage["PENDING"] = "pending";
  Stage["INTERRUPTED"] = "interrupted";
  return Stage;
}({});

/* eslint-disable no-shadow */
export var LabelName = /*#__PURE__*/function (LabelName) {
  LabelName["ALLURE_ID"] = "ALLURE_ID";
  /**
   * @deprecated please use ALLURE_ID instead
   */
  LabelName["AS_ID"] = "ALLURE_ID";
  LabelName["SUITE"] = "suite";
  LabelName["PARENT_SUITE"] = "parentSuite";
  LabelName["SUB_SUITE"] = "subSuite";
  LabelName["EPIC"] = "epic";
  LabelName["FEATURE"] = "feature";
  LabelName["STORY"] = "story";
  LabelName["SEVERITY"] = "severity";
  LabelName["TAG"] = "tag";
  LabelName["OWNER"] = "owner";
  LabelName["LEAD"] = "lead";
  LabelName["HOST"] = "host";
  LabelName["THREAD"] = "thread";
  LabelName["TEST_METHOD"] = "testMethod";
  LabelName["TEST_CLASS"] = "testClass";
  LabelName["PACKAGE"] = "package";
  LabelName["FRAMEWORK"] = "framework";
  LabelName["LANGUAGE"] = "language";
  LabelName["LAYER"] = "layer";
  return LabelName;
}({});

/* eslint-disable no-shadow */
export var Severity = /*#__PURE__*/function (Severity) {
  Severity["BLOCKER"] = "blocker";
  Severity["CRITICAL"] = "critical";
  Severity["NORMAL"] = "normal";
  Severity["MINOR"] = "minor";
  Severity["TRIVIAL"] = "trivial";
  return Severity;
}({});

/* eslint-disable no-shadow */
export var ContentType = /*#__PURE__*/function (ContentType) {
  ContentType["TEXT"] = "text/plain";
  ContentType["XML"] = "application/xml";
  ContentType["HTML"] = "text/html";
  ContentType["CSV"] = "text/csv";
  ContentType["TSV"] = "text/tab-separated-values";
  ContentType["CSS"] = "text/css";
  ContentType["URI"] = "text/uri-list";
  ContentType["SVG"] = "image/svg+xml";
  ContentType["PNG"] = "image/png";
  ContentType["JSON"] = "application/json";
  ContentType["ZIP"] = "application/zip";
  ContentType["WEBM"] = "video/webm";
  ContentType["JPEG"] = "image/jpeg";
  ContentType["MP4"] = "video/mp4";
  ContentType["IMAGEDIFF"] = "application/vnd.allure.image.diff";
  return ContentType;
}({});

/* eslint-disable no-shadow */
export var LinkType = /*#__PURE__*/function (LinkType) {
  LinkType["DEFAULT"] = "link";
  LinkType["ISSUE"] = "issue";
  LinkType["TMS"] = "tms";
  return LinkType;
}({});
//# sourceMappingURL=model.js.map