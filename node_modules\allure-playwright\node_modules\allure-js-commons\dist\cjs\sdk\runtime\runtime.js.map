{"version": 3, "file": "runtime.js", "names": ["_NoopTestRuntime", "require", "ALLURE_TEST_RUNTIME_KEY", "setGlobalTestRuntime", "runtime", "globalThis", "exports", "getGlobalTestRuntimeFunction", "getGlobalTestRuntime", "testRuntime", "_testRuntime", "noopRuntime", "getGlobalTestRuntimeWithAutoconfig", "_testRuntime2", "eval", "then", "_getGlobalTestRuntime", "_getGlobalTestRuntime2", "ignored"], "sources": ["../../../../src/sdk/runtime/runtime.ts"], "sourcesContent": ["import { noopRuntime } from \"./NoopTestRuntime.js\";\nimport type { TestRuntime } from \"./types.js\";\n\nconst ALLURE_TEST_RUNTIME_KEY = \"allureTestRuntime\";\n\nexport const setGlobalTestRuntime = (runtime: TestRuntime) => {\n  (globalThis as any)[ALLURE_TEST_RUNTIME_KEY] = () => runtime;\n};\n\nconst getGlobalTestRuntimeFunction = () => {\n  return (globalThis as any)?.[ALLURE_TEST_RUNTIME_KEY] as (() => TestRuntime | undefined) | undefined;\n};\n\nexport const getGlobalTestRuntime = (): TestRuntime => {\n  const testRuntime = getGlobalTestRuntimeFunction();\n\n  if (testRuntime) {\n    return testRuntime() ?? noopRuntime;\n  }\n\n  return noopRuntime;\n};\n\nexport const getGlobalTestRuntimeWithAutoconfig = (): TestRuntime | Promise<TestRuntime> => {\n  const testRuntime = getGlobalTestRuntimeFunction();\n\n  if (testRuntime) {\n    return testRuntime() ?? noopRuntime;\n  }\n\n  try {\n    // protection from bundlers tree-shaking visiting (webpack, rollup)\n    // @ts-ignore\n    // eslint-disable-next-line no-eval\n    return (0, eval)(\"(() => import('allure-playwright/autoconfig'))()\").then(() => {\n      return getGlobalTestRuntimeFunction()?.() ?? noopRuntime;\n    });\n  } catch (ignored) {}\n\n  return noopRuntime;\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,gBAAA,GAAAC,OAAA;AAGA,IAAMC,uBAAuB,GAAG,mBAAmB;AAE5C,IAAMC,oBAAoB,GAAIC,OAAoB,IAAK;EAC3DC,UAAU,CAASH,uBAAuB,CAAC,GAAG,MAAME,OAAO;AAC9D,CAAC;AAACE,OAAA,CAAAH,oBAAA,GAAAA,oBAAA;AAEF,IAAMI,4BAA4B,GAAGA,CAAA,KAAM;EACzC,OAAQF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAWH,uBAAuB,CAAC;AACvD,CAAC;AAEM,IAAMM,oBAAoB,GAAGA,CAAA,KAAmB;EACrD,IAAMC,WAAW,GAAGF,4BAA4B,CAAC,CAAC;EAElD,IAAIE,WAAW,EAAE;IAAA,IAAAC,YAAA;IACf,QAAAA,YAAA,GAAOD,WAAW,CAAC,CAAC,cAAAC,YAAA,cAAAA,YAAA,GAAIC,4BAAW;EACrC;EAEA,OAAOA,4BAAW;AACpB,CAAC;AAACL,OAAA,CAAAE,oBAAA,GAAAA,oBAAA;AAEK,IAAMI,kCAAkC,GAAGA,CAAA,KAA0C;EAC1F,IAAMH,WAAW,GAAGF,4BAA4B,CAAC,CAAC;EAElD,IAAIE,WAAW,EAAE;IAAA,IAAAI,aAAA;IACf,QAAAA,aAAA,GAAOJ,WAAW,CAAC,CAAC,cAAAI,aAAA,cAAAA,aAAA,GAAIF,4BAAW;EACrC;EAEA,IAAI;IACF;IACA;IACA;IACA,OAAO,CAAC,CAAC,EAAEG,IAAI,EAAE,kDAAkD,CAAC,CAACC,IAAI,CAAC,MAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MAC9E,QAAAD,qBAAA,IAAAC,sBAAA,GAAOV,4BAA4B,CAAC,CAAC,cAAAU,sBAAA,uBAA9BA,sBAAA,CAAiC,CAAC,cAAAD,qBAAA,cAAAA,qBAAA,GAAIL,4BAAW;IAC1D,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOO,OAAO,EAAE,CAAC;EAEnB,OAAOP,4BAAW;AACpB,CAAC;AAACL,OAAA,CAAAM,kCAAA,GAAAA,kCAAA", "ignoreList": []}