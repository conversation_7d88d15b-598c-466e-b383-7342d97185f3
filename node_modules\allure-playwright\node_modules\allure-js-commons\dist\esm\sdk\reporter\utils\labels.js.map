{"version": 3, "file": "labels.js", "names": ["hostname", "path", "env", "pid", "isMainThread", "threadId", "LabelName", "getRelativePath", "ENV_LABEL_PREFIX", "getEnvironmentLabels", "result", "env<PERSON><PERSON>", "startsWith", "name", "substring", "length", "trim", "push", "value", "process", "hostValue", "getHostLabel", "_env$ALLURE_HOST_NAME", "ALLURE_HOST_NAME", "HOST", "getThreadLabel", "userProvidedThreadId", "_ref", "_env$ALLURE_THREAD_NA", "THREAD", "ALLURE_THREAD_NAME", "concat", "toString", "getPackageLabel", "filepath", "PACKAGE", "split", "sep", "filter", "v", "join", "getLanguageLabel", "LANGUAGE", "getFrameworkLabel", "framework", "FRAMEWORK"], "sources": ["../../../../../src/sdk/reporter/utils/labels.ts"], "sourcesContent": ["import { hostname } from \"node:os\";\nimport path from \"node:path\";\nimport { env, pid } from \"node:process\";\nimport { isMainThread, threadId } from \"node:worker_threads\";\nimport type { Label } from \"../../../model.js\";\nimport { LabelName } from \"../../../model.js\";\nimport { getRelativePath } from \"../utils.js\";\n\nconst ENV_LABEL_PREFIX = \"ALLURE_LABEL_\";\n\nexport const getEnvironmentLabels = (): Label[] => {\n  const result: Label[] = [];\n  for (const envKey in env) {\n    if (envKey.startsWith(ENV_LABEL_PREFIX)) {\n      const name = envKey.substring(ENV_LABEL_PREFIX.length).trim();\n      if (name !== \"\") {\n        result.push({ name, value: process.env[envKey]! });\n      }\n    }\n  }\n  return result;\n};\n\nlet hostValue: string;\n\nexport const getHostLabel = (): Label => {\n  if (!hostValue) {\n    hostValue = env.ALLURE_HOST_NAME ?? hostname();\n  }\n\n  return {\n    name: LabelName.HOST,\n    value: hostValue,\n  };\n};\n\nexport const getThreadLabel = (userProvidedThreadId?: string): Label => {\n  return {\n    name: LabelName.THREAD,\n    value:\n      env.ALLURE_THREAD_NAME ??\n      userProvidedThreadId ??\n      `pid-${pid.toString()}-worker-${isMainThread ? \"main\" : threadId}`,\n  };\n};\n\nexport const getPackageLabel = (filepath: string): Label => ({\n  name: LabelName.PACKAGE,\n  value: getRelativePath(filepath)\n    .split(path.sep)\n    .filter((v) => v)\n    .join(\".\"),\n});\n\nexport const getLanguageLabel = (): Label => ({\n  name: LabelName.LANGUAGE,\n  value: \"javascript\",\n});\n\nexport const getFrameworkLabel = (framework: string): Label => ({\n  name: LabelName.FRAMEWORK,\n  value: framework,\n});\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,SAAS;AAClC,OAAOC,IAAI,MAAM,WAAW;AAC5B,SAASC,GAAG,EAAEC,GAAG,QAAQ,cAAc;AACvC,SAASC,YAAY,EAAEC,QAAQ,QAAQ,qBAAqB;AAE5D,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,eAAe,QAAQ,aAAa;AAE7C,IAAMC,gBAAgB,GAAG,eAAe;AAExC,OAAO,IAAMC,oBAAoB,GAAGA,CAAA,KAAe;EACjD,IAAMC,MAAe,GAAG,EAAE;EAC1B,KAAK,IAAMC,MAAM,IAAIT,GAAG,EAAE;IACxB,IAAIS,MAAM,CAACC,UAAU,CAACJ,gBAAgB,CAAC,EAAE;MACvC,IAAMK,IAAI,GAAGF,MAAM,CAACG,SAAS,CAACN,gBAAgB,CAACO,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC;MAC7D,IAAIH,IAAI,KAAK,EAAE,EAAE;QACfH,MAAM,CAACO,IAAI,CAAC;UAAEJ,IAAI;UAAEK,KAAK,EAAEC,OAAO,CAACjB,GAAG,CAACS,MAAM;QAAG,CAAC,CAAC;MACpD;IACF;EACF;EACA,OAAOD,MAAM;AACf,CAAC;AAED,IAAIU,SAAiB;AAErB,OAAO,IAAMC,YAAY,GAAGA,CAAA,KAAa;EACvC,IAAI,CAACD,SAAS,EAAE;IAAA,IAAAE,qBAAA;IACdF,SAAS,IAAAE,qBAAA,GAAGpB,GAAG,CAACqB,gBAAgB,cAAAD,qBAAA,cAAAA,qBAAA,GAAItB,QAAQ,CAAC,CAAC;EAChD;EAEA,OAAO;IACLa,IAAI,EAAEP,SAAS,CAACkB,IAAI;IACpBN,KAAK,EAAEE;EACT,CAAC;AACH,CAAC;AAED,OAAO,IAAMK,cAAc,GAAIC,oBAA6B,IAAY;EAAA,IAAAC,IAAA,EAAAC,qBAAA;EACtE,OAAO;IACLf,IAAI,EAAEP,SAAS,CAACuB,MAAM;IACtBX,KAAK,GAAAS,IAAA,IAAAC,qBAAA,GACH1B,GAAG,CAAC4B,kBAAkB,cAAAF,qBAAA,cAAAA,qBAAA,GACtBF,oBAAoB,cAAAC,IAAA,cAAAA,IAAA,UAAAI,MAAA,CACb5B,GAAG,CAAC6B,QAAQ,CAAC,CAAC,cAAAD,MAAA,CAAW3B,YAAY,GAAG,MAAM,GAAGC,QAAQ;EACpE,CAAC;AACH,CAAC;AAED,OAAO,IAAM4B,eAAe,GAAIC,QAAgB,KAAa;EAC3DrB,IAAI,EAAEP,SAAS,CAAC6B,OAAO;EACvBjB,KAAK,EAAEX,eAAe,CAAC2B,QAAQ,CAAC,CAC7BE,KAAK,CAACnC,IAAI,CAACoC,GAAG,CAAC,CACfC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAChBC,IAAI,CAAC,GAAG;AACb,CAAC,CAAC;AAEF,OAAO,IAAMC,gBAAgB,GAAGA,CAAA,MAAc;EAC5C5B,IAAI,EAAEP,SAAS,CAACoC,QAAQ;EACxBxB,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,OAAO,IAAMyB,iBAAiB,GAAIC,SAAiB,KAAa;EAC9D/B,IAAI,EAAEP,SAAS,CAACuC,SAAS;EACzB3B,KAAK,EAAE0B;AACT,CAAC,CAAC", "ignoreList": []}