"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "MessageHolderTestRuntime", {
  enumerable: true,
  get: function get() {
    return _MessageHolderTestRuntime.MessageHolderTestRuntime;
  }
});
Object.defineProperty(exports, "MessageTestRuntime", {
  enumerable: true,
  get: function get() {
    return _MessageTestRuntime.MessageTestRuntime;
  }
});
Object.defineProperty(exports, "getGlobalTestRuntime", {
  enumerable: true,
  get: function get() {
    return _runtime.getGlobalTestRuntime;
  }
});
Object.defineProperty(exports, "getGlobalTestRuntimeWithAutoconfig", {
  enumerable: true,
  get: function get() {
    return _runtime.getGlobalTestRuntimeWithAutoconfig;
  }
});
Object.defineProperty(exports, "setGlobalTestRuntime", {
  enumerable: true,
  get: function get() {
    return _runtime.setGlobalTestRuntime;
  }
});
var _runtime = require("./runtime.js");
var _MessageTestRuntime = require("./MessageTestRuntime.js");
var _MessageHolderTestRuntime = require("./MessageHolderTestRuntime.js");
//# sourceMappingURL=index.js.map