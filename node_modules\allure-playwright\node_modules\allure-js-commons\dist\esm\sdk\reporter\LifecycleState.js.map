{"version": 3, "file": "LifecycleState.js", "names": ["LifecycleState", "constructor", "_this", "_classPrivateFieldInitSpec", "_scopes", "Map", "_testResults", "_stepResults", "_fixturesResults", "_defineProperty", "uuid", "_classPrivateFieldGet", "get", "_this$getWrappedFixtu", "getWrappedFixtureResult", "value", "_this$getWrappedTestR", "getWrappedTestResult", "_ref", "_this$getFixtureResul", "getFixtureResult", "getTestResult", "getStepResult", "result", "scopeUuids", "arguments", "length", "undefined", "set", "delete", "scopeUuid", "type", "wrappedResult", "data", "scope", "_objectSpread", "labels", "links", "parameters", "fixtures", "tests"], "sources": ["../../../../src/sdk/reporter/LifecycleState.ts"], "sourcesContent": ["import type { FixtureResult, StepResult, TestResult } from \"../../model.js\";\nimport type { FixtureResultWrapper, FixtureType, TestResultWrapper, TestScope } from \"./types.js\";\n\nexport class LifecycleState {\n  #scopes = new Map<string, TestScope>();\n\n  #testResults = new Map<string, TestResultWrapper>();\n\n  #stepResults = new Map<string, StepResult>();\n\n  #fixturesResults = new Map<string, FixtureResultWrapper>();\n\n  getScope = (uuid: string) => this.#scopes.get(uuid);\n\n  getWrappedFixtureResult = (uuid: string) => this.#fixturesResults.get(uuid);\n\n  getFixtureResult = (uuid: string) => this.getWrappedFixtureResult(uuid)?.value;\n\n  getWrappedTestResult = (uuid: string) => this.#testResults.get(uuid);\n\n  getTestResult = (uuid: string) => this.getWrappedTestResult(uuid)?.value;\n\n  getStepResult = (uuid: string) => this.#stepResults.get(uuid);\n\n  getExecutionItem = (uuid: string): FixtureResult | TestResult | StepResult | undefined =>\n    this.getFixtureResult(uuid) ?? this.getTestResult(uuid) ?? this.getStepResult(uuid);\n\n  // test results\n  setTestResult = (uuid: string, result: TestResult, scopeUuids: string[] = []) => {\n    this.#testResults.set(uuid, { value: result, scopeUuids });\n  };\n\n  deleteTestResult = (uuid: string) => {\n    this.#testResults.delete(uuid);\n  };\n\n  // steps\n  setStepResult = (uuid: string, result: StepResult) => {\n    this.#stepResults.set(uuid, result);\n  };\n\n  deleteStepResult = (uuid: string) => {\n    this.#stepResults.delete(uuid);\n  };\n\n  // fixtures\n  setFixtureResult = (scopeUuid: string, uuid: string, type: FixtureType, result: FixtureResult) => {\n    const wrappedResult: FixtureResultWrapper = {\n      uuid,\n      type,\n      value: result,\n      scopeUuid,\n    };\n    this.#fixturesResults.set(uuid, wrappedResult);\n    return wrappedResult;\n  };\n\n  deleteFixtureResult = (uuid: string) => {\n    this.#fixturesResults.delete(uuid);\n  };\n\n  // test scopes\n  setScope = (uuid: string, data: Partial<TestScope> = {}) => {\n    const scope: TestScope = {\n      labels: [],\n      links: [],\n      parameters: [],\n      fixtures: [],\n      tests: [],\n      ...data,\n      uuid,\n    };\n    this.#scopes.set(uuid, scope);\n    return scope;\n  };\n\n  deleteScope = (uuid: string) => {\n    this.#scopes.delete(uuid);\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;AAGA,OAAO,MAAMA,cAAc,CAAC;EAAAC,YAAA;IAAA,IAAAC,KAAA;IAC1BC,0BAAA,OAAAC,OAAO,EAAG,IAAIC,GAAG,CAAoB,CAAC;IAEtCF,0BAAA,OAAAG,YAAY,EAAG,IAAID,GAAG,CAA4B,CAAC;IAEnDF,0BAAA,OAAAI,YAAY,EAAG,IAAIF,GAAG,CAAqB,CAAC;IAE5CF,0BAAA,OAAAK,gBAAgB,EAAG,IAAIH,GAAG,CAA+B,CAAC;IAACI,eAAA,mBAE/CC,IAAY,IAAKC,qBAAA,CAAKP,OAAO,EAAZ,IAAW,CAAC,CAACQ,GAAG,CAACF,IAAI,CAAC;IAAAD,eAAA,kCAExBC,IAAY,IAAKC,qBAAA,CAAKH,gBAAgB,EAArB,IAAoB,CAAC,CAACI,GAAG,CAACF,IAAI,CAAC;IAAAD,eAAA,2BAEvDC,IAAY;MAAA,IAAAG,qBAAA;MAAA,QAAAA,qBAAA,GAAK,IAAI,CAACC,uBAAuB,CAACJ,IAAI,CAAC,cAAAG,qBAAA,uBAAlCA,qBAAA,CAAoCE,KAAK;IAAA;IAAAN,eAAA,+BAEtDC,IAAY,IAAKC,qBAAA,CAAKL,YAAY,EAAjB,IAAgB,CAAC,CAACM,GAAG,CAACF,IAAI,CAAC;IAAAD,eAAA,wBAEnDC,IAAY;MAAA,IAAAM,qBAAA;MAAA,QAAAA,qBAAA,GAAK,IAAI,CAACC,oBAAoB,CAACP,IAAI,CAAC,cAAAM,qBAAA,uBAA/BA,qBAAA,CAAiCD,KAAK;IAAA;IAAAN,eAAA,wBAEvDC,IAAY,IAAKC,qBAAA,CAAKJ,YAAY,EAAjB,IAAgB,CAAC,CAACK,GAAG,CAACF,IAAI,CAAC;IAAAD,eAAA,2BAEzCC,IAAY;MAAA,IAAAQ,IAAA,EAAAC,qBAAA;MAAA,QAAAD,IAAA,IAAAC,qBAAA,GAC9B,IAAI,CAACC,gBAAgB,CAACV,IAAI,CAAC,cAAAS,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAACE,aAAa,CAACX,IAAI,CAAC,cAAAQ,IAAA,cAAAA,IAAA,GAAI,IAAI,CAACI,aAAa,CAACZ,IAAI,CAAC;IAAA;IAErF;IAAAD,eAAA,wBACgB,UAACC,IAAY,EAAEa,MAAkB,EAAgC;MAAA,IAA9BC,UAAoB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;MAC1Ed,qBAAA,CAAKL,YAAY,EAAjBJ,KAAgB,CAAC,CAAC0B,GAAG,CAAClB,IAAI,EAAE;QAAEK,KAAK,EAAEQ,MAAM;QAAEC;MAAW,CAAC,CAAC;IAC5D,CAAC;IAAAf,eAAA,2BAEmBC,IAAY,IAAK;MACnCC,qBAAA,CAAKL,YAAY,EAAjB,IAAgB,CAAC,CAACuB,MAAM,CAACnB,IAAI,CAAC;IAChC,CAAC;IAED;IAAAD,eAAA,wBACgB,CAACC,IAAY,EAAEa,MAAkB,KAAK;MACpDZ,qBAAA,CAAKJ,YAAY,EAAjB,IAAgB,CAAC,CAACqB,GAAG,CAAClB,IAAI,EAAEa,MAAM,CAAC;IACrC,CAAC;IAAAd,eAAA,2BAEmBC,IAAY,IAAK;MACnCC,qBAAA,CAAKJ,YAAY,EAAjB,IAAgB,CAAC,CAACsB,MAAM,CAACnB,IAAI,CAAC;IAChC,CAAC;IAED;IAAAD,eAAA,2BACmB,CAACqB,SAAiB,EAAEpB,IAAY,EAAEqB,IAAiB,EAAER,MAAqB,KAAK;MAChG,IAAMS,aAAmC,GAAG;QAC1CtB,IAAI;QACJqB,IAAI;QACJhB,KAAK,EAAEQ,MAAM;QACbO;MACF,CAAC;MACDnB,qBAAA,CAAKH,gBAAgB,EAArB,IAAoB,CAAC,CAACoB,GAAG,CAAClB,IAAI,EAAEsB,aAAa,CAAC;MAC9C,OAAOA,aAAa;IACtB,CAAC;IAAAvB,eAAA,8BAEsBC,IAAY,IAAK;MACtCC,qBAAA,CAAKH,gBAAgB,EAArB,IAAoB,CAAC,CAACqB,MAAM,CAACnB,IAAI,CAAC;IACpC,CAAC;IAED;IAAAD,eAAA,mBACW,UAACC,IAAY,EAAoC;MAAA,IAAlCuB,IAAwB,GAAAR,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACrD,IAAMS,KAAgB,GAAAC,aAAA,CAAAA,aAAA;QACpBC,MAAM,EAAE,EAAE;QACVC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE;MAAE,GACNP,IAAI;QACPvB;MAAI,EACL;MACDC,qBAAA,CAAKP,OAAO,EAAZF,KAAW,CAAC,CAAC0B,GAAG,CAAClB,IAAI,EAAEwB,KAAK,CAAC;MAC7B,OAAOA,KAAK;IACd,CAAC;IAAAzB,eAAA,sBAEcC,IAAY,IAAK;MAC9BC,qBAAA,CAAKP,OAAO,EAAZ,IAAW,CAAC,CAACyB,MAAM,CAACnB,IAAI,CAAC;IAC3B,CAAC;EAAA;AACH", "ignoreList": []}