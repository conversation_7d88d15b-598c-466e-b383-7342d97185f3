const common = {
  requireModule: ['ts-node/register'],
  require: ['step-definitions/**/*.ts'],
  format: [
    'progress-bar',
    'json:reports/cucumber-report.json',
    'html:reports/cucumber-report.html',
    '@cucumber/pretty-formatter',
  ],
  formatOptions: {
    snippetInterface: 'async-await',
  },
  publishQuiet: true,
};

module.exports = {
  default: {
    ...common,
    paths: ['features/**/*.feature'],
    parallel: 1,
  },
  parallel: {
    ...common,
    paths: ['features/**/*.feature'],
    parallel: 3,
  },
  dev: {
    ...common,
    paths: ['features/**/*.feature'],
    tags: 'not @skip and not @prod-only',
    parallel: 1,
  },
  staging: {
    ...common,
    paths: ['features/**/*.feature'],
    tags: 'not @skip and not @dev-only',
    parallel: 2,
  },
  prod: {
    ...common,
    paths: ['features/**/*.feature'],
    tags: 'not @skip and not @dev-only and not @staging-only',
    parallel: 1,
  },
  smoke: {
    ...common,
    paths: ['features/**/*.feature'],
    tags: '@smoke',
    parallel: 1,
  },
  regression: {
    ...common,
    paths: ['features/**/*.feature'],
    tags: '@regression',
    parallel: 3,
  },
};


