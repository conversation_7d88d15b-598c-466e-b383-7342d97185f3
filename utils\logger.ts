import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import * as path from 'path';

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

// Define colors for each log level
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

// Add colors to winston
winston.addColors(logColors);

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf((info) => {
    if (info.stack) {
      return `${info.timestamp} ${info.level}: ${info.message}\n${info.stack}`;
    }
    return `${info.timestamp} ${info.level}: ${info.message}`;
  }),
);

// Define file format (without colors)
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
);

// Create transports
const transports = [
  // Console transport
  new winston.transports.Console({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
  }),
  
  // Daily rotate file for all logs
  new DailyRotateFile({
    filename: path.join('logs', 'application-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    zippedArchive: true,
    maxSize: '20m',
    maxFiles: '14d',
    format: fileFormat,
    level: 'debug',
  }),
  
  // Daily rotate file for errors only
  new DailyRotateFile({
    filename: path.join('logs', 'error-%DATE%.log'),
    datePattern: 'YYYY-MM-DD',
    zippedArchive: true,
    maxSize: '20m',
    maxFiles: '30d',
    format: fileFormat,
    level: 'error',
  }),
];

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  levels: logLevels,
  format: fileFormat,
  transports,
  exitOnError: false,
});

// Handle uncaught exceptions and unhandled rejections
logger.exceptions.handle(
  new winston.transports.File({ filename: path.join('logs', 'exceptions.log') }),
);

logger.rejections.handle(
  new winston.transports.File({ filename: path.join('logs', 'rejections.log') }),
);

// Add test-specific logging methods
logger.step = (message: string): void => {
  logger.info(`🔸 STEP: ${message}`);
};

logger.scenario = (message: string): void => {
  logger.info(`🎬 SCENARIO: ${message}`);
};

logger.feature = (message: string): void => {
  logger.info(`🎭 FEATURE: ${message}`);
};

logger.assertion = (message: string): void => {
  logger.info(`✅ ASSERTION: ${message}`);
};

logger.action = (message: string): void => {
  logger.info(`🎯 ACTION: ${message}`);
};

// Export logger
export { logger };

// Export types for TypeScript
declare module 'winston' {
  interface Logger {
    step(message: string): void;
    scenario(message: string): void;
    feature(message: string): void;
    assertion(message: string): void;
    action(message: string): void;
  }
}
