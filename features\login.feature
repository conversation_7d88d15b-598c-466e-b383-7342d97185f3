@login @smoke
Feature: User Login
  As a user of SmoothContact
  I want to be able to login to the application
  So that I can access my account and manage my contacts

  Background:
    Given I am on the login page

  @positive @critical
  Scenario: Successful login with valid credentials
    When I login with valid credentials
    Then I should be redirected to the dashboard
    And I should see a welcome message for "Test User"

  @positive @admin
  Scenario: Successful login with admin credentials
    When I login with admin credentials
    Then I should be redirected to the dashboard
    And I should see a welcome message for "Admin User"

  @negative @critical
  Scenario: Failed login with invalid credentials
    When I login with invalid credentials
    Then I should see a login error message
    And I should remain on the login page

  @negative
  Scenario: Failed login with empty username
    When I enter username ""
    And I enter password "validpassword"
    And I click the login button
    Then I should see the error message "Username is required"
    And I should remain on the login page

  @negative
  Scenario: Failed login with empty password
    When I enter username "<EMAIL>"
    And I enter password ""
    And I click the login button
    Then I should see the error message "Password is required"
    And I should remain on the login page

  @negative
  Scenario: Failed login with invalid email format
    When I enter username "invalid-email"
    And I enter password "validpassword"
    And I click the login button
    Then I should see the error message "Please enter a valid email address"
    And I should remain on the login page

  @positive
  Scenario: Remember me functionality
    When I enter username "<EMAIL>"
    And I enter password "UserPass123!"
    And I check the remember me checkbox
    And I click the login button
    Then I should be redirected to the dashboard
    And the remember me checkbox should be checked

  @navigation
  Scenario: Navigate to forgot password page
    When I click the forgot password link
    Then the current URL should contain "forgot-password"

  @navigation
  Scenario: Navigate to sign up page
    When I click the sign up link
    Then the current URL should contain "signup"

  @ui
  Scenario: Login form elements are displayed
    Then the login form should be displayed
    And the "username-input" element should be visible
    And the "password-input" element should be visible
    And the "login-button" element should be visible
    And the "forgot-password-link" element should be visible
    And the "signup-link" element should be visible

  @accessibility
  Scenario: Login button state management
    Then the login button should be enabled
    When I enter username "<EMAIL>"
    Then the login button should be enabled
    When I enter password "password123"
    Then the login button should be enabled
