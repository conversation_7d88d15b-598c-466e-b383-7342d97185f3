import { Before, After, BeforeAll, AfterAll, Status } from '@cucumber/cucumber';
import { chromium, <PERSON><PERSON><PERSON>, BrowserContext, Page } from '@playwright/test';
import { TestContext } from '../utils/TestContext';
import { logger } from '../utils/logger';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

let browser: Browser;
let context: BrowserContext;
let page: Page;

BeforeAll(async () => {
  logger.info('🚀 Starting test suite...');
  
  // Launch browser
  browser = await chromium.launch({
    headless: process.env.HEADLESS === 'true',
    slowMo: parseInt(process.env.SLOW_MO || '0'),
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
  });
  
  logger.info('🌐 Browser launched successfully');
});

Before(async (scenario) => {
  logger.scenario(`Starting scenario: ${scenario.pickle.name}`);
  
  // Create new browser context for each scenario
  context = await browser.newContext({
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    acceptDownloads: true,
  });
  
  // Create new page
  page = await context.newPage();
  
  // Set up TestContext
  const testContext = TestContext.getInstance();
  testContext.setBrowser(browser);
  testContext.setContext(context);
  testContext.setPage(page);
  
  // Add console logging
  page.on('console', (msg) => {
    if (msg.type() === 'error') {
      logger.error(`Browser console error: ${msg.text()}`);
    } else if (msg.type() === 'warning') {
      logger.warn(`Browser console warning: ${msg.text()}`);
    }
  });
  
  // Add page error handling
  page.on('pageerror', (error) => {
    logger.error(`Page error: ${error.message}`);
  });
  
  // Add request failure logging
  page.on('requestfailed', (request) => {
    logger.warn(`Request failed: ${request.url()} - ${request.failure()?.errorText}`);
  });
  
  logger.info('📄 New page context created for scenario');
});

After(async function (scenario) {
  const testContext = TestContext.getInstance();
  
  if (scenario.result?.status === Status.FAILED) {
    logger.error(`❌ Scenario failed: ${scenario.pickle.name}`);
    
    // Take screenshot on failure
    try {
      const screenshotPath = await testContext.takeScreenshot(`failed-${scenario.pickle.name.replace(/\s+/g, '-')}`);
      logger.info(`📸 Failure screenshot saved: ${screenshotPath}`);
      
      // Attach screenshot to Cucumber report (if using Allure or similar)
      if (this.attach) {
        const screenshot = await page.screenshot({ fullPage: true });
        this.attach(screenshot, 'image/png');
      }
    } catch (error) {
      logger.error(`Failed to take screenshot: ${error}`);
    }
    
    // Save page HTML for debugging
    try {
      const html = await page.content();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fs = require('fs');
      fs.writeFileSync(`logs/failed-page-${timestamp}.html`, html);
      logger.info('💾 Page HTML saved for debugging');
    } catch (error) {
      logger.error(`Failed to save page HTML: ${error}`);
    }
  } else {
    logger.info(`✅ Scenario passed: ${scenario.pickle.name}`);
  }
  
  // Close page and context
  try {
    await page.close();
    await context.close();
    logger.info('🧹 Page and context closed');
  } catch (error) {
    logger.error(`Error closing page/context: ${error}`);
  }
  
  // Clear test context data
  testContext.clearTestData();
});

AfterAll(async () => {
  logger.info('🏁 Test suite completed');
  
  // Close browser
  if (browser) {
    await browser.close();
    logger.info('🌐 Browser closed');
  }
  
  // Reset test context
  TestContext.reset();
  
  logger.info('✅ Cleanup completed successfully');
});

// Export for use in step definitions
export { browser, context, page };
