@dashboard @smoke
Feature: Dashboard Functionality
  As a logged-in user
  I want to access the dashboard
  So that I can view my account overview and navigate to different sections

  Background:
    Given I am on the login page
    When I login with valid credentials
    Then I should be redirected to the dashboard

  @positive @critical
  Scenario: Dashboard loads successfully
    Then I should see "Welcome"
    And the "welcome-message" element should be visible
    And the "navigation-menu" element should be visible
    And the "user-profile-dropdown" element should be visible

  @navigation @critical
  Scenario: Navigate to contacts page
    When I click on "contacts-link"
    Then the current URL should contain "contacts"

  @navigation
  Scenario: Navigate to reports page
    When I click on "reports-link"
    Then the current URL should contain "reports"

  @navigation
  Scenario: Navigate to settings page
    When I click on "settings-link"
    Then the current URL should contain "settings"

  @search
  Scenario: Search functionality
    When I fill "search-input" with "test query"
    And I wait for 2 seconds
    Then I should see search results

  @profile
  Scenario: User profile dropdown
    When I click on "user-profile-dropdown"
    Then the "logout-button" element should be visible

  @logout @critical
  Scenario: Successful logout
    When I click on "user-profile-dropdown"
    And I click on "logout-button"
    Then the current URL should contain "login"
    And the login form should be displayed

  @notifications
  Scenario: Notification bell functionality
    When I click on "notification-bell"
    Then I should see notification panel

  @data
  Scenario: Dashboard statistics display
    Then the "dashboard-stats" element should be visible
    And I should see statistics for contacts
    And I should see statistics for recent activities

  @responsive @dev-only
  Scenario: Dashboard responsive design
    When I resize the browser to mobile view
    Then the navigation menu should be collapsed
    And the mobile menu button should be visible

  @performance @regression
  Scenario: Dashboard loads within acceptable time
    When I take a screenshot named "dashboard-loaded"
    Then the dashboard should load within 3 seconds
    And all dashboard components should be visible
