{"version": 3, "file": "runtime.js", "names": ["noopRuntime", "ALLURE_TEST_RUNTIME_KEY", "setGlobalTestRuntime", "runtime", "globalThis", "getGlobalTestRuntimeFunction", "getGlobalTestRuntime", "testRuntime", "_testRuntime", "getGlobalTestRuntimeWithAutoconfig", "_testRuntime2", "eval", "then", "_getGlobalTestRuntime", "_getGlobalTestRuntime2", "ignored"], "sources": ["../../../../src/sdk/runtime/runtime.ts"], "sourcesContent": ["import { noopRuntime } from \"./NoopTestRuntime.js\";\nimport type { TestRuntime } from \"./types.js\";\n\nconst ALLURE_TEST_RUNTIME_KEY = \"allureTestRuntime\";\n\nexport const setGlobalTestRuntime = (runtime: TestRuntime) => {\n  (globalThis as any)[ALLURE_TEST_RUNTIME_KEY] = () => runtime;\n};\n\nconst getGlobalTestRuntimeFunction = () => {\n  return (globalThis as any)?.[ALLURE_TEST_RUNTIME_KEY] as (() => TestRuntime | undefined) | undefined;\n};\n\nexport const getGlobalTestRuntime = (): TestRuntime => {\n  const testRuntime = getGlobalTestRuntimeFunction();\n\n  if (testRuntime) {\n    return testRuntime() ?? noopRuntime;\n  }\n\n  return noopRuntime;\n};\n\nexport const getGlobalTestRuntimeWithAutoconfig = (): TestRuntime | Promise<TestRuntime> => {\n  const testRuntime = getGlobalTestRuntimeFunction();\n\n  if (testRuntime) {\n    return testRuntime() ?? noopRuntime;\n  }\n\n  try {\n    // protection from bundlers tree-shaking visiting (webpack, rollup)\n    // @ts-ignore\n    // eslint-disable-next-line no-eval\n    return (0, eval)(\"(() => import('allure-playwright/autoconfig'))()\").then(() => {\n      return getGlobalTestRuntimeFunction()?.() ?? noopRuntime;\n    });\n  } catch (ignored) {}\n\n  return noopRuntime;\n};\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,sBAAsB;AAGlD,IAAMC,uBAAuB,GAAG,mBAAmB;AAEnD,OAAO,IAAMC,oBAAoB,GAAIC,OAAoB,IAAK;EAC3DC,UAAU,CAASH,uBAAuB,CAAC,GAAG,MAAME,OAAO;AAC9D,CAAC;AAED,IAAME,4BAA4B,GAAGA,CAAA,KAAM;EACzC,OAAQD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAWH,uBAAuB,CAAC;AACvD,CAAC;AAED,OAAO,IAAMK,oBAAoB,GAAGA,CAAA,KAAmB;EACrD,IAAMC,WAAW,GAAGF,4BAA4B,CAAC,CAAC;EAElD,IAAIE,WAAW,EAAE;IAAA,IAAAC,YAAA;IACf,QAAAA,YAAA,GAAOD,WAAW,CAAC,CAAC,cAAAC,YAAA,cAAAA,YAAA,GAAIR,WAAW;EACrC;EAEA,OAAOA,WAAW;AACpB,CAAC;AAED,OAAO,IAAMS,kCAAkC,GAAGA,CAAA,KAA0C;EAC1F,IAAMF,WAAW,GAAGF,4BAA4B,CAAC,CAAC;EAElD,IAAIE,WAAW,EAAE;IAAA,IAAAG,aAAA;IACf,QAAAA,aAAA,GAAOH,WAAW,CAAC,CAAC,cAAAG,aAAA,cAAAA,aAAA,GAAIV,WAAW;EACrC;EAEA,IAAI;IACF;IACA;IACA;IACA,OAAO,CAAC,CAAC,EAAEW,IAAI,EAAE,kDAAkD,CAAC,CAACC,IAAI,CAAC,MAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MAC9E,QAAAD,qBAAA,IAAAC,sBAAA,GAAOT,4BAA4B,CAAC,CAAC,cAAAS,sBAAA,uBAA9BA,sBAAA,CAAiC,CAAC,cAAAD,qBAAA,cAAAA,qBAAA,GAAIb,WAAW;IAC1D,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOe,OAAO,EAAE,CAAC;EAEnB,OAAOf,WAAW;AACpB,CAAC", "ignoreList": []}