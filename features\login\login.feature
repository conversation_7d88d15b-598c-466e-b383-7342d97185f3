@login
Feature: Login Functionality
  As a user of SmoothContact
  I want to be able to login to the application
  So that I can access my account and manage my contacts

  Background:
    Given I am on the login page

  @smoke @login
  Scenario: LGI-01: Login page loads
    Then the login page should be loaded
    And the logo should be visible
    And the page title should be visible
    And the email input should be visible
    And the password input should be visible
    And the submit button should be visible

  @smoke @login
  Scenario: LGI-02: User can login with valid credentials
    When I enter valid email and password
    And I submit the login form
    Then I should be redirected to dashboard or home
    And I should see authenticated indicator

  @login
  Scenario: LGI-03: Password empty shows required message
    When I enter valid email
    And I leave password empty
    And I submit the login form
    Then I should see the error message "パスワードが必須"

  @login
  Scenario: LGI-04: Password shorter than 6 shows min-length message
    When I enter valid email
    And I enter password "12345"
    And I submit the login form
    Then I should see the error message "パスワードは6文字以上で入力してください"

  @login
  Scenario: LGI-05: Password with disallowed characters shows policy message
    When I enter valid email
    And I enter password "abcDEF!@#"
    And I submit the login form
    Then I should see the error message "パスワードに使える文字は半角のアルファベット、\"_\" ＆ \"-\"記号"

  @login
  Scenario: LGI-06: Password longer than 16 shows max-length message
    When I enter valid email
    And I enter password "abcdefghijklmnopq"
    And I submit the login form
    Then I should see the error message "パスワードは16文字以内で入力してください"

  @regression @login
  Scenario: LGI-07: Invalid email or password shows auth error
    When I enter email "<EMAIL>"
    And I enter password "wrongpassword"
    And I submit the login form
    Then I should see the error message "メールアドレスが存在しません。"



  @regression @login
  Scenario: LGI-08: Logout invalidates session and returns to login
    Given I am logged in with valid credentials
    When I logout
    Then I should be redirected to login page
    And I should not be authenticated

  @visual @login
  Scenario: LGI-09: Visual regression baseline for login page
    Then the login page should match the visual baseline

  @a11y @login
  Scenario: LGI-10: Accessibility smoke on login page
    Then the login page should pass accessibility checks

  @login
  Scenario Outline: Password validation messages
    When I enter "<email>" and "<password>" and submit
    Then I should see the error message "<expected>"

    Examples:
      | email                                      | password           | expected                                                    |
      | <EMAIL>     |                    | パスワードが必須                                            |
      | <EMAIL>     | 12345              | パスワードは6文字以上で入力してください                    |
      | <EMAIL>     | abcDEF!@#          | パスワードに使える文字は半角のアルファベット、\"_\" ＆ \"-\"記号 |
      | <EMAIL>     | abcdefghijklmnopq  | パスワードは16文字以内で入力してください                   |
      | <EMAIL>                        | wrongpassword      | メールアドレスが存在しません。                              |
