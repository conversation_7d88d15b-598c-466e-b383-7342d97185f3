{"version": 3, "file": "MessageHolderTestRuntime.js", "names": ["MessageTestRuntime", "MessageHolderTestRuntime", "constructor", "arguments", "_defineProperty", "sendMessage", "message", "_this", "_asyncToGenerator", "messagesHolder", "push", "Promise", "resolve", "messages"], "sources": ["../../../../src/sdk/runtime/MessageHolderTestRuntime.ts"], "sourcesContent": ["import type { RuntimeMessage } from \"../types.js\";\nimport { MessageTestRuntime } from \"./MessageTestRuntime.js\";\n\nexport class MessageHolderTestRuntime extends MessageTestRuntime {\n  private messagesHolder: RuntimeMessage[] = [];\n\n  async sendMessage(message: RuntimeMessage) {\n    this.messagesHolder.push(message);\n    return Promise.resolve();\n  }\n\n  messages(): RuntimeMessage[] {\n    return [...this.messagesHolder];\n  }\n}\n"], "mappings": ";;;;;AACA,SAASA,kBAAkB,QAAQ,yBAAyB;AAE5D,OAAO,MAAMC,wBAAwB,SAASD,kBAAkB,CAAC;EAAAE,YAAA;IAAA,SAAAC,SAAA;IAAAC,eAAA,yBACpB,EAAE;EAAA;EAEvCC,WAAWA,CAACC,OAAuB,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACzCD,KAAI,CAACE,cAAc,CAACC,IAAI,CAACJ,OAAO,CAAC;MACjC,OAAOK,OAAO,CAACC,OAAO,CAAC,CAAC;IAAC;EAC3B;EAEAC,QAAQA,CAAA,EAAqB;IAC3B,OAAO,CAAC,GAAG,IAAI,CAACJ,cAAc,CAAC;EACjC;AACF", "ignoreList": []}