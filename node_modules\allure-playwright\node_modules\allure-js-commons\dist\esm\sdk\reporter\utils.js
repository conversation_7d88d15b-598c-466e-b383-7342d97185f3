function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == typeof i ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != typeof i) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
import { createHash, randomUUID } from "node:crypto";
import fs from "node:fs";
import { readFile } from "node:fs/promises";
import path from "node:path";
import process from "node:process";
import { LabelName, LinkType, StatusByPriority } from "../../model.js";
import { FileSystemWriter } from "./writer/FileSystemWriter.js";
import { MessageWriter } from "./writer/MessageWriter.js";
export var randomUuid = () => {
  return randomUUID();
};
export var md5 = str => {
  return createHash("md5").update(str).digest("hex");
};
export var getTestResultHistoryId = result => {
  var _result$testCaseId;
  if (result.historyId) {
    return result.historyId;
  }
  var tcId = (_result$testCaseId = result.testCaseId) !== null && _result$testCaseId !== void 0 ? _result$testCaseId : result.fullName ? md5(result.fullName) : null;
  if (!tcId) {
    return "";
  }
  var paramsString = result.parameters.filter(p => !(p !== null && p !== void 0 && p.excluded)).sort((a, b) => {
    var _a$name, _a$value;
    return ((_a$name = a.name) === null || _a$name === void 0 ? void 0 : _a$name.localeCompare(b === null || b === void 0 ? void 0 : b.name)) || ((_a$value = a.value) === null || _a$value === void 0 ? void 0 : _a$value.localeCompare(b === null || b === void 0 ? void 0 : b.value));
  }).map(p => {
    var _p$name, _p$value;
    return "".concat((_p$name = p.name) !== null && _p$name !== void 0 ? _p$name : "null", ":").concat((_p$value = p.value) !== null && _p$value !== void 0 ? _p$value : "null");
  }).join(",");
  var paramsHash = md5(paramsString);
  return "".concat(tcId, ":").concat(paramsHash);
};
export var getTestResultTestCaseId = result => {
  return result.fullName ? md5(result.fullName) : undefined;
};
var statusToPriority = status => {
  if (!status) {
    return -1;
  }
  return StatusByPriority.indexOf(status);
};
export var getWorstTestStepResult = steps => {
  if (steps.length === 0) {
    return;
  }
  return [...steps].sort((a, b) => statusToPriority(a.status) - statusToPriority(b.status))[0];
};
export var readImageAsBase64 = /*#__PURE__*/function () {
  var _ref = _asyncToGenerator(function* (filePath) {
    try {
      var file = yield readFile(filePath, {
        encoding: "base64"
      });
      return file ? "data:image/png;base64,".concat(file) : undefined;
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error("could not read file ".concat(filePath), e);
      return undefined;
    }
  });
  return function readImageAsBase64(_x) {
    return _ref.apply(this, arguments);
  };
}();
export var getProjectRoot = (() => {
  var cachedProjectRoot = null;
  var resolveProjectRootByPath = () => {
    var cwd = process.cwd();
    var nextDir = cwd;
    var dir;
    do {
      dir = nextDir;
      try {
        fs.accessSync(path.join(dir, "package.json"), fs.constants.F_OK);

        // package.json exists; use the directory as the project root
        return dir;
      } catch (_unused) {}
      nextDir = path.dirname(dir);
    } while (nextDir.length < dir.length);

    // package.json doesn't exist in any parent; fall back to CWD
    return cwd;
  };
  return () => {
    if (!cachedProjectRoot) {
      cachedProjectRoot = resolveProjectRootByPath();
    }
    return cachedProjectRoot;
  };
})();
export var getRelativePath = filepath => {
  if (path.isAbsolute(filepath)) {
    var projectRoot = getProjectRoot();
    filepath = path.relative(projectRoot, filepath);
  }
  return filepath;
};
export var getPosixPath = filepath => {
  if (process.platform === "win32") {
    return filepath.replaceAll("\\", "/");
  }
  return filepath;
};
export var deepClone = obj => JSON.parse(JSON.stringify(obj));
export var getSuiteLabels = suites => {
  if (suites.length === 0) {
    return [];
  }
  var [parentSuite, suite, ...subSuites] = suites;
  var labels = [];
  if (parentSuite) {
    labels.push({
      name: LabelName.PARENT_SUITE,
      value: parentSuite
    });
  }
  if (suite) {
    labels.push({
      name: LabelName.SUITE,
      value: suite
    });
  }
  if (subSuites.length > 0) {
    labels.push({
      name: LabelName.SUB_SUITE,
      value: subSuites.join(" > ")
    });
  }
  return labels;
};
var suiteLabelNames = [LabelName.PARENT_SUITE, LabelName.SUITE, LabelName.SUB_SUITE];

/**
 * Resolves suite labels for the given test results and add default lables if there is no any suite label.
 * @example
 * ```ts
 * ensureSuiteLabels({ labels: [{ name: "suite", value: "foo" }] }, ["bar"]) // => [{ name: "suite", value: "foo" }]
 * ensureSuiteLabels({ labels: [] }, ["bar"]) // => [{ name: "parentSuite", value: "bar" }]
 * ```
 * @param test - Test result to resolve suite labels for
 * @param defaultSuites - Default suites to add if there is no any suite label
 * @returns Actual suite labels
 */
export var ensureSuiteLabels = (test, defaultSuites) => {
  var _test$labels;
  if (!((_test$labels = test.labels) !== null && _test$labels !== void 0 && _test$labels.find(l => suiteLabelNames.includes(l.name)))) {
    var _test$labels2;
    test.labels = [...((_test$labels2 = test.labels) !== null && _test$labels2 !== void 0 ? _test$labels2 : []), ...getSuiteLabels(defaultSuites)];
  }
  return suiteLabelNames.map(name => {
    var _test$labels3;
    return (_test$labels3 = test.labels) === null || _test$labels3 === void 0 ? void 0 : _test$labels3.find(l => l.name === name);
  }).filter(Boolean);
};
var reRegExpChar = /[\\^$.*+?()[\]{}|]/g,
  reHasRegExpChar = RegExp(reRegExpChar.source);
export var escapeRegExp = value => {
  return reHasRegExpChar.test(value) ? value.replace(reRegExpChar, "\\$&") : value;
};

// TODO: may also use URL.canParse instead (requires node.js v18.17, v19.9, or higher)
var isUrl = potentialUrl => {
  // Short-circuits the check for many short URL cases, bypassing the try-catch logic.
  if (potentialUrl.indexOf(":") === -1) {
    return false;
  }

  // There is ':' in the string: a potential scheme separator.
  // The string might be a proper URL already.
  try {
    new URL(potentialUrl);
    return true;
  } catch (e) {
    return false;
  }
};
export var applyLinkTemplate = (template, value) => typeof template === "string" ? template.replace("%s", value) : template(value);
export var formatLink = (templates, link) => {
  var {
    url: originalUrl,
    name,
    type
  } = link;
  if (isUrl(originalUrl)) {
    return link;
  } else {
    var _templates;
    var formattedLink = _objectSpread({}, link);
    var {
      urlTemplate,
      nameTemplate
    } = (_templates = templates[type !== null && type !== void 0 ? type : LinkType.DEFAULT]) !== null && _templates !== void 0 ? _templates : {};
    if (urlTemplate !== undefined) {
      formattedLink.url = applyLinkTemplate(urlTemplate, originalUrl);
    }
    if (name === undefined && nameTemplate !== undefined) {
      formattedLink.name = applyLinkTemplate(nameTemplate, originalUrl);
    }
    return formattedLink;
  }
};
export var formatLinks = (templates, links) => links.map(link => formatLink(templates, link));
export var createDefaultWriter = config => {
  return process.env.ALLURE_TEST_MODE ? new MessageWriter(config.emitter) : new FileSystemWriter({
    resultsDir: config.resultsDir || "./allure-results"
  });
};
//# sourceMappingURL=utils.js.map