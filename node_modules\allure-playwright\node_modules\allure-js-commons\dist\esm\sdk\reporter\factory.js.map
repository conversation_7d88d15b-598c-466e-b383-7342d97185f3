{"version": 3, "file": "factory.js", "names": ["Stage", "Status", "createTestResultContainer", "uuid", "children", "befores", "afters", "createFixtureResult", "status", "BROKEN", "statusDetails", "stage", "PENDING", "steps", "attachments", "parameters", "createStepResult", "undefined", "createTestResult", "historyUuid", "name", "historyId", "labels", "links"], "sources": ["../../../../src/sdk/reporter/factory.ts"], "sourcesContent": ["import type { FixtureResult, StepResult, TestResult, TestResultContainer } from \"../../model.js\";\nimport { Stage, Status } from \"../../model.js\";\n\nexport const createTestResultContainer = (uuid: string): TestResultContainer => {\n  return {\n    uuid,\n    children: [],\n    befores: [],\n    afters: [],\n  };\n};\n\nexport const createFixtureResult = (): FixtureResult => {\n  return {\n    status: Status.BROKEN,\n    statusDetails: {},\n    stage: Stage.PENDING,\n    steps: [],\n    attachments: [],\n    parameters: [],\n  };\n};\n\nexport const createStepResult = (): StepResult => {\n  return {\n    status: undefined,\n    statusDetails: {},\n    stage: Stage.PENDING,\n    steps: [],\n    attachments: [],\n    parameters: [],\n  };\n};\n\nexport const createTestResult = (uuid: string, historyUuid?: string): TestResult => {\n  return {\n    uuid,\n    name: \"\",\n    historyId: historyUuid,\n    status: undefined,\n    statusDetails: {},\n    stage: Stage.PENDING,\n    steps: [],\n    attachments: [],\n    parameters: [],\n    labels: [],\n    links: [],\n  };\n};\n"], "mappings": "AACA,SAASA,KAAK,EAAEC,MAAM,QAAQ,gBAAgB;AAE9C,OAAO,IAAMC,yBAAyB,GAAIC,IAAY,IAA0B;EAC9E,OAAO;IACLA,IAAI;IACJC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE;EACV,CAAC;AACH,CAAC;AAED,OAAO,IAAMC,mBAAmB,GAAGA,CAAA,KAAqB;EACtD,OAAO;IACLC,MAAM,EAAEP,MAAM,CAACQ,MAAM;IACrBC,aAAa,EAAE,CAAC,CAAC;IACjBC,KAAK,EAAEX,KAAK,CAACY,OAAO;IACpBC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE;EACd,CAAC;AACH,CAAC;AAED,OAAO,IAAMC,gBAAgB,GAAGA,CAAA,KAAkB;EAChD,OAAO;IACLR,MAAM,EAAES,SAAS;IACjBP,aAAa,EAAE,CAAC,CAAC;IACjBC,KAAK,EAAEX,KAAK,CAACY,OAAO;IACpBC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE;EACd,CAAC;AACH,CAAC;AAED,OAAO,IAAMG,gBAAgB,GAAGA,CAACf,IAAY,EAAEgB,WAAoB,KAAiB;EAClF,OAAO;IACLhB,IAAI;IACJiB,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEF,WAAW;IACtBX,MAAM,EAAES,SAAS;IACjBP,aAAa,EAAE,CAAC,CAAC;IACjBC,KAAK,EAAEX,KAAK,CAACY,OAAO;IACpBC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdO,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE;EACT,CAAC;AACH,CAAC", "ignoreList": []}