{"version": 3, "file": "types.js", "names": ["ALLURE_METADATA_CONTENT_TYPE", "ALLURE_RUNTIME_MESSAGE_CONTENT_TYPE"], "sources": ["../../../../src/sdk/reporter/types.ts"], "sourcesContent": ["import type {\n  FixtureResult,\n  Label,\n  LabelName,\n  <PERSON>,\n  <PERSON>Type,\n  Parameter,\n  StepResult,\n  TestResult,\n  TestResultContainer,\n} from \"../../model.js\";\nimport type { Category, EnvironmentInfo } from \"../types.js\";\n\nexport const ALLURE_METADATA_CONTENT_TYPE = \"application/vnd.allure.metadata+json\";\nexport const ALLURE_RUNTIME_MESSAGE_CONTENT_TYPE = \"application/vnd.allure.message+json\";\n\nexport interface LifecycleListener {\n  beforeTestResultStart?: (result: TestResult) => void;\n\n  afterTestResultStart?: (result: TestResult) => void;\n\n  beforeTestResultStop?: (result: TestResult) => void;\n\n  afterTestResultStop?: (result: TestResult) => void;\n\n  beforeTestResultUpdate?: (result: TestResult) => void;\n\n  afterTestResultUpdate?: (result: TestResult) => void;\n\n  beforeTestResultWrite?: (result: TestResult) => void;\n\n  afterTestResultWrite?: (result: TestResult) => void;\n\n  beforeStepStop?: (result: StepResult) => void;\n\n  afterStepStop?: (result: StepResult) => void;\n}\n\nexport type LinkTemplate = string | ((url: string) => string);\n\nexport type LinkTypeOptions = {\n  urlTemplate: LinkTemplate;\n  nameTemplate?: LinkTemplate;\n};\n\nexport type LinkConfig<TOpts extends LinkTypeOptions = LinkTypeOptions> = Partial<Record<LinkType, TOpts>> &\n  Record<string, TOpts>;\n\nexport type WriterDescriptor = [cls: string, ...args: readonly unknown[]] | string;\n\nexport type GlobalLabelsConfig = Partial<Record<LabelName, string | string[]>> & Record<string, string | string[]>;\n\nexport interface ReporterConfig {\n  readonly resultsDir?: string;\n  readonly links?: LinkConfig;\n  readonly globalLabels?: Label[] | GlobalLabelsConfig;\n  readonly listeners?: LifecycleListener[];\n  readonly environmentInfo?: EnvironmentInfo;\n  readonly categories?: Category[];\n}\n\nexport interface ReporterRuntimeConfig extends Omit<ReporterConfig, \"resultsDir\"> {\n  readonly writer: Writer | WriterDescriptor;\n}\n\nexport interface Writer {\n  writeResult(result: TestResult): void;\n\n  writeGroup(result: TestResultContainer): void;\n\n  writeAttachment(distFileName: string, content: Buffer): void;\n\n  writeAttachmentFromPath(distFileName: string, from: string): void;\n\n  writeEnvironmentInfo(info: EnvironmentInfo): void;\n\n  writeCategoriesDefinitions(categories: Category[]): void;\n}\n\nexport type TestScope = {\n  uuid: string;\n  tests: string[];\n  fixtures: FixtureResultWrapper[];\n  labels: Label[];\n  links: Link[];\n  parameters: Parameter[];\n  description?: string;\n  descriptionHtml?: string;\n};\n\nexport type FixtureType = \"before\" | \"after\";\n\nexport type FixtureResultWrapper = {\n  uuid: string;\n  value: FixtureResult;\n  type: FixtureType;\n  scopeUuid: string;\n};\n\nexport type TestResultWrapper = {\n  value: TestResult;\n  scopeUuids: string[];\n};\n"], "mappings": "AAaA,OAAO,IAAMA,4BAA4B,GAAG,sCAAsC;AAClF,OAAO,IAAMC,mCAAmC,GAAG,qCAAqC", "ignoreList": []}