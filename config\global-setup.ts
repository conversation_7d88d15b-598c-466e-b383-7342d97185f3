import { FullConfig } from '@playwright/test';
import { logger } from '../utils/logger';
import * as fs from 'fs';

async function globalSetup(config: FullConfig): Promise<void> {
  logger.info('🚀 Starting global setup...');
  
  // Create necessary directories
  const directories = [
    'reports/allure-results',
    'reports/playwright-report',
    'logs',
    'test-results',
  ];

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      logger.info(`📁 Created directory: ${dir}`);
    }
  });

  // Clean up old reports and logs
  const cleanupDirs = [
    'reports/allure-results',
    'reports/playwright-report',
    'test-results',
  ];

  cleanupDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
      fs.mkdirSync(dir, { recursive: true });
      logger.info(`🧹 Cleaned directory: ${dir}`);
    }
  });

  // Log environment information
  logger.info(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`🔗 Base URL: ${config.projects[0]?.use?.baseURL || 'Not set'}`);
  logger.info(`👥 Workers: ${config.workers || 'Default'}`);
  logger.info(`🔄 Retries: ${config.projects[0]?.retries || 0}`);
  logger.info(`🎭 Headless: ${config.projects[0]?.use?.headless || false}`);

  logger.info('✅ Global setup completed successfully');
}

export default globalSetup;
