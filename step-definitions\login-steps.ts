import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { TestContext } from '../utils/TestContext';
import { TestDataManager } from '../utils/TestDataManager';
import { LoginPage } from '../pages/LoginPage';
import { DashboardPage } from '../pages/DashboardPage';
import { logger } from '../utils/logger';

const testContext = TestContext.getInstance();
const testDataManager = TestDataManager.getInstance();

// Login page navigation
Given('I am on the login page', async () => {
  logger.step('Navigating to login page');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  await loginPage.navigate();
  await loginPage.verifyLoginPageElements();
  logger.info('✅ Successfully navigated to login page');
});

// Login actions
When('I login with username {string} and password {string}', async (username: string, password: string) => {
  logger.step(`Logging in with username: ${username}`);
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  await loginPage.login(username, password);
  logger.info(`✅ Login attempted with username: ${username}`);
});

When('I login with valid credentials', async () => {
  logger.step('Logging in with valid credentials');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  const userData = testDataManager.getUserData('user');
  
  await loginPage.loginWithValidCredentials(userData.username, userData.password);
  logger.info('✅ Login successful with valid credentials');
});

When('I login with admin credentials', async () => {
  logger.step('Logging in with admin credentials');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  const adminData = testDataManager.getUserData('admin');
  
  await loginPage.loginWithValidCredentials(adminData.username, adminData.password);
  logger.info('✅ Login successful with admin credentials');
});

When('I login with invalid credentials', async () => {
  logger.step('Logging in with invalid credentials');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  await loginPage.login('<EMAIL>', 'wrongpassword');
  logger.info('✅ Login attempted with invalid credentials');
});

When('I enter username {string}', async (username: string) => {
  logger.step(`Entering username: ${username}`);
  
  const page = testContext.getPage();
  const usernameInput = page.locator('[data-testid="username-input"]');
  
  await usernameInput.waitFor({ state: 'visible' });
  await usernameInput.clear();
  await usernameInput.fill(username);
  logger.info(`✅ Username entered: ${username}`);
});

When('I enter password {string}', async (password: string) => {
  logger.step(`Entering password: ${password}`);
  
  const page = testContext.getPage();
  const passwordInput = page.locator('[data-testid="password-input"]');
  
  await passwordInput.waitFor({ state: 'visible' });
  await passwordInput.clear();
  await passwordInput.fill(password);
  logger.info('✅ Password entered');
});

When('I click the login button', async () => {
  logger.step('Clicking login button');
  
  const page = testContext.getPage();
  const loginButton = page.locator('[data-testid="login-button"]');
  
  await loginButton.waitFor({ state: 'visible' });
  await loginButton.click();
  logger.info('✅ Login button clicked');
});

When('I check the remember me checkbox', async () => {
  logger.step('Checking remember me checkbox');
  
  const page = testContext.getPage();
  const rememberMeCheckbox = page.locator('[data-testid="remember-me-checkbox"]');
  
  await rememberMeCheckbox.waitFor({ state: 'visible' });
  await rememberMeCheckbox.check();
  logger.info('✅ Remember me checkbox checked');
});

When('I click the forgot password link', async () => {
  logger.step('Clicking forgot password link');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  await loginPage.clickForgotPassword();
  logger.info('✅ Forgot password link clicked');
});

When('I click the sign up link', async () => {
  logger.step('Clicking sign up link');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  await loginPage.clickSignUp();
  logger.info('✅ Sign up link clicked');
});

// Login verifications
Then('I should be redirected to the dashboard', async () => {
  logger.step('Verifying redirect to dashboard');
  
  const page = testContext.getPage();
  const dashboardPage = new DashboardPage(page);
  
  await page.waitForURL('**/dashboard', { timeout: 10000 });
  await dashboardPage.verifyDashboardLoaded();
  logger.assertion('✅ Successfully redirected to dashboard');
});

Then('I should see a login error message', async () => {
  logger.step('Verifying login error message is displayed');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  const isErrorDisplayed = await loginPage.isErrorMessageDisplayed();
  expect(isErrorDisplayed).toBe(true);
  
  const errorMessage = await loginPage.getErrorMessage();
  logger.assertion(`✅ Login error message displayed: ${errorMessage}`);
});

Then('I should see the error message {string}', async (expectedMessage: string) => {
  logger.step(`Verifying specific error message: ${expectedMessage}`);
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  const actualMessage = await loginPage.getErrorMessage();
  expect(actualMessage).toContain(expectedMessage);
  logger.assertion(`✅ Error message verified: ${actualMessage}`);
});

Then('I should remain on the login page', async () => {
  logger.step('Verifying user remains on login page');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  await expect(page).toHaveURL(/.*\/login/);
  const isLoginFormDisplayed = await loginPage.isLoginFormDisplayed();
  expect(isLoginFormDisplayed).toBe(true);
  logger.assertion('✅ User remains on login page');
});

Then('the login form should be displayed', async () => {
  logger.step('Verifying login form is displayed');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  const isDisplayed = await loginPage.isLoginFormDisplayed();
  expect(isDisplayed).toBe(true);
  logger.assertion('✅ Login form is displayed');
});

Then('the login button should be enabled', async () => {
  logger.step('Verifying login button is enabled');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  const isEnabled = await loginPage.isLoginButtonEnabled();
  expect(isEnabled).toBe(true);
  logger.assertion('✅ Login button is enabled');
});

Then('the login button should be disabled', async () => {
  logger.step('Verifying login button is disabled');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  const isEnabled = await loginPage.isLoginButtonEnabled();
  expect(isEnabled).toBe(false);
  logger.assertion('✅ Login button is disabled');
});

Then('the remember me checkbox should be checked', async () => {
  logger.step('Verifying remember me checkbox is checked');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  const isChecked = await loginPage.isRememberMeChecked();
  expect(isChecked).toBe(true);
  logger.assertion('✅ Remember me checkbox is checked');
});

Then('I should see a welcome message for {string}', async (username: string) => {
  logger.step(`Verifying welcome message for: ${username}`);
  
  const page = testContext.getPage();
  const dashboardPage = new DashboardPage(page);
  
  const welcomeMessage = await dashboardPage.getWelcomeMessage();
  expect(welcomeMessage).toContain(username);
  logger.assertion(`✅ Welcome message verified for: ${username}`);
});
