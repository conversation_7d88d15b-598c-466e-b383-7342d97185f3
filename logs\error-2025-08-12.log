{"level":"error","message":"❌ <PERSON><PERSON><PERSON> failed: LGI-01: Login page loads","timestamp":"2025-08-12 15:52:29:5229"}
{"level":"error","message":"❌ <PERSON><PERSON><PERSON> failed: LGI-02: User can login with valid credentials","timestamp":"2025-08-12 15:57:43:5743"}
{"level":"error","message":"❌ <PERSON><PERSON><PERSON> failed: LGI-03: Password empty shows required message","timestamp":"2025-08-12 15:57:50:5750"}
{"level":"error","message":"❌ <PERSON><PERSON><PERSON> failed: LGI-04: Password shorter than 6 shows min-length message","timestamp":"2025-08-12 15:57:58:5758"}
{"level":"error","message":"❌ <PERSON><PERSON><PERSON> failed: LGI-05: Password with disallowed characters shows policy message","timestamp":"2025-08-12 15:58:08:588"}
{"level":"error","message":"❌ <PERSON><PERSON>rio failed: LGI-06: Password longer than 16 shows max-length message","timestamp":"2025-08-12 15:58:15:5815"}
{"level":"error","message":"Browser console error: Failed to load resource: the server responded with a status of 400 ()","timestamp":"2025-08-12 15:58:20:5820"}
{"level":"error","message":"❌ Scenario failed: LGI-07: Invalid email or password shows auth error","timestamp":"2025-08-12 15:58:25:5825"}
{"level":"error","message":"❌ Scenario failed: LGI-08: Show/Hide password toggle works","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"error","message":"Failed to take screenshot: Error: ENOENT: no such file or directory, open 'D:\\Automation\\SmoothContact\\logs\\failed-LGI-08:-Show\\Hide-password-toggle-works-2025-08-12T08-58-33-153Z.png'","timestamp":"2025-08-12 15:58:33:5833"}
{"level":"error","message":"❌ Scenario failed: LGI-09: Remember me persists session across reload","timestamp":"2025-08-12 15:58:40:5840"}
{"level":"error","message":"❌ Scenario failed: LGI-10: Logout invalidates session and returns to login","timestamp":"2025-08-12 15:58:48:5848"}
{"level":"error","message":"❌ Scenario failed: LGI-11: Visual regression baseline for login page","timestamp":"2025-08-12 15:58:53:5853"}
{"level":"error","message":"❌ Scenario failed: LGI-12: Accessibility smoke on login page","timestamp":"2025-08-12 15:58:58:5858"}
{"level":"error","message":"❌ Scenario failed: Password validation messages","timestamp":"2025-08-12 15:59:04:594"}
{"level":"error","message":"❌ Scenario failed: Password validation messages","timestamp":"2025-08-12 15:59:09:599"}
{"level":"error","message":"❌ Scenario failed: Password validation messages","timestamp":"2025-08-12 15:59:17:5917"}
{"level":"error","message":"❌ Scenario failed: Password validation messages","timestamp":"2025-08-12 15:59:27:5927"}
{"level":"error","message":"Browser console error: Failed to load resource: the server responded with a status of 400 ()","timestamp":"2025-08-12 15:59:30:5930"}
{"level":"error","message":"❌ Scenario failed: Password validation messages","timestamp":"2025-08-12 15:59:34:5934"}
{"level":"error","message":"❌ Scenario failed: LGI-01: Login page loads","timestamp":"2025-08-12 16:10:30:1030"}
{"level":"error","message":"❌ Scenario failed: LGI-03: Password empty shows required message","timestamp":"2025-08-12 16:12:43:1243"}
{"level":"error","message":"❌ Scenario failed: LGI-04: Password shorter than 6 shows min-length message","timestamp":"2025-08-12 16:13:19:1319"}
{"level":"error","message":"❌ Scenario failed: LGI-06: Password longer than 16 shows max-length message","timestamp":"2025-08-12 16:15:01:151"}
{"level":"error","message":"❌ Scenario failed: LGI-05: Password with disallowed characters shows policy message","timestamp":"2025-08-12 16:18:24:1824"}
{"level":"error","message":"Browser console error: Failed to load resource: the server responded with a status of 400 ()","timestamp":"2025-08-12 16:18:34:1834"}
{"level":"error","message":"❌ Scenario failed: LGI-08: Logout invalidates session and returns to login","timestamp":"2025-08-12 16:18:47:1847"}
{"level":"error","message":"❌ Scenario failed: LGI-09: Visual regression baseline for login page","timestamp":"2025-08-12 16:18:53:1853"}
{"level":"error","message":"❌ Scenario failed: LGI-10: Accessibility smoke on login page","timestamp":"2025-08-12 16:19:00:190"}
{"level":"error","message":"❌ Scenario failed: Password validation messages","timestamp":"2025-08-12 16:19:13:1913"}
{"level":"error","message":"Browser console error: Failed to load resource: the server responded with a status of 400 ()","timestamp":"2025-08-12 16:19:24:1924"}
