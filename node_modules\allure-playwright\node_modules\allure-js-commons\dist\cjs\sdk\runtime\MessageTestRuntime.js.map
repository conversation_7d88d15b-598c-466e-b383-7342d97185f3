{"version": 3, "file": "MessageTestRuntime.js", "names": ["_model", "require", "_utils", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "o", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "Symbol", "toPrimitive", "call", "TypeError", "String", "Number", "asyncGeneratorStep", "n", "a", "c", "u", "done", "Promise", "resolve", "then", "_asyncToGenerator", "_next", "_throw", "MessageTestRuntime", "label", "name", "_this", "sendMessage", "type", "data", "labels", "_arguments", "_this2", "_len", "Array", "_key", "link", "url", "_this3", "links", "_arguments2", "_this4", "_len2", "_key2", "parameter", "options", "_this5", "parameters", "description", "markdown", "_this6", "descriptionHtml", "html", "_this7", "displayName", "_this8", "historyId", "_this9", "testCaseId", "_this0", "attachment", "content", "_this1", "bufferContent", "<PERSON><PERSON><PERSON>", "from", "encoding", "toString", "contentType", "fileExtension", "wrapInStep", "timestamp", "Date", "now", "attachmentFromPath", "path", "_this10", "logStep", "_arguments3", "_this11", "status", "undefined", "Status", "PASSED", "error", "start", "stop", "statusDetails", "getMessageAndTraceFromError", "step", "body", "_this12", "result", "err", "details", "getStatusFromError", "stepDis<PERSON><PERSON><PERSON>", "_this13", "stepParameter", "mode", "_this14", "exports"], "sources": ["../../../../src/sdk/runtime/MessageTestRuntime.ts"], "sourcesContent": ["import {\n  type AttachmentOptions,\n  type Label,\n  type LabelName,\n  type Link,\n  type LinkType,\n  type ParameterMode,\n  type ParameterOptions,\n  Status,\n} from \"../../model.js\";\nimport type { RuntimeMessage } from \"../types.js\";\nimport { getMessageAndTraceFromError, getStatusFromError } from \"../utils.js\";\nimport type { TestRuntime } from \"./types.js\";\n\nexport abstract class MessageTestRuntime implements TestRuntime {\n  async label(name: LabelName | string, value: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        labels: [{ name, value }],\n      },\n    });\n  }\n\n  async labels(...labels: Label[]) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        labels,\n      },\n    });\n  }\n\n  async link(url: string, type?: LinkType | string, name?: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        links: [{ type, url, name }],\n      },\n    });\n  }\n\n  async links(...links: Link[]) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        links,\n      },\n    });\n  }\n\n  async parameter(name: string, value: string, options?: ParameterOptions) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        parameters: [\n          {\n            name,\n            value,\n            ...options,\n          },\n        ],\n      },\n    });\n  }\n\n  async description(markdown: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        description: markdown,\n      },\n    });\n  }\n\n  async descriptionHtml(html: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        descriptionHtml: html,\n      },\n    });\n  }\n\n  async displayName(name: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        displayName: name,\n      },\n    });\n  }\n\n  async historyId(value: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        historyId: value,\n      },\n    });\n  }\n\n  async testCaseId(value: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        testCaseId: value,\n      },\n    });\n  }\n\n  async attachment(name: string, content: Buffer | string, options: AttachmentOptions) {\n    const bufferContent = typeof content === \"string\" ? Buffer.from(content, options.encoding) : content;\n    await this.sendMessage({\n      type: \"attachment_content\",\n      data: {\n        name,\n        content: bufferContent.toString(\"base64\"),\n        encoding: \"base64\",\n        contentType: options.contentType,\n        fileExtension: options.fileExtension,\n        wrapInStep: true,\n        timestamp: Date.now(),\n      },\n    });\n  }\n\n  async attachmentFromPath(name: string, path: string, options: AttachmentOptions) {\n    await this.sendMessage({\n      type: \"attachment_path\",\n      data: {\n        name,\n        path,\n        contentType: options.contentType,\n        fileExtension: options.fileExtension,\n        wrapInStep: true,\n        timestamp: Date.now(),\n      },\n    });\n  }\n\n  async logStep(name: string, status: Status = Status.PASSED, error?: Error) {\n    const timestamp = Date.now();\n    await this.sendMessage({\n      type: \"step_start\",\n      data: {\n        name,\n        start: timestamp,\n      },\n    });\n    await this.sendMessage({\n      type: \"step_stop\",\n      data: {\n        status: status,\n        stop: timestamp,\n        statusDetails: error\n          ? {\n              ...getMessageAndTraceFromError(error),\n            }\n          : undefined,\n      },\n    });\n  }\n\n  async step<T = void>(name: string, body: () => T | PromiseLike<T>) {\n    await this.sendMessage({\n      type: \"step_start\",\n      data: {\n        name,\n        start: Date.now(),\n      },\n    });\n\n    try {\n      const result = await body();\n\n      await this.sendMessage({\n        type: \"step_stop\",\n        data: {\n          status: Status.PASSED,\n          stop: Date.now(),\n        },\n      });\n\n      return result;\n    } catch (err) {\n      const details = getMessageAndTraceFromError(err as Error);\n\n      await this.sendMessage({\n        type: \"step_stop\",\n        data: {\n          status: getStatusFromError(err as Error),\n          stop: Date.now(),\n          statusDetails: {\n            ...details,\n          },\n        },\n      });\n\n      throw err;\n    }\n  }\n\n  async stepDisplayName(name: string) {\n    await this.sendMessage({\n      type: \"step_metadata\",\n      data: { name },\n    });\n  }\n\n  async stepParameter(name: string, value: string, mode?: ParameterMode) {\n    await this.sendMessage({\n      type: \"step_metadata\",\n      data: {\n        parameters: [{ name, value, mode }],\n      },\n    });\n  }\n\n  abstract sendMessage(message: RuntimeMessage): Promise<void>;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAWA,IAAAC,MAAA,GAAAD,OAAA;AAA8E,SAAAE,QAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAJ,CAAA,OAAAG,MAAA,CAAAE,qBAAA,QAAAC,CAAA,GAAAH,MAAA,CAAAE,qBAAA,CAAAL,CAAA,GAAAC,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAC,MAAA,WAAAN,CAAA,WAAAE,MAAA,CAAAK,wBAAA,CAAAR,CAAA,EAAAC,CAAA,EAAAQ,UAAA,OAAAP,CAAA,CAAAQ,IAAA,CAAAC,KAAA,CAAAT,CAAA,EAAAI,CAAA,YAAAJ,CAAA;AAAA,SAAAU,cAAAZ,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAY,SAAA,CAAAC,MAAA,EAAAb,CAAA,UAAAC,CAAA,WAAAW,SAAA,CAAAZ,CAAA,IAAAY,SAAA,CAAAZ,CAAA,QAAAA,CAAA,OAAAF,OAAA,CAAAI,MAAA,CAAAD,CAAA,OAAAa,OAAA,WAAAd,CAAA,IAAAe,eAAA,CAAAhB,CAAA,EAAAC,CAAA,EAAAC,CAAA,CAAAD,CAAA,SAAAE,MAAA,CAAAc,yBAAA,GAAAd,MAAA,CAAAe,gBAAA,CAAAlB,CAAA,EAAAG,MAAA,CAAAc,yBAAA,CAAAf,CAAA,KAAAH,OAAA,CAAAI,MAAA,CAAAD,CAAA,GAAAa,OAAA,WAAAd,CAAA,IAAAE,MAAA,CAAAgB,cAAA,CAAAnB,CAAA,EAAAC,CAAA,EAAAE,MAAA,CAAAK,wBAAA,CAAAN,CAAA,EAAAD,CAAA,iBAAAD,CAAA;AAAA,SAAAgB,gBAAAhB,CAAA,EAAAC,CAAA,EAAAC,CAAA,YAAAD,CAAA,GAAAmB,cAAA,CAAAnB,CAAA,MAAAD,CAAA,GAAAG,MAAA,CAAAgB,cAAA,CAAAnB,CAAA,EAAAC,CAAA,IAAAoB,KAAA,EAAAnB,CAAA,EAAAO,UAAA,MAAAa,YAAA,MAAAC,QAAA,UAAAvB,CAAA,CAAAC,CAAA,IAAAC,CAAA,EAAAF,CAAA;AAAA,SAAAoB,eAAAlB,CAAA,QAAAsB,CAAA,GAAAC,YAAA,CAAAvB,CAAA,uCAAAsB,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAC,aAAAvB,CAAA,EAAAD,CAAA,2BAAAC,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAF,CAAA,GAAAE,CAAA,CAAAwB,MAAA,CAAAC,WAAA,kBAAA3B,CAAA,QAAAwB,CAAA,GAAAxB,CAAA,CAAA4B,IAAA,CAAA1B,CAAA,EAAAD,CAAA,uCAAAuB,CAAA,SAAAA,CAAA,YAAAK,SAAA,yEAAA5B,CAAA,GAAA6B,MAAA,GAAAC,MAAA,EAAA7B,CAAA;AAAA,SAAA8B,mBAAAC,CAAA,EAAA/B,CAAA,EAAAF,CAAA,EAAAC,CAAA,EAAAK,CAAA,EAAA4B,CAAA,EAAAC,CAAA,cAAAX,CAAA,GAAAS,CAAA,CAAAC,CAAA,EAAAC,CAAA,GAAAC,CAAA,GAAAZ,CAAA,CAAAH,KAAA,WAAAY,CAAA,gBAAAjC,CAAA,CAAAiC,CAAA,KAAAT,CAAA,CAAAa,IAAA,GAAAnC,CAAA,CAAAkC,CAAA,IAAAE,OAAA,CAAAC,OAAA,CAAAH,CAAA,EAAAI,IAAA,CAAAvC,CAAA,EAAAK,CAAA;AAAA,SAAAmC,kBAAAR,CAAA,6BAAA/B,CAAA,SAAAF,CAAA,GAAAa,SAAA,aAAAyB,OAAA,WAAArC,CAAA,EAAAK,CAAA,QAAA4B,CAAA,GAAAD,CAAA,CAAAtB,KAAA,CAAAT,CAAA,EAAAF,CAAA,YAAA0C,MAAAT,CAAA,IAAAD,kBAAA,CAAAE,CAAA,EAAAjC,CAAA,EAAAK,CAAA,EAAAoC,KAAA,EAAAC,MAAA,UAAAV,CAAA,cAAAU,OAAAV,CAAA,IAAAD,kBAAA,CAAAE,CAAA,EAAAjC,CAAA,EAAAK,CAAA,EAAAoC,KAAA,EAAAC,MAAA,WAAAV,CAAA,KAAAS,KAAA;AAGvE,MAAeE,kBAAkB,CAAwB;EACxDC,KAAKA,CAACC,IAAwB,EAAEzB,KAAa,EAAE;IAAA,IAAA0B,KAAA;IAAA,OAAAN,iBAAA;MACnD,MAAMM,KAAI,CAACC,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJC,MAAM,EAAE,CAAC;YAAEL,IAAI;YAAEzB;UAAM,CAAC;QAC1B;MACF,CAAC,CAAC;IAAC;EACL;EAEM8B,MAAMA,CAAA,EAAqB;IAAA,IAAAC,UAAA,GAAAvC,SAAA;MAAAwC,MAAA;IAAA,OAAAZ,iBAAA;MAAA,SAAAa,IAAA,GAAAF,UAAA,CAAAtC,MAAA,EAAjBqC,MAAM,OAAAI,KAAA,CAAAD,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;QAANL,MAAM,CAAAK,IAAA,IAAAJ,UAAA,CAAAI,IAAA;MAAA;MACpB,MAAMH,MAAI,CAACL,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJC;QACF;MACF,CAAC,CAAC;IAAC;EACL;EAEMM,IAAIA,CAACC,GAAW,EAAET,IAAwB,EAAEH,IAAa,EAAE;IAAA,IAAAa,MAAA;IAAA,OAAAlB,iBAAA;MAC/D,MAAMkB,MAAI,CAACX,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJU,KAAK,EAAE,CAAC;YAAEX,IAAI;YAAES,GAAG;YAAEZ;UAAK,CAAC;QAC7B;MACF,CAAC,CAAC;IAAC;EACL;EAEMc,KAAKA,CAAA,EAAmB;IAAA,IAAAC,WAAA,GAAAhD,SAAA;MAAAiD,MAAA;IAAA,OAAArB,iBAAA;MAAA,SAAAsB,KAAA,GAAAF,WAAA,CAAA/C,MAAA,EAAf8C,KAAK,OAAAL,KAAA,CAAAQ,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAALJ,KAAK,CAAAI,KAAA,IAAAH,WAAA,CAAAG,KAAA;MAAA;MAClB,MAAMF,MAAI,CAACd,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJU;QACF;MACF,CAAC,CAAC;IAAC;EACL;EAEMK,SAASA,CAACnB,IAAY,EAAEzB,KAAa,EAAE6C,OAA0B,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA1B,iBAAA;MACvE,MAAM0B,MAAI,CAACnB,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJkB,UAAU,EAAE,CAAAxD,aAAA;YAERkC,IAAI;YACJzB;UAAK,GACF6C,OAAO;QAGhB;MACF,CAAC,CAAC;IAAC;EACL;EAEMG,WAAWA,CAACC,QAAgB,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA9B,iBAAA;MAClC,MAAM8B,MAAI,CAACvB,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJmB,WAAW,EAAEC;QACf;MACF,CAAC,CAAC;IAAC;EACL;EAEME,eAAeA,CAACC,IAAY,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAjC,iBAAA;MAClC,MAAMiC,MAAI,CAAC1B,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJsB,eAAe,EAAEC;QACnB;MACF,CAAC,CAAC;IAAC;EACL;EAEME,WAAWA,CAAC7B,IAAY,EAAE;IAAA,IAAA8B,MAAA;IAAA,OAAAnC,iBAAA;MAC9B,MAAMmC,MAAI,CAAC5B,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJyB,WAAW,EAAE7B;QACf;MACF,CAAC,CAAC;IAAC;EACL;EAEM+B,SAASA,CAACxD,KAAa,EAAE;IAAA,IAAAyD,MAAA;IAAA,OAAArC,iBAAA;MAC7B,MAAMqC,MAAI,CAAC9B,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJ2B,SAAS,EAAExD;QACb;MACF,CAAC,CAAC;IAAC;EACL;EAEM0D,UAAUA,CAAC1D,KAAa,EAAE;IAAA,IAAA2D,MAAA;IAAA,OAAAvC,iBAAA;MAC9B,MAAMuC,MAAI,CAAChC,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJ6B,UAAU,EAAE1D;QACd;MACF,CAAC,CAAC;IAAC;EACL;EAEM4D,UAAUA,CAACnC,IAAY,EAAEoC,OAAwB,EAAEhB,OAA0B,EAAE;IAAA,IAAAiB,MAAA;IAAA,OAAA1C,iBAAA;MACnF,IAAM2C,aAAa,GAAG,OAAOF,OAAO,KAAK,QAAQ,GAAGG,MAAM,CAACC,IAAI,CAACJ,OAAO,EAAEhB,OAAO,CAACqB,QAAQ,CAAC,GAAGL,OAAO;MACpG,MAAMC,MAAI,CAACnC,WAAW,CAAC;QACrBC,IAAI,EAAE,oBAAoB;QAC1BC,IAAI,EAAE;UACJJ,IAAI;UACJoC,OAAO,EAAEE,aAAa,CAACI,QAAQ,CAAC,QAAQ,CAAC;UACzCD,QAAQ,EAAE,QAAQ;UAClBE,WAAW,EAAEvB,OAAO,CAACuB,WAAW;UAChCC,aAAa,EAAExB,OAAO,CAACwB,aAAa;UACpCC,UAAU,EAAE,IAAI;UAChBC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;QACtB;MACF,CAAC,CAAC;IAAC;EACL;EAEMC,kBAAkBA,CAACjD,IAAY,EAAEkD,IAAY,EAAE9B,OAA0B,EAAE;IAAA,IAAA+B,OAAA;IAAA,OAAAxD,iBAAA;MAC/E,MAAMwD,OAAI,CAACjD,WAAW,CAAC;QACrBC,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE;UACJJ,IAAI;UACJkD,IAAI;UACJP,WAAW,EAAEvB,OAAO,CAACuB,WAAW;UAChCC,aAAa,EAAExB,OAAO,CAACwB,aAAa;UACpCC,UAAU,EAAE,IAAI;UAChBC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;QACtB;MACF,CAAC,CAAC;IAAC;EACL;EAEMI,OAAOA,CAACpD,IAAY,EAAiD;IAAA,IAAAqD,WAAA,GAAAtF,SAAA;MAAAuF,OAAA;IAAA,OAAA3D,iBAAA;MAAA,IAA/C4D,MAAc,GAAAF,WAAA,CAAArF,MAAA,QAAAqF,WAAA,QAAAG,SAAA,GAAAH,WAAA,MAAGI,aAAM,CAACC,MAAM;MAAA,IAAEC,KAAa,GAAAN,WAAA,CAAArF,MAAA,OAAAqF,WAAA,MAAAG,SAAA;MACvE,IAAMV,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,MAAMM,OAAI,CAACpD,WAAW,CAAC;QACrBC,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE;UACJJ,IAAI;UACJ4D,KAAK,EAAEd;QACT;MACF,CAAC,CAAC;MACF,MAAMQ,OAAI,CAACpD,WAAW,CAAC;QACrBC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE;UACJmD,MAAM,EAAEA,MAAM;UACdM,IAAI,EAAEf,SAAS;UACfgB,aAAa,EAAEH,KAAK,GAAA7F,aAAA,KAEX,IAAAiG,kCAA2B,EAACJ,KAAK,CAAC,IAEvCH;QACN;MACF,CAAC,CAAC;IAAC;EACL;EAEMQ,IAAIA,CAAWhE,IAAY,EAAEiE,IAA8B,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAAvE,iBAAA;MACjE,MAAMuE,OAAI,CAAChE,WAAW,CAAC;QACrBC,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE;UACJJ,IAAI;UACJ4D,KAAK,EAAEb,IAAI,CAACC,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MAEF,IAAI;QACF,IAAMmB,MAAM,SAASF,IAAI,CAAC,CAAC;QAE3B,MAAMC,OAAI,CAAChE,WAAW,CAAC;UACrBC,IAAI,EAAE,WAAW;UACjBC,IAAI,EAAE;YACJmD,MAAM,EAAEE,aAAM,CAACC,MAAM;YACrBG,IAAI,EAAEd,IAAI,CAACC,GAAG,CAAC;UACjB;QACF,CAAC,CAAC;QAEF,OAAOmB,MAAM;MACf,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZ,IAAMC,OAAO,GAAG,IAAAN,kCAA2B,EAACK,GAAY,CAAC;QAEzD,MAAMF,OAAI,CAAChE,WAAW,CAAC;UACrBC,IAAI,EAAE,WAAW;UACjBC,IAAI,EAAE;YACJmD,MAAM,EAAE,IAAAe,yBAAkB,EAACF,GAAY,CAAC;YACxCP,IAAI,EAAEd,IAAI,CAACC,GAAG,CAAC,CAAC;YAChBc,aAAa,EAAAhG,aAAA,KACRuG,OAAO;UAEd;QACF,CAAC,CAAC;QAEF,MAAMD,GAAG;MACX;IAAC;EACH;EAEMG,eAAeA,CAACvE,IAAY,EAAE;IAAA,IAAAwE,OAAA;IAAA,OAAA7E,iBAAA;MAClC,MAAM6E,OAAI,CAACtE,WAAW,CAAC;QACrBC,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE;UAAEJ;QAAK;MACf,CAAC,CAAC;IAAC;EACL;EAEMyE,aAAaA,CAACzE,IAAY,EAAEzB,KAAa,EAAEmG,IAAoB,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAAhF,iBAAA;MACrE,MAAMgF,OAAI,CAACzE,WAAW,CAAC;QACrBC,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE;UACJkB,UAAU,EAAE,CAAC;YAAEtB,IAAI;YAAEzB,KAAK;YAAEmG;UAAK,CAAC;QACpC;MACF,CAAC,CAAC;IAAC;EACL;AAGF;AAACE,OAAA,CAAA9E,kBAAA,GAAAA,kBAAA", "ignoreList": []}