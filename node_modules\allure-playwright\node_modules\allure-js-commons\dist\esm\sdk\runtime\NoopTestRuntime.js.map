{"version": 3, "file": "NoopTestRuntime.js", "names": ["NoopTestRuntime", "attachment", "_this", "_asyncToGenerator", "warning", "attachmentFromPath", "_this2", "description", "_this3", "descriptionHtml", "_this4", "displayName", "_this5", "historyId", "_this6", "labels", "_this7", "links", "_this8", "parameter", "_this9", "logStep", "_this0", "step", "name", "body", "_this1", "stepDis<PERSON><PERSON><PERSON>", "_this10", "stepParameter", "_this11", "testCaseId", "_this12", "console", "log", "noopRuntime"], "sources": ["../../../../src/sdk/runtime/NoopTestRuntime.ts"], "sourcesContent": ["import type { TestRuntime } from \"./types.js\";\n\nexport class NoopTestRuntime implements TestRuntime {\n  async attachment() {\n    await this.warning();\n  }\n\n  async attachmentFromPath() {\n    await this.warning();\n  }\n\n  async description() {\n    await this.warning();\n  }\n\n  async descriptionHtml() {\n    await this.warning();\n  }\n\n  async displayName() {\n    await this.warning();\n  }\n\n  async historyId() {\n    await this.warning();\n  }\n\n  async labels() {\n    await this.warning();\n  }\n\n  async links() {\n    await this.warning();\n  }\n\n  async parameter() {\n    await this.warning();\n  }\n\n  async logStep() {\n    await this.warning();\n  }\n\n  async step<T>(name: string, body: () => T | PromiseLike<T>): Promise<T> {\n    await this.warning();\n    return body();\n  }\n\n  async stepDisplayName() {\n    await this.warning();\n  }\n\n  async stepParameter() {\n    await this.warning();\n  }\n\n  async testCaseId() {\n    await this.warning();\n  }\n\n  // eslint-disable-next-line @typescript-eslint/require-await\n  async warning() {\n    // eslint-disable-next-line no-console\n    console.log(\"no test runtime is found. Please check test framework configuration\");\n  }\n}\n\nexport const noopRuntime: TestRuntime = new NoopTestRuntime();\n"], "mappings": ";;AAEA,OAAO,MAAMA,eAAe,CAAwB;EAC5CC,UAAUA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACjB,MAAMD,KAAI,CAACE,OAAO,CAAC,CAAC;IAAC;EACvB;EAEMC,kBAAkBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAH,iBAAA;MACzB,MAAMG,MAAI,CAACF,OAAO,CAAC,CAAC;IAAC;EACvB;EAEMG,WAAWA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAL,iBAAA;MAClB,MAAMK,MAAI,CAACJ,OAAO,CAAC,CAAC;IAAC;EACvB;EAEMK,eAAeA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAP,iBAAA;MACtB,MAAMO,MAAI,CAACN,OAAO,CAAC,CAAC;IAAC;EACvB;EAEMO,WAAWA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAT,iBAAA;MAClB,MAAMS,MAAI,CAACR,OAAO,CAAC,CAAC;IAAC;EACvB;EAEMS,SAASA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAX,iBAAA;MAChB,MAAMW,MAAI,CAACV,OAAO,CAAC,CAAC;IAAC;EACvB;EAEMW,MAAMA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAb,iBAAA;MACb,MAAMa,MAAI,CAACZ,OAAO,CAAC,CAAC;IAAC;EACvB;EAEMa,KAAKA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAf,iBAAA;MACZ,MAAMe,MAAI,CAACd,OAAO,CAAC,CAAC;IAAC;EACvB;EAEMe,SAASA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAjB,iBAAA;MAChB,MAAMiB,MAAI,CAAChB,OAAO,CAAC,CAAC;IAAC;EACvB;EAEMiB,OAAOA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAnB,iBAAA;MACd,MAAMmB,MAAI,CAAClB,OAAO,CAAC,CAAC;IAAC;EACvB;EAEMmB,IAAIA,CAAIC,IAAY,EAAEC,IAA8B,EAAc;IAAA,IAAAC,MAAA;IAAA,OAAAvB,iBAAA;MACtE,MAAMuB,MAAI,CAACtB,OAAO,CAAC,CAAC;MACpB,OAAOqB,IAAI,CAAC,CAAC;IAAC;EAChB;EAEME,eAAeA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAAzB,iBAAA;MACtB,MAAMyB,OAAI,CAACxB,OAAO,CAAC,CAAC;IAAC;EACvB;EAEMyB,aAAaA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAA3B,iBAAA;MACpB,MAAM2B,OAAI,CAAC1B,OAAO,CAAC,CAAC;IAAC;EACvB;EAEM2B,UAAUA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAA7B,iBAAA;MACjB,MAAM6B,OAAI,CAAC5B,OAAO,CAAC,CAAC;IAAC;EACvB;;EAEA;EACMA,OAAOA,CAAA,EAAG;IAAA,OAAAD,iBAAA;MACd;MACA8B,OAAO,CAACC,GAAG,CAAC,qEAAqE,CAAC;IAAC;EACrF;AACF;AAEA,OAAO,IAAMC,WAAwB,GAAG,IAAInC,eAAe,CAAC,CAAC", "ignoreList": []}