{"version": 3, "file": "MessageWriter.js", "names": ["readFileSync", "process", "stringifyEnvInfo", "MessageWriter", "constructor", "bus", "sendData", "path", "type", "data", "_process$send", "event", "toString", "emit", "JSON", "stringify", "send", "call", "writeJson", "<PERSON><PERSON><PERSON>", "from", "writeAttachment", "distFileName", "content", "writeAttachmentFromPath", "writeEnvironmentInfo", "info", "text", "writeCategoriesDefinitions", "categories", "writeGroup", "result", "concat", "uuid", "writeResult"], "sources": ["../../../../../src/sdk/reporter/writer/MessageWriter.ts"], "sourcesContent": ["import { readFileSync } from \"fs\";\nimport type { EventEmitter } from \"node:events\";\nimport process from \"process\";\nimport type { TestResult, TestResultContainer } from \"../../../model.js\";\nimport type { Category, EnvironmentInfo } from \"../../types.js\";\nimport type { Writer } from \"../types.js\";\nimport { stringifyEnvInfo } from \"../utils/envInfo.js\";\n\ntype EventType = \"result\" | \"container\" | \"attachment\" | \"misc\";\n\nexport class MessageWriter implements Writer {\n  constructor(private bus?: EventEmitter) {}\n\n  private sendData(path: string, type: EventType, data: Buffer) {\n    const event = { path, type, data: data.toString(\"base64\") };\n\n    if (this.bus) {\n      this.bus.emit(\"allureWriterMessage\", JSON.stringify(event));\n      return;\n    }\n\n    process.send?.(JSON.stringify(event));\n  }\n\n  private writeJson(path: string, type: EventType, data: any) {\n    this.sendData(path, type, Buffer.from(JSON.stringify(data), \"utf-8\"));\n  }\n\n  writeAttachment(distFileName: string, content: Buffer): void {\n    this.sendData(distFileName, \"attachment\", content);\n  }\n\n  writeAttachmentFromPath(distFileName: string, from: string): void {\n    this.sendData(distFileName, \"attachment\", readFileSync(from));\n  }\n\n  writeEnvironmentInfo(info: EnvironmentInfo): void {\n    const text = stringifyEnvInfo(info);\n\n    this.sendData(\"environment.properties\", \"misc\", Buffer.from(text, \"utf-8\"));\n  }\n\n  writeCategoriesDefinitions(categories: Category[]): void {\n    this.writeJson(\"categories.json\", \"misc\", categories);\n  }\n\n  writeGroup(result: TestResultContainer): void {\n    this.writeJson(`${result.uuid}-container.json`, \"container\", result);\n  }\n\n  writeResult(result: TestResult): void {\n    this.writeJson(`${result.uuid}-result.json`, \"result\", result);\n  }\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,IAAI;AAEjC,OAAOC,OAAO,MAAM,SAAS;AAI7B,SAASC,gBAAgB,QAAQ,qBAAqB;AAItD,OAAO,MAAMC,aAAa,CAAmB;EAC3CC,WAAWA,CAASC,GAAkB,EAAE;IAAA,KAApBA,GAAkB,GAAlBA,GAAkB;EAAG;EAEjCC,QAAQA,CAACC,IAAY,EAAEC,IAAe,EAAEC,IAAY,EAAE;IAAA,IAAAC,aAAA;IAC5D,IAAMC,KAAK,GAAG;MAAEJ,IAAI;MAAEC,IAAI;MAAEC,IAAI,EAAEA,IAAI,CAACG,QAAQ,CAAC,QAAQ;IAAE,CAAC;IAE3D,IAAI,IAAI,CAACP,GAAG,EAAE;MACZ,IAAI,CAACA,GAAG,CAACQ,IAAI,CAAC,qBAAqB,EAAEC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAAC,CAAC;MAC3D;IACF;IAEA,CAAAD,aAAA,GAAAT,OAAO,CAACe,IAAI,cAAAN,aAAA,eAAZA,aAAA,CAAAO,IAAA,CAAAhB,OAAO,EAAQa,IAAI,CAACC,SAAS,CAACJ,KAAK,CAAC,CAAC;EACvC;EAEQO,SAASA,CAACX,IAAY,EAAEC,IAAe,EAAEC,IAAS,EAAE;IAC1D,IAAI,CAACH,QAAQ,CAACC,IAAI,EAAEC,IAAI,EAAEW,MAAM,CAACC,IAAI,CAACN,IAAI,CAACC,SAAS,CAACN,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;EACvE;EAEAY,eAAeA,CAACC,YAAoB,EAAEC,OAAe,EAAQ;IAC3D,IAAI,CAACjB,QAAQ,CAACgB,YAAY,EAAE,YAAY,EAAEC,OAAO,CAAC;EACpD;EAEAC,uBAAuBA,CAACF,YAAoB,EAAEF,IAAY,EAAQ;IAChE,IAAI,CAACd,QAAQ,CAACgB,YAAY,EAAE,YAAY,EAAEtB,YAAY,CAACoB,IAAI,CAAC,CAAC;EAC/D;EAEAK,oBAAoBA,CAACC,IAAqB,EAAQ;IAChD,IAAMC,IAAI,GAAGzB,gBAAgB,CAACwB,IAAI,CAAC;IAEnC,IAAI,CAACpB,QAAQ,CAAC,wBAAwB,EAAE,MAAM,EAAEa,MAAM,CAACC,IAAI,CAACO,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7E;EAEAC,0BAA0BA,CAACC,UAAsB,EAAQ;IACvD,IAAI,CAACX,SAAS,CAAC,iBAAiB,EAAE,MAAM,EAAEW,UAAU,CAAC;EACvD;EAEAC,UAAUA,CAACC,MAA2B,EAAQ;IAC5C,IAAI,CAACb,SAAS,IAAAc,MAAA,CAAID,MAAM,CAACE,IAAI,sBAAmB,WAAW,EAAEF,MAAM,CAAC;EACtE;EAEAG,WAAWA,CAACH,MAAkB,EAAQ;IACpC,IAAI,CAACb,SAAS,IAAAc,MAAA,CAAID,MAAM,CAACE,IAAI,mBAAgB,QAAQ,EAAEF,MAAM,CAAC;EAChE;AACF", "ignoreList": []}