{"version": 3, "file": "utils.js", "names": ["LabelName", "Status", "getStatusFromError", "error", "test", "constructor", "name", "message", "stack", "inspect", "FAILED", "BROKEN", "ansiRegex", "<PERSON><PERSON><PERSON><PERSON>", "arguments", "length", "undefined", "pattern", "join", "RegExp", "stripAnsi", "str", "regex", "replace", "actualAndExpected", "value", "matcherR<PERSON>ult", "actual", "serialize", "expected", "_objectSpread", "getMessageAndTraceFromError", "trace", "allureMetadataRegexp", "_wrapRegExp", "type", "allureTitleMetadataRegexp", "allureTitleMetadataRegexpGlobal", "allureIdRegexp", "id", "allureLabelRegexp", "getTypeFromAllureTitleMetadataMatch", "match", "getValueFromAllureTitleMetadataMatch", "_match$", "quotesRegexp", "quoteOpenRegexp", "concat", "source", "quoteCloseRegexp", "matchedValue", "slice", "isMetadataTag", "tag", "getMetadataLabel", "_match$groups", "groups", "subtype", "split", "ALLURE_ID", "extractMetadataFromString", "title", "labels", "links", "metadata", "matchAll", "cleanTitle", "replaceAll", "filter", "Boolean", "reduce", "acc", "word", "trim", "m", "push", "url", "isAnyStepFailed", "item", "isFailed", "status", "steps", "find", "step", "isAllStepsEnded", "every", "val", "stop", "<PERSON><PERSON><PERSON><PERSON>", "testResult", "labelName", "l", "hasStepMessage", "messages", "some", "getStepsMessagesPair", "unfinishedStepIdx", "findLastIndex", "getUnfinishedStepsMessages", "grouppedStepsMessage", "isPromise", "obj", "then", "max<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "replacer", "limitString", "JSON", "stringify", "createSerializeReplacer", "String", "userDefinedReplacer", "parents", "limitingReplacer", "_", "Object", "is", "at", "pop", "includes", "Map", "excludeCircularRefsFromMap", "Set", "excludeCircularRefsFromSet", "composeReplacers", "first", "second", "k", "v", "call", "map", "Array", "from", "_ref", "_ref2", "set", "substring"], "sources": ["../../../src/sdk/utils.ts"], "sourcesContent": ["import type { FixtureResult, Label, Link, StatusDetails, StepResult, TestResult } from \"../model.js\";\nimport { LabelName, Status } from \"../model.js\";\nimport type { RuntimeMessage, SerializeOptions, SerializerReplacerFunc } from \"./types.js\";\n\nexport const getStatusFromError = (error: Partial<Error>): Status => {\n  switch (true) {\n    /**\n     * Native `node:assert` and `chai` (`vitest` uses it under the hood) throw `AssertionError`\n     * `jest` throws `JestAssertionError` instance\n     * `jasmine` throws `ExpectationFailed` instance\n     * `vitest` throws `Error` for extended assertions, so we look into stack\n     */\n    case /assert/gi.test(error.constructor.name):\n    case /expectation/gi.test(error.constructor.name):\n    case error.name && /assert/gi.test(error.name):\n    case error.message && /assert/gi.test(error.message):\n    case error.stack && /@vitest\\/expect/gi.test(error.stack):\n    case error.stack && /playwright\\/lib\\/matchers\\/expect\\.js/gi.test(error.stack):\n    case \"matcherResult\" in error:\n    case \"inspect\" in error && typeof error.inspect === \"function\":\n      return Status.FAILED;\n    default:\n      return Status.BROKEN;\n  }\n};\n\n/**\n * Source: https://github.com/chalk/ansi-regex\n */\nconst ansiRegex = ({ onlyFirst = false } = {}) => {\n  const pattern = [\n    \"[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)\",\n    \"(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-nq-uy=><~]))\",\n  ].join(\"|\");\n\n  return new RegExp(pattern, onlyFirst ? undefined : \"g\");\n};\n\n/**\n * https://github.com/chalk/strip-ansi\n */\nexport const stripAnsi = (str: string): string => {\n  const regex = ansiRegex();\n  return str.replace(regex, \"\");\n};\n\nconst actualAndExpected = (value: unknown): { actual?: string; expected?: string } => {\n  if (!value || typeof value !== \"object\") {\n    return {};\n  }\n\n  // support for jest asserts\n  if (\"matcherResult\" in value && value.matcherResult !== undefined && typeof value.matcherResult === \"object\") {\n    return {\n      actual: serialize((value.matcherResult as any).actual),\n      expected: serialize((value.matcherResult as any).expected),\n    };\n  }\n\n  const actual = \"actual\" in value && value.actual !== undefined ? { actual: serialize(value.actual) } : {};\n  const expected = \"expected\" in value && value.expected !== undefined ? { expected: serialize(value.expected) } : {};\n  return {\n    ...actual,\n    ...expected,\n  };\n};\n\nexport const getMessageAndTraceFromError = (\n  error:\n    | Error\n    | {\n        message?: string;\n        stack?: string;\n      },\n): StatusDetails => {\n  const { message, stack } = error;\n  return {\n    message: message ? stripAnsi(message) : undefined,\n    trace: stack ? stripAnsi(stack) : undefined,\n    ...actualAndExpected(error),\n  };\n};\n\ntype AllureTitleMetadataMatch = RegExpMatchArray & {\n  groups: {\n    type?: string;\n    v1?: string;\n    v2?: string;\n    v3?: string;\n    v4?: string;\n  };\n};\n\nexport const allureMetadataRegexp = /(?:^|\\s)@?allure\\.(?<type>\\S+)$/;\nexport const allureTitleMetadataRegexp = /(?:^|\\s)@?allure\\.(?<type>[^:=\\s]+)[:=](\"[^\"]+\"|'[^']+'|`[^`]+`|\\S+)/;\nexport const allureTitleMetadataRegexpGlobal = new RegExp(allureTitleMetadataRegexp, \"g\");\nexport const allureIdRegexp = /(?:^|\\s)@?allure\\.id[:=](?<id>\\S+)/;\nexport const allureLabelRegexp = /(?:^|\\s)@?allure\\.label\\.(?<name>[^:=\\s]+)[:=](?<value>[^\\s]+)/;\n\nexport const getTypeFromAllureTitleMetadataMatch = (match: AllureTitleMetadataMatch) => {\n  return match?.[1];\n};\n\nexport const getValueFromAllureTitleMetadataMatch = (match: AllureTitleMetadataMatch) => {\n  const quotesRegexp = /['\"`]/;\n  const quoteOpenRegexp = new RegExp(`^${quotesRegexp.source}`);\n  const quoteCloseRegexp = new RegExp(`${quotesRegexp.source}$`);\n  const matchedValue = match?.[2] ?? \"\";\n\n  if (quoteOpenRegexp.test(matchedValue) && quoteCloseRegexp.test(matchedValue)) {\n    return matchedValue.slice(1, -1);\n  }\n\n  return matchedValue;\n};\n\nexport const isMetadataTag = (tag: string) => {\n  return allureMetadataRegexp.test(tag);\n};\n\nexport const getMetadataLabel = (tag: string, value?: string): Label | undefined => {\n  const match = tag.match(allureMetadataRegexp);\n  const type = match?.groups?.type;\n\n  if (!type) {\n    return undefined;\n  }\n\n  const [subtype, name] = type.split(\".\");\n\n  return {\n    name: subtype === \"id\" ? LabelName.ALLURE_ID : name,\n    value: value ?? \"\",\n  };\n};\n\nexport const extractMetadataFromString = (\n  title: string,\n): {\n  labels: Label[];\n  links: Link[];\n  cleanTitle: string;\n} => {\n  const labels = [] as Label[];\n  const links = [] as Link[];\n  const metadata = title.matchAll(allureTitleMetadataRegexpGlobal);\n  const cleanTitle = title\n    .replaceAll(allureTitleMetadataRegexpGlobal, \"\")\n    .split(\" \")\n    .filter(Boolean)\n    .reduce((acc, word) => {\n      if (/^[\\n\\r]/.test(word)) {\n        return acc + word;\n      }\n\n      return `${acc} ${word}`;\n    }, \"\")\n    .trim();\n\n  for (const m of metadata) {\n    const match = m as AllureTitleMetadataMatch;\n    const type = getTypeFromAllureTitleMetadataMatch(match);\n    const value = getValueFromAllureTitleMetadataMatch(match);\n\n    if (!type || !value) {\n      continue;\n    }\n\n    const [subtype, name] = type.split(\".\");\n\n    switch (subtype) {\n      case \"id\":\n        labels.push({ name: LabelName.ALLURE_ID, value });\n        break;\n      case \"label\":\n        labels.push({ name, value });\n        break;\n      case \"link\":\n        links.push({ type: name, url: value });\n        break;\n    }\n  }\n\n  return {\n    labels,\n    links,\n    cleanTitle,\n  };\n};\n\nexport const isAnyStepFailed = (item: StepResult | TestResult | FixtureResult): boolean => {\n  const isFailed = item.status === Status.FAILED;\n\n  if (isFailed || item.steps.length === 0) {\n    return isFailed;\n  }\n\n  return !!item.steps.find((step) => isAnyStepFailed(step));\n};\n\nexport const isAllStepsEnded = (item: StepResult | TestResult | FixtureResult): boolean => {\n  return item.steps.every((val) => val.stop && isAllStepsEnded(val));\n};\n\nexport const hasLabel = (testResult: TestResult, labelName: LabelName | string): boolean => {\n  return !!testResult.labels.find((l) => l.name === labelName);\n};\n\nexport const hasStepMessage = (messages: RuntimeMessage[]) => {\n  return messages.some((message) => message.type === \"step_start\" || message.type === \"step_stop\");\n};\n\nexport const getStepsMessagesPair = (messages: RuntimeMessage[]) =>\n  messages.reduce((acc, message) => {\n    if (message.type !== \"step_start\" && message.type !== \"step_stop\") {\n      return acc;\n    }\n\n    if (message.type === \"step_start\") {\n      acc.push([message]);\n\n      return acc;\n    }\n\n    const unfinishedStepIdx = acc.findLastIndex((step) => step.length === 1);\n\n    if (unfinishedStepIdx === -1) {\n      return acc;\n    }\n\n    acc[unfinishedStepIdx].push(message);\n\n    return acc;\n  }, [] as RuntimeMessage[][]);\n\nexport const getUnfinishedStepsMessages = (messages: RuntimeMessage[]) => {\n  const grouppedStepsMessage = getStepsMessagesPair(messages);\n\n  return grouppedStepsMessage.filter((step) => step.length === 1);\n};\n\nexport const isPromise = <T = any>(obj: any): obj is PromiseLike<T> =>\n  !!obj && (typeof obj === \"object\" || typeof obj === \"function\") && typeof obj.then === \"function\";\n\nexport const serialize = (value: any, { maxDepth = 0, maxLength = 0, replacer }: SerializeOptions = {}): string =>\n  limitString(\n    typeof value === \"object\" ? JSON.stringify(value, createSerializeReplacer(maxDepth, replacer)) : String(value),\n    maxLength,\n  );\n\nconst createSerializeReplacer = (maxDepth: number, userDefinedReplacer: SerializeOptions[\"replacer\"]) => {\n  const parents: unknown[] = [];\n  const limitingReplacer = function (this: unknown, _: string, value: unknown) {\n    if (typeof value !== \"object\" || value === null) {\n      return value;\n    }\n\n    while (parents.length > 0 && !Object.is(parents.at(-1), this)) {\n      parents.pop();\n    }\n\n    if ((maxDepth && parents.length >= maxDepth) || parents.includes(value)) {\n      return undefined;\n    }\n\n    parents.push(value);\n\n    return value instanceof Map\n      ? excludeCircularRefsFromMap(parents, value)\n      : value instanceof Set\n        ? excludeCircularRefsFromSet(parents, value)\n        : value;\n  };\n  return userDefinedReplacer ? composeReplacers(userDefinedReplacer, limitingReplacer) : limitingReplacer;\n};\n\nconst composeReplacers = (first: SerializerReplacerFunc, second: SerializerReplacerFunc): SerializerReplacerFunc =>\n  function (k, v) {\n    return second.call(this, k, first.call(this, k, v));\n  };\n\nconst excludeCircularRefsFromMap = (parents: any[], map: Map<any, any>) => {\n  return Array.from(map)\n    .filter(([k]) => !parents.includes(k))\n    .map(([k, v]) => [k, parents.includes(v) ? undefined : v]);\n};\n\nconst excludeCircularRefsFromSet = (parents: any[], set: Set<any>) => {\n  return Array.from(set).map((v) => (parents.includes(v) ? undefined : v));\n};\n\nconst limitString = (value: string, maxLength: number) =>\n  maxLength && value.length > maxLength ? `${value.substring(0, maxLength)}...` : value;\n"], "mappings": ";;;;;;;;AACA,SAASA,SAAS,EAAEC,MAAM,QAAQ,aAAa;AAG/C,OAAO,IAAMC,kBAAkB,GAAIC,KAAqB,IAAa;EACnE,QAAQ,IAAI;IACV;AACJ;AACA;AACA;AACA;AACA;IACI,KAAK,UAAU,CAACC,IAAI,CAACD,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC;IAC5C,KAAK,eAAe,CAACF,IAAI,CAACD,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC;IACjD,KAAKH,KAAK,CAACG,IAAI,IAAI,UAAU,CAACF,IAAI,CAACD,KAAK,CAACG,IAAI,CAAC;IAC9C,KAAKH,KAAK,CAACI,OAAO,IAAI,UAAU,CAACH,IAAI,CAACD,KAAK,CAACI,OAAO,CAAC;IACpD,KAAKJ,KAAK,CAACK,KAAK,IAAI,mBAAmB,CAACJ,IAAI,CAACD,KAAK,CAACK,KAAK,CAAC;IACzD,KAAKL,KAAK,CAACK,KAAK,IAAI,yCAAyC,CAACJ,IAAI,CAACD,KAAK,CAACK,KAAK,CAAC;IAC/E,KAAK,eAAe,IAAIL,KAAK;IAC7B,KAAK,SAAS,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAACM,OAAO,KAAK,UAAU;MAC5D,OAAOR,MAAM,CAACS,MAAM;IACtB;MACE,OAAOT,MAAM,CAACU,MAAM;EACxB;AACF,CAAC;;AAED;AACA;AACA;AACA,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAmC;EAAA,IAA/B;IAAEC,SAAS,GAAG;EAAM,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC3C,IAAMG,OAAO,GAAG,CACd,8HAA8H,EAC9H,0DAA0D,CAC3D,CAACC,IAAI,CAAC,GAAG,CAAC;EAEX,OAAO,IAAIC,MAAM,CAACF,OAAO,EAAEJ,SAAS,GAAGG,SAAS,GAAG,GAAG,CAAC;AACzD,CAAC;;AAED;AACA;AACA;AACA,OAAO,IAAMI,SAAS,GAAIC,GAAW,IAAa;EAChD,IAAMC,KAAK,GAAGV,SAAS,CAAC,CAAC;EACzB,OAAOS,GAAG,CAACE,OAAO,CAACD,KAAK,EAAE,EAAE,CAAC;AAC/B,CAAC;AAED,IAAME,iBAAiB,GAAIC,KAAc,IAA6C;EACpF,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACvC,OAAO,CAAC,CAAC;EACX;;EAEA;EACA,IAAI,eAAe,IAAIA,KAAK,IAAIA,KAAK,CAACC,aAAa,KAAKV,SAAS,IAAI,OAAOS,KAAK,CAACC,aAAa,KAAK,QAAQ,EAAE;IAC5G,OAAO;MACLC,MAAM,EAAEC,SAAS,CAAEH,KAAK,CAACC,aAAa,CAASC,MAAM,CAAC;MACtDE,QAAQ,EAAED,SAAS,CAAEH,KAAK,CAACC,aAAa,CAASG,QAAQ;IAC3D,CAAC;EACH;EAEA,IAAMF,MAAM,GAAG,QAAQ,IAAIF,KAAK,IAAIA,KAAK,CAACE,MAAM,KAAKX,SAAS,GAAG;IAAEW,MAAM,EAAEC,SAAS,CAACH,KAAK,CAACE,MAAM;EAAE,CAAC,GAAG,CAAC,CAAC;EACzG,IAAME,QAAQ,GAAG,UAAU,IAAIJ,KAAK,IAAIA,KAAK,CAACI,QAAQ,KAAKb,SAAS,GAAG;IAAEa,QAAQ,EAAED,SAAS,CAACH,KAAK,CAACI,QAAQ;EAAE,CAAC,GAAG,CAAC,CAAC;EACnH,OAAAC,aAAA,CAAAA,aAAA,KACKH,MAAM,GACNE,QAAQ;AAEf,CAAC;AAED,OAAO,IAAME,2BAA2B,GACtC5B,KAKK,IACa;EAClB,IAAM;IAAEI,OAAO;IAAEC;EAAM,CAAC,GAAGL,KAAK;EAChC,OAAA2B,aAAA;IACEvB,OAAO,EAAEA,OAAO,GAAGa,SAAS,CAACb,OAAO,CAAC,GAAGS,SAAS;IACjDgB,KAAK,EAAExB,KAAK,GAAGY,SAAS,CAACZ,KAAK,CAAC,GAAGQ;EAAS,GACxCQ,iBAAiB,CAACrB,KAAK,CAAC;AAE/B,CAAC;AAYD,OAAO,IAAM8B,oBAAoB,gBAAAC,WAAA,CAAG,0BAAiC;EAAAC,IAAA;AAAA;AACrE,OAAO,IAAMC,yBAAyB,gBAAAF,WAAA,CAAG,+DAAsE;EAAAC,IAAA;AAAA;AAC/G,OAAO,IAAME,+BAA+B,GAAG,IAAIlB,MAAM,CAACiB,yBAAyB,EAAE,GAAG,CAAC;AACzF,OAAO,IAAME,cAAc,gBAAAJ,WAAA,CAAG,+BAAoC;EAAAK,EAAA;AAAA;AAClE,OAAO,IAAMC,iBAAiB,gBAAAN,WAAA,CAAG,iDAAgE;EAAA5B,IAAA;EAAAmB,KAAA;AAAA;AAEjG,OAAO,IAAMgB,mCAAmC,GAAIC,KAA+B,IAAK;EACtF,OAAOA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAC,CAAC;AACnB,CAAC;AAED,OAAO,IAAMC,oCAAoC,GAAID,KAA+B,IAAK;EAAA,IAAAE,OAAA;EACvF,IAAMC,YAAY,GAAG,OAAO;EAC5B,IAAMC,eAAe,GAAG,IAAI3B,MAAM,KAAA4B,MAAA,CAAKF,YAAY,CAACG,MAAM,CAAE,CAAC;EAC7D,IAAMC,gBAAgB,GAAG,IAAI9B,MAAM,IAAA4B,MAAA,CAAIF,YAAY,CAACG,MAAM,MAAG,CAAC;EAC9D,IAAME,YAAY,IAAAN,OAAA,GAAGF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAC,CAAC,cAAAE,OAAA,cAAAA,OAAA,GAAI,EAAE;EAErC,IAAIE,eAAe,CAAC1C,IAAI,CAAC8C,YAAY,CAAC,IAAID,gBAAgB,CAAC7C,IAAI,CAAC8C,YAAY,CAAC,EAAE;IAC7E,OAAOA,YAAY,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAClC;EAEA,OAAOD,YAAY;AACrB,CAAC;AAED,OAAO,IAAME,aAAa,GAAIC,GAAW,IAAK;EAC5C,OAAOpB,oBAAoB,CAAC7B,IAAI,CAACiD,GAAG,CAAC;AACvC,CAAC;AAED,OAAO,IAAMC,gBAAgB,GAAGA,CAACD,GAAW,EAAE5B,KAAc,KAAwB;EAAA,IAAA8B,aAAA;EAClF,IAAMb,KAAK,GAAGW,GAAG,CAACX,KAAK,CAACT,oBAAoB,CAAC;EAC7C,IAAME,IAAI,GAAGO,KAAK,aAALA,KAAK,gBAAAa,aAAA,GAALb,KAAK,CAAEc,MAAM,cAAAD,aAAA,uBAAbA,aAAA,CAAepB,IAAI;EAEhC,IAAI,CAACA,IAAI,EAAE;IACT,OAAOnB,SAAS;EAClB;EAEA,IAAM,CAACyC,OAAO,EAAEnD,IAAI,CAAC,GAAG6B,IAAI,CAACuB,KAAK,CAAC,GAAG,CAAC;EAEvC,OAAO;IACLpD,IAAI,EAAEmD,OAAO,KAAK,IAAI,GAAGzD,SAAS,CAAC2D,SAAS,GAAGrD,IAAI;IACnDmB,KAAK,EAAEA,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI;EAClB,CAAC;AACH,CAAC;AAED,OAAO,IAAMmC,yBAAyB,GACpCC,KAAa,IAKV;EACH,IAAMC,MAAM,GAAG,EAAa;EAC5B,IAAMC,KAAK,GAAG,EAAY;EAC1B,IAAMC,QAAQ,GAAGH,KAAK,CAACI,QAAQ,CAAC5B,+BAA+B,CAAC;EAChE,IAAM6B,UAAU,GAAGL,KAAK,CACrBM,UAAU,CAAC9B,+BAA+B,EAAE,EAAE,CAAC,CAC/CqB,KAAK,CAAC,GAAG,CAAC,CACVU,MAAM,CAACC,OAAO,CAAC,CACfC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;IACrB,IAAI,SAAS,CAACpE,IAAI,CAACoE,IAAI,CAAC,EAAE;MACxB,OAAOD,GAAG,GAAGC,IAAI;IACnB;IAEA,UAAAzB,MAAA,CAAUwB,GAAG,OAAAxB,MAAA,CAAIyB,IAAI;EACvB,CAAC,EAAE,EAAE,CAAC,CACLC,IAAI,CAAC,CAAC;EAET,KAAK,IAAMC,CAAC,IAAIV,QAAQ,EAAE;IACxB,IAAMtB,KAAK,GAAGgC,CAA6B;IAC3C,IAAMvC,IAAI,GAAGM,mCAAmC,CAACC,KAAK,CAAC;IACvD,IAAMjB,KAAK,GAAGkB,oCAAoC,CAACD,KAAK,CAAC;IAEzD,IAAI,CAACP,IAAI,IAAI,CAACV,KAAK,EAAE;MACnB;IACF;IAEA,IAAM,CAACgC,OAAO,EAAEnD,IAAI,CAAC,GAAG6B,IAAI,CAACuB,KAAK,CAAC,GAAG,CAAC;IAEvC,QAAQD,OAAO;MACb,KAAK,IAAI;QACPK,MAAM,CAACa,IAAI,CAAC;UAAErE,IAAI,EAAEN,SAAS,CAAC2D,SAAS;UAAElC;QAAM,CAAC,CAAC;QACjD;MACF,KAAK,OAAO;QACVqC,MAAM,CAACa,IAAI,CAAC;UAAErE,IAAI;UAAEmB;QAAM,CAAC,CAAC;QAC5B;MACF,KAAK,MAAM;QACTsC,KAAK,CAACY,IAAI,CAAC;UAAExC,IAAI,EAAE7B,IAAI;UAAEsE,GAAG,EAAEnD;QAAM,CAAC,CAAC;QACtC;IACJ;EACF;EAEA,OAAO;IACLqC,MAAM;IACNC,KAAK;IACLG;EACF,CAAC;AACH,CAAC;AAED,OAAO,IAAMW,eAAe,GAAIC,IAA6C,IAAc;EACzF,IAAMC,QAAQ,GAAGD,IAAI,CAACE,MAAM,KAAK/E,MAAM,CAACS,MAAM;EAE9C,IAAIqE,QAAQ,IAAID,IAAI,CAACG,KAAK,CAAClE,MAAM,KAAK,CAAC,EAAE;IACvC,OAAOgE,QAAQ;EACjB;EAEA,OAAO,CAAC,CAACD,IAAI,CAACG,KAAK,CAACC,IAAI,CAAEC,IAAI,IAAKN,eAAe,CAACM,IAAI,CAAC,CAAC;AAC3D,CAAC;AAED,OAAO,IAAMC,eAAe,GAAIN,IAA6C,IAAc;EACzF,OAAOA,IAAI,CAACG,KAAK,CAACI,KAAK,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,IAAIH,eAAe,CAACE,GAAG,CAAC,CAAC;AACpE,CAAC;AAED,OAAO,IAAME,QAAQ,GAAGA,CAACC,UAAsB,EAAEC,SAA6B,KAAc;EAC1F,OAAO,CAAC,CAACD,UAAU,CAAC3B,MAAM,CAACoB,IAAI,CAAES,CAAC,IAAKA,CAAC,CAACrF,IAAI,KAAKoF,SAAS,CAAC;AAC9D,CAAC;AAED,OAAO,IAAME,cAAc,GAAIC,QAA0B,IAAK;EAC5D,OAAOA,QAAQ,CAACC,IAAI,CAAEvF,OAAO,IAAKA,OAAO,CAAC4B,IAAI,KAAK,YAAY,IAAI5B,OAAO,CAAC4B,IAAI,KAAK,WAAW,CAAC;AAClG,CAAC;AAED,OAAO,IAAM4D,oBAAoB,GAAIF,QAA0B,IAC7DA,QAAQ,CAACvB,MAAM,CAAC,CAACC,GAAG,EAAEhE,OAAO,KAAK;EAChC,IAAIA,OAAO,CAAC4B,IAAI,KAAK,YAAY,IAAI5B,OAAO,CAAC4B,IAAI,KAAK,WAAW,EAAE;IACjE,OAAOoC,GAAG;EACZ;EAEA,IAAIhE,OAAO,CAAC4B,IAAI,KAAK,YAAY,EAAE;IACjCoC,GAAG,CAACI,IAAI,CAAC,CAACpE,OAAO,CAAC,CAAC;IAEnB,OAAOgE,GAAG;EACZ;EAEA,IAAMyB,iBAAiB,GAAGzB,GAAG,CAAC0B,aAAa,CAAEd,IAAI,IAAKA,IAAI,CAACpE,MAAM,KAAK,CAAC,CAAC;EAExE,IAAIiF,iBAAiB,KAAK,CAAC,CAAC,EAAE;IAC5B,OAAOzB,GAAG;EACZ;EAEAA,GAAG,CAACyB,iBAAiB,CAAC,CAACrB,IAAI,CAACpE,OAAO,CAAC;EAEpC,OAAOgE,GAAG;AACZ,CAAC,EAAE,EAAwB,CAAC;AAE9B,OAAO,IAAM2B,0BAA0B,GAAIL,QAA0B,IAAK;EACxE,IAAMM,oBAAoB,GAAGJ,oBAAoB,CAACF,QAAQ,CAAC;EAE3D,OAAOM,oBAAoB,CAAC/B,MAAM,CAAEe,IAAI,IAAKA,IAAI,CAACpE,MAAM,KAAK,CAAC,CAAC;AACjE,CAAC;AAED,OAAO,IAAMqF,SAAS,GAAaC,GAAQ,IACzC,CAAC,CAACA,GAAG,KAAK,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IAAI,OAAOA,GAAG,CAACC,IAAI,KAAK,UAAU;AAEnG,OAAO,IAAM1E,SAAS,GAAG,SAAZA,SAASA,CAAIH,KAAU;EAAA,IAAE;IAAE8E,QAAQ,GAAG,CAAC;IAAEC,SAAS,GAAG,CAAC;IAAEC;EAA2B,CAAC,GAAA3F,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,OACpG4F,WAAW,CACT,OAAOjF,KAAK,KAAK,QAAQ,GAAGkF,IAAI,CAACC,SAAS,CAACnF,KAAK,EAAEoF,uBAAuB,CAACN,QAAQ,EAAEE,QAAQ,CAAC,CAAC,GAAGK,MAAM,CAACrF,KAAK,CAAC,EAC9G+E,SACF,CAAC;AAAA;AAEH,IAAMK,uBAAuB,GAAGA,CAACN,QAAgB,EAAEQ,mBAAiD,KAAK;EACvG,IAAMC,OAAkB,GAAG,EAAE;EAC7B,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAA4BC,CAAS,EAAEzF,KAAc,EAAE;IAC3E,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;MAC/C,OAAOA,KAAK;IACd;IAEA,OAAOuF,OAAO,CAACjG,MAAM,GAAG,CAAC,IAAI,CAACoG,MAAM,CAACC,EAAE,CAACJ,OAAO,CAACK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE;MAC7DL,OAAO,CAACM,GAAG,CAAC,CAAC;IACf;IAEA,IAAKf,QAAQ,IAAIS,OAAO,CAACjG,MAAM,IAAIwF,QAAQ,IAAKS,OAAO,CAACO,QAAQ,CAAC9F,KAAK,CAAC,EAAE;MACvE,OAAOT,SAAS;IAClB;IAEAgG,OAAO,CAACrC,IAAI,CAAClD,KAAK,CAAC;IAEnB,OAAOA,KAAK,YAAY+F,GAAG,GACvBC,0BAA0B,CAACT,OAAO,EAAEvF,KAAK,CAAC,GAC1CA,KAAK,YAAYiG,GAAG,GAClBC,0BAA0B,CAACX,OAAO,EAAEvF,KAAK,CAAC,GAC1CA,KAAK;EACb,CAAC;EACD,OAAOsF,mBAAmB,GAAGa,gBAAgB,CAACb,mBAAmB,EAAEE,gBAAgB,CAAC,GAAGA,gBAAgB;AACzG,CAAC;AAED,IAAMW,gBAAgB,GAAGA,CAACC,KAA6B,EAAEC,MAA8B,KACrF,UAAUC,CAAC,EAAEC,CAAC,EAAE;EACd,OAAOF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,CAAC,EAAEF,KAAK,CAACI,IAAI,CAAC,IAAI,EAAEF,CAAC,EAAEC,CAAC,CAAC,CAAC;AACrD,CAAC;AAEH,IAAMP,0BAA0B,GAAGA,CAACT,OAAc,EAAEkB,GAAkB,KAAK;EACzE,OAAOC,KAAK,CAACC,IAAI,CAACF,GAAG,CAAC,CACnB9D,MAAM,CAACiE,IAAA;IAAA,IAAC,CAACN,CAAC,CAAC,GAAAM,IAAA;IAAA,OAAK,CAACrB,OAAO,CAACO,QAAQ,CAACQ,CAAC,CAAC;EAAA,EAAC,CACrCG,GAAG,CAACI,KAAA;IAAA,IAAC,CAACP,CAAC,EAAEC,CAAC,CAAC,GAAAM,KAAA;IAAA,OAAK,CAACP,CAAC,EAAEf,OAAO,CAACO,QAAQ,CAACS,CAAC,CAAC,GAAGhH,SAAS,GAAGgH,CAAC,CAAC;EAAA,EAAC;AAC9D,CAAC;AAED,IAAML,0BAA0B,GAAGA,CAACX,OAAc,EAAEuB,GAAa,KAAK;EACpE,OAAOJ,KAAK,CAACC,IAAI,CAACG,GAAG,CAAC,CAACL,GAAG,CAAEF,CAAC,IAAMhB,OAAO,CAACO,QAAQ,CAACS,CAAC,CAAC,GAAGhH,SAAS,GAAGgH,CAAE,CAAC;AAC1E,CAAC;AAED,IAAMtB,WAAW,GAAGA,CAACjF,KAAa,EAAE+E,SAAiB,KACnDA,SAAS,IAAI/E,KAAK,CAACV,MAAM,GAAGyF,SAAS,MAAAzD,MAAA,CAAMtB,KAAK,CAAC+G,SAAS,CAAC,CAAC,EAAEhC,SAAS,CAAC,WAAQ/E,KAAK", "ignoreList": []}