import { Given, When, Then } from '@cucumber/cucumber';
import { expect } from '@playwright/test';
import { TestContext } from '../utils/TestContext';
import { LoginPage } from '../pages/login.page';
import { logger } from '../utils/logger';
import { injectAxe, checkA11y } from 'axe-playwright';

const testContext = TestContext.getInstance();

// Helper function to normalize Unicode text for comparison
function normalizeText(text: string): string {
  return text.normalize('NFC').trim();
}

// Helper function to get credentials from environment
function getValidCredentials() {
  return {
    email: process.env.VALID_EMAIL || '<EMAIL>',
    password: process.env.VALID_PASSWORD || 'vietnam5963'
  };
}

// Background step
Given('I am on the login page', async function () {
  logger.step('Navigating to login page');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  
  await loginPage.goto();
  await loginPage.isLoaded();
  
  // Store login page instance for reuse
  testContext.setTestData('loginPage', loginPage);
  
  logger.info('✅ Successfully on login page');
});

// Page load verification steps
Then('the login page should be loaded', async function () {
  logger.step('Verifying login page is loaded');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  const isLoaded = await loginPage.isLoaded();
  
  expect(isLoaded).toBe(true);
  logger.assertion('✅ Login page is properly loaded');
});

Then('the logo should be visible', async function () {
  logger.step('Verifying logo is visible');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  const isVisible = await loginPage.isElementVisible(loginPage.logo);
  
  if (isVisible) {
    await loginPage.verifyElementVisible(loginPage.logo);
    logger.assertion('✅ Logo is visible');
  } else {
    logger.warn('⚠️ Logo not found on page');
  }
});

Then('the page title should be visible', async function () {
  logger.step('Verifying page title is visible');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  const isVisible = await loginPage.isElementVisible(loginPage.pageTitle);
  
  if (isVisible) {
    await loginPage.verifyElementVisible(loginPage.pageTitle);
    logger.assertion('✅ Page title is visible');
  } else {
    logger.warn('⚠️ Page title not found');
  }
});

Then('the email input should be visible', async function () {
  logger.step('Verifying email input is visible');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  await loginPage.verifyElementVisible(loginPage.emailInput);
  
  logger.assertion('✅ Email input is visible');
});

Then('the password input should be visible', async function () {
  logger.step('Verifying password input is visible');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  await loginPage.verifyElementVisible(loginPage.passwordInput);
  
  logger.assertion('✅ Password input is visible');
});

Then('the submit button should be visible', async function () {
  logger.step('Verifying submit button is visible');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  await loginPage.verifyElementVisible(loginPage.submitButton);
  
  logger.assertion('✅ Submit button is visible');
});

// Login action steps
When('I enter valid email and password', async function () {
  logger.step('Entering valid credentials');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  const credentials = getValidCredentials();
  
  await loginPage.enterEmail(credentials.email);
  await loginPage.enterPassword(credentials.password);
  
  logger.action('Valid credentials entered');
});

When('I enter valid email', async function () {
  logger.step('Entering valid email');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  const credentials = getValidCredentials();
  
  await loginPage.enterEmail(credentials.email);
  
  logger.action('Valid email entered');
});

When('I leave password empty', async function () {
  logger.step('Leaving password field empty');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  await loginPage.enterPassword('');
  
  logger.action('Password field left empty');
});

When('I enter password {string}', async function (password: string) {
  logger.step(`Entering password: ${password}`);
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  await loginPage.enterPassword(password);
  
  logger.action('Password entered');
});

When('I enter email {string}', async function (email: string) {
  logger.step(`Entering email: ${email}`);
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  await loginPage.enterEmail(email);
  
  logger.action('Email entered');
});

When('I enter {string} and {string} and submit', async function (email: string, password: string) {
  logger.step(`Entering credentials and submitting: ${email}`);
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  
  // Handle environment variable substitution
  const actualEmail = email.startsWith('${') ? getValidCredentials().email : email;
  
  await loginPage.enterEmail(actualEmail);
  await loginPage.enterPassword(password);
  await loginPage.submit();
  
  logger.action('Credentials entered and form submitted');
});

When('I submit the login form', async function () {
  logger.step('Submitting login form');

  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  const page = testContext.getPage();

  // Click submit button
  await loginPage.submitButton.click();

  // Wait a moment for validation to trigger
  await page.waitForTimeout(1000);

  logger.action('Login form submitted');
});

// Success verification steps
Then('I should be redirected to dashboard or home', async function () {
  logger.step('Verifying successful login and dashboard access');

  const page = testContext.getPage();

  // Wait for success message or authenticated state
  try {
    // Look for success message
    const successMessage = page.locator('.MuiAlert-message');
    await successMessage.waitFor({ state: 'visible', timeout: 10000 });

    const messageText = await successMessage.textContent();
    if (messageText && messageText.includes('ログインに成功しました')) {
      logger.assertion('✅ Login success message found');
    }
  } catch (error) {
    logger.warn('Success message not found, checking for authenticated state');
  }

  // Check for authenticated indicators
  const userAvatar = page.locator('.MuiAvatar-root');
  await userAvatar.waitFor({ state: 'visible', timeout: 10000 });

  logger.assertion('✅ Successfully authenticated and on dashboard');
});

Then('I should see authenticated indicator', async function () {
  logger.step('Verifying authenticated state');

  const page = testContext.getPage();

  // Look for user avatar or greeting
  const userAvatar = page.locator('.MuiAvatar-root');
  const userGreeting = page.locator('text=こんにちは');

  const avatarVisible = await userAvatar.isVisible();
  const greetingVisible = await userGreeting.isVisible();

  expect(avatarVisible || greetingVisible).toBe(true);
  logger.assertion('✅ Authenticated indicator found');
});

// Error message verification steps
Then('I should see the error message {string}', async function (expectedMessage: string) {
  logger.step(`Verifying error message: ${expectedMessage}`);

  const page = testContext.getPage();

  // Wait for any error message to appear using the correct selector
  const errorLocator = page.locator('.MuiFormHelperText-root.Mui-error');
  await errorLocator.first().waitFor({ state: 'visible', timeout: 10000 });

  // Get all error messages and find the one that matches
  const errorElements = await errorLocator.all();
  let actualMessage = '';

  for (const element of errorElements) {
    const text = await element.textContent();
    if (text && text.trim()) {
      actualMessage = text.trim();
      break;
    }
  }

  const normalizedExpected = normalizeText(expectedMessage);
  const normalizedActual = normalizeText(actualMessage);

  logger.info(`Expected: "${normalizedExpected}"`);
  logger.info(`Actual: "${normalizedActual}"`);

  // Use exact string match for Japanese errors
  expect(normalizedActual).toBe(normalizedExpected);

  logger.assertion(`✅ Error message verified: ${normalizedActual}`);
});



// Logout steps
Given('I am logged in with valid credentials', async function () {
  logger.step('Logging in with valid credentials');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  const credentials = getValidCredentials();
  
  await loginPage.login(credentials.email, credentials.password);
  await loginPage.waitForLoginSuccess();
  
  logger.action('Successfully logged in');
});

When('I logout', async function () {
  logger.step('Logging out');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  await loginPage.logout();
  
  logger.action('Logout attempted');
});

Then('I should be redirected to login page', async function () {
  logger.step('Verifying redirect to login page');
  
  const page = testContext.getPage();
  await page.waitForURL(/login|^\/$/, { timeout: 10000 });
  
  logger.assertion('✅ Redirected to login page');
});

Then('I should not be authenticated', async function () {
  logger.step('Verifying not authenticated');
  
  const page = testContext.getPage();
  const loginPage = new LoginPage(page);
  const isLoaded = await loginPage.isLoaded();
  
  expect(isLoaded).toBe(true);
  logger.assertion('✅ Not authenticated - on login page');
});

// Visual regression steps
Then('the login page should match the visual baseline', async function () {
  logger.step('Performing visual regression test');
  
  const loginPage = testContext.getTestData('loginPage') as LoginPage;
  await loginPage.takeVisualSnapshot('login-page-baseline');
  
  logger.assertion('✅ Visual regression test completed');
});

// Accessibility steps
Then('the login page should pass accessibility checks', async function () {
  logger.step('Running accessibility checks');
  
  const page = testContext.getPage();
  
  // Inject axe-core
  await injectAxe(page);
  
  // Run accessibility checks on form elements
  await checkA11y(page, 'form, input, button', {
    detailedReport: true,
    detailedReportOptions: { html: true }
  });
  
  logger.assertion('✅ Accessibility checks passed');
});
