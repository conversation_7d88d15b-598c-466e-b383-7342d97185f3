import { defineConfig } from '@playwright/test';
import baseConfig from '../playwright.config';

export default defineConfig({
  ...baseConfig,
  use: {
    ...baseConfig.use,
    baseURL: process.env.STAGING_BASE_URL || 'https://staging.smoothcontact.com',
    headless: true,
  },
  workers: 2,
  retries: 1,
  reporter: [
    ['html', { outputFolder: 'reports/playwright-report-staging' }],
    ['json', { outputFile: 'reports/playwright-results-staging.json' }],
    ['allure-playwright', { outputFolder: 'reports/allure-results-staging' }],
  ],
});
