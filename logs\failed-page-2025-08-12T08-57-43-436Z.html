<!DOCTYPE html><html lang="ja"><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    
    <!-- HEAD -->
    <script type="module" crossorigin="" src="/assets/index-dd09192f.js"></script>
    <link rel="modulepreload" crossorigin="" href="/assets/lodash-c12cda70.js">
    <link rel="modulepreload" crossorigin="" href="/assets/vendor-56862f1e.js">
    <link rel="stylesheet" href="/assets/index-37c6705f.css">
  <style data-emotion="css-global" data-s=""></style><style data-emotion="css-global" data-s=""></style><style data-emotion="css" data-s=""></style><title>Admin: Dashboard</title><style id="joyride-beacon-animation">
        @keyframes joyride-beacon-inner {
          20% {
            opacity: 0.9;
          }
        
          90% {
            opacity: 0.7;
          }
        }
        
        @keyframes joyride-beacon-outer {
          0% {
            transform: scale(1);
          }
        
          45% {
            opacity: 0.7;
            transform: scale(0.75);
          }
        
          100% {
            opacity: 0.9;
            transform: scale(1);
          }
        }
      </style></head>
  <body>
    <div id="app"><main class="MuiBox-root css-0"><div class="MuiBox-root css-0"><header class="MuiPaper-root MuiPaper-elevation MuiPaper-elevation4 MuiAppBar-root MuiAppBar-colorPrimary MuiAppBar-positionStatic css-ctjtpw"><div class="MuiContainer-root css-10ur324"><div class="MuiToolbar-root MuiToolbar-regular css-12o98wt"><a href="/"><img src="/assets/logo-cc350a34.svg" alt="logo" style="height: 14px; width: 130px;"></a><div class="MuiBox-root css-1rw111m"><div class="MuiBox-root css-1guk29"></div></div><div class="MuiBox-root css-2uchni"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-btlflx" tabindex="0" type="button"><div class="MuiAvatar-root MuiAvatar-circular MuiAvatar-colorDefault css-18kz6bu">V</div><span class="MuiTouchRipple-root css-w0pj6f"></span></button></div></div></div></header></div><div class="MuiBox-root css-0"><div class="MuiContainer-root MuiContainer-maxWidthLg css-1qsxih2"><div class="MuiBox-root css-i83yke"><div class="MuiBox-root css-8atqhb"><div class="MuiBox-root css-759u60"><svg class="MuiSvgIcon-root MuiSvgIcon-colorPrimary MuiSvgIcon-fontSizeMedium css-jc3lyx" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="AddIcon"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"></path></svg><p class="MuiTypography-root MuiTypography-body1 css-1ochi38">新しいフォームを作成</p><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-1o4bpvd" tabindex="0" type="button"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-11fo197" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ExpandLessIcon"><path d="m12 8-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z"></path></svg></button></div><div class="MuiCollapse-root MuiCollapse-vertical MuiCollapse-entered css-c4sutr" style="min-height: 0px;"><div class="MuiCollapse-wrapper MuiCollapse-vertical css-hboir5"><div class="MuiCollapse-wrapperInner MuiCollapse-vertical css-8atqhb"><div class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-2csr8t"><div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-3 css-1ngswdq"><div><div class="step-create-form css-1enpp1m"><div class="css-1mtoqb3"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-11fo197" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="AddIcon" style="font-size: 34px;"><path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"></path></svg></div></div><div class="MuiStack-root css-bmn5cr"><p class="MuiTypography-root MuiTypography-body1 css-g141ba">空白のフォーム</p></div></div></div><div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-3 css-1ngswdq"><div><div class="step-create-from-template css-1wnhu3g"><div class="css-xhhw1s"><img src="/assets/simple_contact_form-b1e92f6b.png" alt="お問い合わせフォーム"></div></div><div class="MuiStack-root css-bmn5cr"><p class="MuiTypography-root MuiTypography-body1 css-g141ba">お問い合わせフォーム</p></div></div></div><div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-3 css-1ngswdq"><div><div class="step-create-from-template css-1wnhu3g"><div class="css-xhhw1s"><img src="/assets/quick_questionnaire_form-98b7aedc.png" alt="クイックアンケート"></div></div><div class="MuiStack-root css-bmn5cr"><p class="MuiTypography-root MuiTypography-body1 css-g141ba">クイックアンケート</p></div></div></div><div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-3 css-1ngswdq"><div><div class="step-create-from-template css-1wnhu3g"><div class="css-xhhw1s"><img src="/assets/application_form-88d87dd5.png" alt="申込フォーム"></div></div><div class="MuiStack-root css-bmn5cr"><p class="MuiTypography-root MuiTypography-body1 css-g141ba">申込フォーム</p></div></div></div><div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-3 css-1ngswdq"><div><div class="step-create-from-template css-1wnhu3g"><div class="css-xhhw1s"><img src="/assets/reservation_form-04fa6fdd.png" alt="ご予約受付フォーム"></div></div><div class="MuiStack-root css-bmn5cr"><p class="MuiTypography-root MuiTypography-body1 css-g141ba">ご予約受付フォーム</p></div></div></div></div></div></div></div></div></div><div class="MuiStack-root css-1rrerex"><h6 class="MuiTypography-root MuiTypography-h6 css-c94pqk">vietnamdev02さん、こんにちは！</h6><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 MuiCard-root css-18pw11e"><div class="MuiTableContainer-root css-kge0eu"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 css-wbhazl"><div class="MuiTableContainer-root css-kge0eu"><div class="MuiStack-root css-hwutf7"><div class="MuiStack-root css-1xhj18k"><button class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary css-mmhipl" tabindex="0" type="button"><p class="MuiTypography-root MuiTypography-body1 css-1w4666t">すべて（1）</p><span class="MuiTouchRipple-root css-w0pj6f"></span></button><button class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary css-mmhipl" tabindex="0" type="button"><p class="MuiTypography-root MuiTypography-body1 css-1whlp3l">公開中 (1）</p><span class="MuiTouchRipple-root css-w0pj6f"></span></button><button class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary css-mmhipl" tabindex="0" type="button"><p class="MuiTypography-root MuiTypography-body1 css-1whlp3l">公開予約 (0）</p><span class="MuiTouchRipple-root css-w0pj6f"></span></button><button class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary css-mmhipl" tabindex="0" type="button"><p class="MuiTypography-root MuiTypography-body1 css-1whlp3l">非公開 (0）</p><span class="MuiTouchRipple-root css-w0pj6f"></span></button><button class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary MuiButton-root MuiButton-text MuiButton-textSecondary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorSecondary css-mmhipl" tabindex="0" type="button"><p class="MuiTypography-root MuiTypography-body1 css-1whlp3l">公開終了（0）</p><span class="MuiTouchRipple-root css-w0pj6f"></span></button></div><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorSecondary MuiIconButton-sizeMedium css-oh9l0r" tabindex="0" type="button"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-11fo197" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="FilterListOffIcon"><path d="M10.83 8H21V6H8.83zm5 5H18v-2h-4.17zM14 16.83V18h-4v-2h3.17l-3-3H6v-2h2.17l-3-3H3V6h.17L1.39 4.22 2.8 2.81l18.38 18.38-1.41 1.41z"></path></svg><span class="MuiTouchRipple-root css-w0pj6f"></span></button></div><table class="MuiTable-root css-10eyhy5"><thead class="MuiTableHead-root css-1wbz3t9"><tr class="MuiTableRow-root MuiTableRow-head css-jjx4e0"><th class="MuiTableCell-root MuiTableCell-head MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1tyyfoh" aria-sort="descending" scope="col" width="80"><p class="MuiTypography-root MuiTypography-body1 css-1j3umhp">#</p></th><th class="MuiTableCell-root MuiTableCell-head MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1tyyfoh" aria-sort="descending" scope="col" width="auto"><span class="MuiButtonBase-root MuiTableSortLabel-root css-kbf45c" tabindex="0" role="button">フォーム名<svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiTableSortLabel-icon MuiTableSortLabel-iconDirectionAsc css-tzi8t" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ArrowDownwardIcon"><path d="M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"></path></svg></span></th><th class="MuiTableCell-root MuiTableCell-head MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1tyyfoh" aria-sort="descending" scope="col" width="130"><span class="MuiButtonBase-root MuiTableSortLabel-root css-kbf45c" tabindex="0" role="button">ステータス<svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiTableSortLabel-icon MuiTableSortLabel-iconDirectionAsc css-tzi8t" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ArrowDownwardIcon"><path d="M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"></path></svg></span></th><th class="MuiTableCell-root MuiTableCell-head MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1tyyfoh" aria-sort="descending" scope="col" width="130"><span class="MuiButtonBase-root MuiTableSortLabel-root css-kbf45c" tabindex="0" role="button">更新日<svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiTableSortLabel-icon MuiTableSortLabel-iconDirectionAsc css-tzi8t" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ArrowDownwardIcon"><path d="M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"></path></svg></span></th><th class="MuiTableCell-root MuiTableCell-head MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1tyyfoh" aria-sort="descending" scope="col" width="100"><span class="MuiButtonBase-root MuiTableSortLabel-root css-kbf45c" tabindex="0" role="button">回答数<svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiTableSortLabel-icon MuiTableSortLabel-iconDirectionAsc css-tzi8t" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ArrowDownwardIcon"><path d="M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"></path></svg></span></th><th class="MuiTableCell-root MuiTableCell-head MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1tyyfoh" aria-sort="descending" scope="col" width="150"><p class="MuiTypography-root MuiTypography-body1 css-1j3umhp"></p></th></tr></thead><tbody class="MuiTableBody-root css-1xnox0e"><tr class="MuiTableRow-root css-jjx4e0" role="checkbox" aria-checked="false" tabindex="-1"><td class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeMedium css-1h84o7o">355342</td><td class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeMedium css-1h84o7o"><div class="MuiStack-root css-18wrjt3"><svg class="MuiSvgIcon-root MuiSvgIcon-colorPrimary MuiSvgIcon-fontSizeMedium css-1whe33f" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="DynamicFormIcon" aria-label="標準モード"><path d="M17 20v-9h-2V4h7l-2 5h2zm-2-7v7H4c-1.1 0-2-.9-2-2v-3c0-1.1.9-2 2-2zm-8.75 2.75h-1.5v1.5h1.5zM13 4v7H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2zM6.25 6.75h-1.5v1.5h1.5z"></path></svg><a href="/form-builder/edit/mnuxbrpo1bv3tdowb6gg4vyd62rb535n" style="text-decoration: none; color: rgb(29, 161, 168);"><p class="MuiTypography-root MuiTypography-body1 css-1ypziyr">空白のフォーム</p></a></div></td><td class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeMedium css-1h84o7o"><div class="MuiButtonBase-root MuiChip-root MuiChip-filled MuiChip-sizeSmall MuiChip-colorSuccess MuiChip-clickable MuiChip-clickableColorSuccess MuiChip-filledSuccess step-handle-form-status css-z5u62r" tabindex="0" role="button"><span class="MuiChip-label MuiChip-labelSmall css-1pjtbja">公開中</span><span class="MuiTouchRipple-root css-w0pj6f"></span></div></td><td class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeMedium css-1h84o7o">2025/08/12 11:13</td><td class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeMedium css-1h84o7o"><p class="MuiTypography-root MuiTypography-body1 css-1j3umhp">1</p></td><td class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeMedium css-1h84o7o"><div class="MuiStack-root css-pgnm0j"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-rrlqo" tabindex="0" type="button" aria-label="編集"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit css-1cw4hi4" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="EditIcon"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"></path></svg><span class="MuiTouchRipple-root css-w0pj6f"></span></button><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-rrlqo" tabindex="0" type="button" aria-label="レポート"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit css-1cw4hi4" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="LeaderboardIcon"><path d="M7.5 21H2V9h5.5zm7.25-18h-5.5v18h5.5zM22 11h-5.5v10H22z"></path></svg><span class="MuiTouchRipple-root css-w0pj6f"></span></button><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-rrlqo" tabindex="0" type="button" aria-label="共有"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit css-1cw4hi4" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ShareIcon"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92"></path></svg><span class="MuiTouchRipple-root css-w0pj6f"></span></button><div class="MuiBox-root css-0"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium step-handle-form css-dg0ck3" tabindex="0" type="button" aria-haspopup="true"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit css-1cw4hi4" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ExpandMoreIcon"><path d="M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg><span class="MuiTouchRipple-root css-w0pj6f"></span></button></div></div></td></tr></tbody></table><div class="MuiTablePagination-root css-1loffit"><div class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular MuiTablePagination-toolbar css-8nphli"><div class="MuiTablePagination-spacer css-1mrwq1p"></div><p class="MuiTablePagination-selectLabel css-1ee19xd" id=":r8:">Rows per page:</p><div class="MuiInputBase-root MuiInputBase-colorPrimary MuiTablePagination-input css-1osfqj0"><div tabindex="0" role="combobox" aria-controls=":r9:" aria-expanded="false" aria-haspopup="listbox" aria-labelledby=":r8: :r7:" id=":r7:" class="MuiSelect-select MuiTablePagination-select MuiSelect-standard MuiInputBase-input css-wsg82s">5</div><input aria-invalid="false" aria-hidden="true" tabindex="-1" class="MuiSelect-nativeInput css-1k3x8v3" value="5" style=""><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiTablePagination-selectIcon MuiSelect-iconStandard css-8szhrm" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="ArrowDropDownIcon"><path d="M7 10l5 5 5-5z"></path></svg></div><p class="MuiTablePagination-displayedRows css-1ee19xd">1–1 of 1</p><div class="MuiTablePagination-actions"><button class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-sizeMedium css-rrlqo" tabindex="-1" type="button" disabled="" aria-label="Go to first page" title="Go to first page"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-11fo197" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="FirstPageIcon"><path d="M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z"></path></svg></button><button class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-colorInherit MuiIconButton-sizeMedium css-1h7y6t" tabindex="-1" type="button" disabled="" aria-label="Go to previous page" title="Go to previous page"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-11fo197" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="KeyboardArrowLeftIcon"><path d="M15.41 16.09l-4.58-4.59 4.58-4.59L14 5.5l-6 6 6 6z"></path></svg></button><button class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-colorInherit MuiIconButton-sizeMedium css-1h7y6t" tabindex="-1" type="button" disabled="" aria-label="Go to next page" title="Go to next page"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-11fo197" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="KeyboardArrowRightIcon"><path d="M8.59 16.34l4.58-4.59-4.58-4.59L10 5.75l6 6-6 6z"></path></svg></button><button class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-sizeMedium css-rrlqo" tabindex="-1" type="button" disabled="" aria-label="Go to last page" title="Go to last page"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-11fo197" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="LastPageIcon"><path d="M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z"></path></svg></button></div></div></div></div></div></div></div></div><div class="react-joyride"><div class="react-joyride__step"><span></span></div></div></div></div></main><div role="presentation" class="MuiSnackbar-root MuiSnackbar-anchorOriginTopRight css-3sz7wh"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation6 MuiAlert-root MuiAlert-colorSuccess MuiAlert-filledSuccess MuiAlert-filled css-tuh127" role="alert" direction="down" style="opacity: 1; transform: none; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);"><div class="MuiAlert-icon css-1l54tgj"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeInherit css-1cw4hi4" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="SuccessOutlinedIcon"><path d="M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"></path></svg></div><div class="MuiAlert-message css-1xsto0d">ログインに成功しました</div><div class="MuiAlert-action css-1mzcepu"><button class="MuiButtonBase-root MuiIconButton-root MuiIconButton-colorInherit MuiIconButton-sizeSmall css-18cscfz" tabindex="0" type="button" aria-label="Close" title="Close"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1f872uo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="CloseIcon"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"></path></svg><span class="MuiTouchRipple-root css-w0pj6f"></span></button></div></div></div></div>
    
    <script>
      const global = globalThis;
    </script>
    <!-- BODY -->
  

<div role="presentation" class="MuiPopover-root MuiMenu-root MuiModal-root MuiModal-hidden css-w9rhsv" aria-hidden="true"><div aria-hidden="true" class="MuiBackdrop-root MuiBackdrop-invisible MuiModal-backdrop css-esi9ax" style="opacity: 0; visibility: hidden;"></div><div tabindex="-1" data-testid="sentinelStart"></div><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation8 MuiPopover-paper MuiMenu-paper MuiMenu-paper css-wo9nal" tabindex="-1" style="opacity: 0; transform: scale(0.75, 0.5625); visibility: hidden;"><ul class="MuiList-root MuiList-padding MuiMenu-list css-r8u8y9" role="menu" tabindex="-1"><li class="MuiButtonBase-root MuiMenuItem-root MuiMenuItem-gutters MuiMenuItem-root MuiMenuItem-gutters css-1tp1pof" tabindex="0" role="menuitem"><div class="MuiStack-root css-j7qwjs"><h6 class="MuiTypography-root MuiTypography-h6 css-18y90j5">vietnamdev02</h6><p class="MuiTypography-root MuiTypography-body2 css-dxeh6m"><EMAIL></p></div><span class="MuiTouchRipple-root css-w0pj6f"></span></li><hr class="MuiDivider-root MuiDivider-fullWidth css-39bbo6"><li class="MuiButtonBase-root MuiMenuItem-root MuiMenuItem-gutters MuiMenuItem-root MuiMenuItem-gutters css-1tp1pof" tabindex="-1" role="menuitem"><div class="MuiListItemIcon-root css-1f8bwsm"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1f872uo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="DynamicFormIcon"><path d="M17 20v-9h-2V4h7l-2 5h2zm-2-7v7H4c-1.1 0-2-.9-2-2v-3c0-1.1.9-2 2-2zm-8.75 2.75h-1.5v1.5h1.5zM13 4v7H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2zM6.25 6.75h-1.5v1.5h1.5z"></path></svg></div>ダッシュボード<span class="MuiTouchRipple-root css-w0pj6f"></span></li><li class="MuiButtonBase-root MuiMenuItem-root MuiMenuItem-gutters MuiMenuItem-root MuiMenuItem-gutters css-1tp1pof" tabindex="-1" role="menuitem"><div class="MuiListItemIcon-root css-1f8bwsm"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1f872uo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="SettingsIcon"><path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.***********.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.***********.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6"></path></svg></div>設定<span class="MuiTouchRipple-root css-w0pj6f"></span></li><li class="MuiButtonBase-root MuiMenuItem-root MuiMenuItem-gutters MuiMenuItem-root MuiMenuItem-gutters css-1tp1pof" tabindex="-1" role="menuitem"><div class="MuiListItemIcon-root css-1f8bwsm"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeSmall css-1f872uo" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="LogoutIcon"><path d="m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z"></path></svg></div>ログアウト<span class="MuiTouchRipple-root css-w0pj6f"></span></li></ul></div><div tabindex="-1" data-testid="sentinelEnd"></div></div><div id="react-joyride-step-0" style="z-index: 1100;"><div class="__floater" x-placement="bottom" style="display: inline-block; filter: drop-shadow(rgba(0, 0, 0, 0.3) 0px 0px 3px); max-width: 100%; opacity: 0; position: absolute; transition: opacity 0.3s; visibility: hidden; z-index: 1100; padding: 16px 0px 0px; will-change: transform; top: 0px; left: 0px; transform: translate3d(23px, 280px, 0px);"><div class="__floater__body"><div aria-label="クリックして、新しいフォームを作成します" class="react-joyride__tooltip" aria-modal="true" role="alertdialog" style="background-color: rgb(255, 255, 255); border-radius: 5px; box-sizing: border-box; color: rgb(0, 74, 20); font-size: 16px; padding: 30px 10px 10px; position: relative; width: 300px;"><div style="line-height: 1.4; text-align: left;"><div style="padding: 15px 5px; font-size: 12px; color: rgb(39, 41, 55);">クリックして、新しいフォームを作成します</div></div><div style="align-items: center; display: flex; justify-content: flex-end; margin-top: 15px;"><div style="flex: 1 1 0%;"></div><button data-test-id="button-primary" type="button" aria-label="次へ" data-action="primary" role="button" title="次へ" style="background-color: rgb(39, 41, 55); border: 0px; border-radius: 4px; color: rgb(255, 255, 255); cursor: pointer; font-size: 13px; line-height: 1.5; padding: 4px 10px; appearance: none; font-weight: 500;">次へ</button></div><button type="button" data-test-id="button-close" aria-label="Close" data-action="close" role="button" title="Close" style="background-color: transparent; border: 0px; border-radius: 0px; cursor: pointer; font-size: 16px; line-height: 1; padding: 15px; appearance: none; position: absolute; right: 0px; top: 0px;"><svg height="12px" preserveAspectRatio="xMidYMid" version="1.1" viewBox="0 0 18 18" width="12px" xmlns="http://www.w3.org/2000/svg"><g><path d="M8.13911129,9.00268191 L0.171521827,17.0258467 C-0.0498027049,17.248715 -0.0498027049,17.6098394 0.171521827,17.8327545 C0.28204354,17.9443526 0.427188206,17.9998706 0.572051765,17.9998706 C0.71714958,17.9998706 0.862013139,17.9443526 0.972581703,17.8327545 L9.0000937,9.74924618 L17.0276057,17.8327545 C17.1384085,17.9443526 17.2832721,17.9998706 17.4281356,17.9998706 C17.5729992,17.9998706 17.718097,17.9443526 17.8286656,17.8327545 C18.0499901,17.6098862 18.0499901,17.2487618 17.8286656,17.0258467 L9.86135722,9.00268191 L17.8340066,0.973848225 C18.0553311,0.750979934 18.0553311,0.389855532 17.8340066,0.16694039 C17.6126821,-0.0556467968 17.254037,-0.0556467968 17.0329467,0.16694039 L9.00042166,8.25611765 L0.967006424,0.167268345 C0.745681892,-0.0553188426 0.387317931,-0.0553188426 0.165993399,0.167268345 C-0.0553311331,0.390136635 -0.0553311331,0.751261038 0.165993399,0.974176179 L8.13920499,9.00268191 L8.13911129,9.00268191 Z" fill="#004a14"></path></g></svg></button></div><div class="__floater__arrow" style="pointer-events: none; position: absolute; width: 100%; left: 0px; right: 0px; top: 0px; height: 16px;"><span style="display: inline-flex; position: absolute; margin-left: 8px; margin-right: 8px; left: 126px;"><svg width="32" height="16" version="1.1" xmlns="http://www.w3.org/2000/svg"><polygon points="32,16 16,0 0,16" fill="#fff"></polygon></svg></span></div></div></div><span x-placement="bottom" style="cursor: help; display: inline-flex; flex-direction: column; z-index: 1100; position: absolute; top: 0px; left: 0px; transform: translate3d(163px, 242px, 0px); will-change: transform;"><button class="react-joyride__beacon" data-test-id="button-beacon" type="button" aria-label="Open the dialog" title="Open the dialog" style="background-color: transparent; border: 0px; border-radius: 0px; color: rgba(0, 0, 0, 0.6); cursor: pointer; font-size: 16px; line-height: 1; padding: 8px; appearance: none; display: inline-block; height: 20px; position: relative; width: 20px; z-index: 1000;"><span style="animation: 1.2s ease-in-out 0s infinite normal none running joyride-beacon-inner; background-color: rgb(36, 203, 212); border-radius: 50%; display: block; height: 50%; left: 50%; opacity: 0.7; position: absolute; top: 50%; transform: translate(-50%, -50%); width: 50%;"></span><span style="animation: 1.2s ease-in-out 0s infinite normal none running joyride-beacon-outer; background-color: rgba(36, 203, 212, 0.2); border: 2px solid rgb(36, 203, 212); border-radius: 50%; box-sizing: border-box; display: block; height: 100%; left: 0px; opacity: 0.9; position: absolute; top: 0px; transform-origin: center center; width: 100%;"></span></button></span></div><div id="react-joyride-portal"></div></body></html>