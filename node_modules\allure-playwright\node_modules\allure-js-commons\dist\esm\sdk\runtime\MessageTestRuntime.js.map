{"version": 3, "file": "MessageTestRuntime.js", "names": ["Status", "getMessageAndTraceFromError", "getStatusFromError", "MessageTestRuntime", "label", "name", "value", "_this", "_asyncToGenerator", "sendMessage", "type", "data", "labels", "_arguments", "arguments", "_this2", "_len", "length", "Array", "_key", "link", "url", "_this3", "links", "_arguments2", "_this4", "_len2", "_key2", "parameter", "options", "_this5", "parameters", "_objectSpread", "description", "markdown", "_this6", "descriptionHtml", "html", "_this7", "displayName", "_this8", "historyId", "_this9", "testCaseId", "_this0", "attachment", "content", "_this1", "bufferContent", "<PERSON><PERSON><PERSON>", "from", "encoding", "toString", "contentType", "fileExtension", "wrapInStep", "timestamp", "Date", "now", "attachmentFromPath", "path", "_this10", "logStep", "_arguments3", "_this11", "status", "undefined", "PASSED", "error", "start", "stop", "statusDetails", "step", "body", "_this12", "result", "err", "details", "stepDis<PERSON><PERSON><PERSON>", "_this13", "stepParameter", "mode", "_this14"], "sources": ["../../../../src/sdk/runtime/MessageTestRuntime.ts"], "sourcesContent": ["import {\n  type AttachmentOptions,\n  type Label,\n  type LabelName,\n  type Link,\n  type LinkType,\n  type ParameterMode,\n  type ParameterOptions,\n  Status,\n} from \"../../model.js\";\nimport type { RuntimeMessage } from \"../types.js\";\nimport { getMessageAndTraceFromError, getStatusFromError } from \"../utils.js\";\nimport type { TestRuntime } from \"./types.js\";\n\nexport abstract class MessageTestRuntime implements TestRuntime {\n  async label(name: LabelName | string, value: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        labels: [{ name, value }],\n      },\n    });\n  }\n\n  async labels(...labels: Label[]) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        labels,\n      },\n    });\n  }\n\n  async link(url: string, type?: LinkType | string, name?: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        links: [{ type, url, name }],\n      },\n    });\n  }\n\n  async links(...links: Link[]) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        links,\n      },\n    });\n  }\n\n  async parameter(name: string, value: string, options?: ParameterOptions) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        parameters: [\n          {\n            name,\n            value,\n            ...options,\n          },\n        ],\n      },\n    });\n  }\n\n  async description(markdown: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        description: markdown,\n      },\n    });\n  }\n\n  async descriptionHtml(html: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        descriptionHtml: html,\n      },\n    });\n  }\n\n  async displayName(name: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        displayName: name,\n      },\n    });\n  }\n\n  async historyId(value: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        historyId: value,\n      },\n    });\n  }\n\n  async testCaseId(value: string) {\n    await this.sendMessage({\n      type: \"metadata\",\n      data: {\n        testCaseId: value,\n      },\n    });\n  }\n\n  async attachment(name: string, content: Buffer | string, options: AttachmentOptions) {\n    const bufferContent = typeof content === \"string\" ? Buffer.from(content, options.encoding) : content;\n    await this.sendMessage({\n      type: \"attachment_content\",\n      data: {\n        name,\n        content: bufferContent.toString(\"base64\"),\n        encoding: \"base64\",\n        contentType: options.contentType,\n        fileExtension: options.fileExtension,\n        wrapInStep: true,\n        timestamp: Date.now(),\n      },\n    });\n  }\n\n  async attachmentFromPath(name: string, path: string, options: AttachmentOptions) {\n    await this.sendMessage({\n      type: \"attachment_path\",\n      data: {\n        name,\n        path,\n        contentType: options.contentType,\n        fileExtension: options.fileExtension,\n        wrapInStep: true,\n        timestamp: Date.now(),\n      },\n    });\n  }\n\n  async logStep(name: string, status: Status = Status.PASSED, error?: Error) {\n    const timestamp = Date.now();\n    await this.sendMessage({\n      type: \"step_start\",\n      data: {\n        name,\n        start: timestamp,\n      },\n    });\n    await this.sendMessage({\n      type: \"step_stop\",\n      data: {\n        status: status,\n        stop: timestamp,\n        statusDetails: error\n          ? {\n              ...getMessageAndTraceFromError(error),\n            }\n          : undefined,\n      },\n    });\n  }\n\n  async step<T = void>(name: string, body: () => T | PromiseLike<T>) {\n    await this.sendMessage({\n      type: \"step_start\",\n      data: {\n        name,\n        start: Date.now(),\n      },\n    });\n\n    try {\n      const result = await body();\n\n      await this.sendMessage({\n        type: \"step_stop\",\n        data: {\n          status: Status.PASSED,\n          stop: Date.now(),\n        },\n      });\n\n      return result;\n    } catch (err) {\n      const details = getMessageAndTraceFromError(err as Error);\n\n      await this.sendMessage({\n        type: \"step_stop\",\n        data: {\n          status: getStatusFromError(err as Error),\n          stop: Date.now(),\n          statusDetails: {\n            ...details,\n          },\n        },\n      });\n\n      throw err;\n    }\n  }\n\n  async stepDisplayName(name: string) {\n    await this.sendMessage({\n      type: \"step_metadata\",\n      data: { name },\n    });\n  }\n\n  async stepParameter(name: string, value: string, mode?: ParameterMode) {\n    await this.sendMessage({\n      type: \"step_metadata\",\n      data: {\n        parameters: [{ name, value, mode }],\n      },\n    });\n  }\n\n  abstract sendMessage(message: RuntimeMessage): Promise<void>;\n}\n"], "mappings": ";;;;;;;AAAA,SAQEA,MAAM,QACD,gBAAgB;AAEvB,SAASC,2BAA2B,EAAEC,kBAAkB,QAAQ,aAAa;AAG7E,OAAO,MAAeC,kBAAkB,CAAwB;EACxDC,KAAKA,CAACC,IAAwB,EAAEC,KAAa,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnD,MAAMD,KAAI,CAACE,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJC,MAAM,EAAE,CAAC;YAAEP,IAAI;YAAEC;UAAM,CAAC;QAC1B;MACF,CAAC,CAAC;IAAC;EACL;EAEMM,MAAMA,CAAA,EAAqB;IAAA,IAAAC,UAAA,GAAAC,SAAA;MAAAC,MAAA;IAAA,OAAAP,iBAAA;MAAA,SAAAQ,IAAA,GAAAH,UAAA,CAAAI,MAAA,EAAjBL,MAAM,OAAAM,KAAA,CAAAF,IAAA,GAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;QAANP,MAAM,CAAAO,IAAA,IAAAN,UAAA,CAAAM,IAAA;MAAA;MACpB,MAAMJ,MAAI,CAACN,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJC;QACF;MACF,CAAC,CAAC;IAAC;EACL;EAEMQ,IAAIA,CAACC,GAAW,EAAEX,IAAwB,EAAEL,IAAa,EAAE;IAAA,IAAAiB,MAAA;IAAA,OAAAd,iBAAA;MAC/D,MAAMc,MAAI,CAACb,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJY,KAAK,EAAE,CAAC;YAAEb,IAAI;YAAEW,GAAG;YAAEhB;UAAK,CAAC;QAC7B;MACF,CAAC,CAAC;IAAC;EACL;EAEMkB,KAAKA,CAAA,EAAmB;IAAA,IAAAC,WAAA,GAAAV,SAAA;MAAAW,MAAA;IAAA,OAAAjB,iBAAA;MAAA,SAAAkB,KAAA,GAAAF,WAAA,CAAAP,MAAA,EAAfM,KAAK,OAAAL,KAAA,CAAAQ,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;QAALJ,KAAK,CAAAI,KAAA,IAAAH,WAAA,CAAAG,KAAA;MAAA;MAClB,MAAMF,MAAI,CAAChB,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJY;QACF;MACF,CAAC,CAAC;IAAC;EACL;EAEMK,SAASA,CAACvB,IAAY,EAAEC,KAAa,EAAEuB,OAA0B,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAtB,iBAAA;MACvE,MAAMsB,MAAI,CAACrB,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJoB,UAAU,EAAE,CAAAC,aAAA;YAER3B,IAAI;YACJC;UAAK,GACFuB,OAAO;QAGhB;MACF,CAAC,CAAC;IAAC;EACL;EAEMI,WAAWA,CAACC,QAAgB,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA3B,iBAAA;MAClC,MAAM2B,MAAI,CAAC1B,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJsB,WAAW,EAAEC;QACf;MACF,CAAC,CAAC;IAAC;EACL;EAEME,eAAeA,CAACC,IAAY,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA9B,iBAAA;MAClC,MAAM8B,MAAI,CAAC7B,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJyB,eAAe,EAAEC;QACnB;MACF,CAAC,CAAC;IAAC;EACL;EAEME,WAAWA,CAAClC,IAAY,EAAE;IAAA,IAAAmC,MAAA;IAAA,OAAAhC,iBAAA;MAC9B,MAAMgC,MAAI,CAAC/B,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJ4B,WAAW,EAAElC;QACf;MACF,CAAC,CAAC;IAAC;EACL;EAEMoC,SAASA,CAACnC,KAAa,EAAE;IAAA,IAAAoC,MAAA;IAAA,OAAAlC,iBAAA;MAC7B,MAAMkC,MAAI,CAACjC,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJ8B,SAAS,EAAEnC;QACb;MACF,CAAC,CAAC;IAAC;EACL;EAEMqC,UAAUA,CAACrC,KAAa,EAAE;IAAA,IAAAsC,MAAA;IAAA,OAAApC,iBAAA;MAC9B,MAAMoC,MAAI,CAACnC,WAAW,CAAC;QACrBC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAE;UACJgC,UAAU,EAAErC;QACd;MACF,CAAC,CAAC;IAAC;EACL;EAEMuC,UAAUA,CAACxC,IAAY,EAAEyC,OAAwB,EAAEjB,OAA0B,EAAE;IAAA,IAAAkB,MAAA;IAAA,OAAAvC,iBAAA;MACnF,IAAMwC,aAAa,GAAG,OAAOF,OAAO,KAAK,QAAQ,GAAGG,MAAM,CAACC,IAAI,CAACJ,OAAO,EAAEjB,OAAO,CAACsB,QAAQ,CAAC,GAAGL,OAAO;MACpG,MAAMC,MAAI,CAACtC,WAAW,CAAC;QACrBC,IAAI,EAAE,oBAAoB;QAC1BC,IAAI,EAAE;UACJN,IAAI;UACJyC,OAAO,EAAEE,aAAa,CAACI,QAAQ,CAAC,QAAQ,CAAC;UACzCD,QAAQ,EAAE,QAAQ;UAClBE,WAAW,EAAExB,OAAO,CAACwB,WAAW;UAChCC,aAAa,EAAEzB,OAAO,CAACyB,aAAa;UACpCC,UAAU,EAAE,IAAI;UAChBC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;QACtB;MACF,CAAC,CAAC;IAAC;EACL;EAEMC,kBAAkBA,CAACtD,IAAY,EAAEuD,IAAY,EAAE/B,OAA0B,EAAE;IAAA,IAAAgC,OAAA;IAAA,OAAArD,iBAAA;MAC/E,MAAMqD,OAAI,CAACpD,WAAW,CAAC;QACrBC,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE;UACJN,IAAI;UACJuD,IAAI;UACJP,WAAW,EAAExB,OAAO,CAACwB,WAAW;UAChCC,aAAa,EAAEzB,OAAO,CAACyB,aAAa;UACpCC,UAAU,EAAE,IAAI;UAChBC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;QACtB;MACF,CAAC,CAAC;IAAC;EACL;EAEMI,OAAOA,CAACzD,IAAY,EAAiD;IAAA,IAAA0D,WAAA,GAAAjD,SAAA;MAAAkD,OAAA;IAAA,OAAAxD,iBAAA;MAAA,IAA/CyD,MAAc,GAAAF,WAAA,CAAA9C,MAAA,QAAA8C,WAAA,QAAAG,SAAA,GAAAH,WAAA,MAAG/D,MAAM,CAACmE,MAAM;MAAA,IAAEC,KAAa,GAAAL,WAAA,CAAA9C,MAAA,OAAA8C,WAAA,MAAAG,SAAA;MACvE,IAAMV,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,MAAMM,OAAI,CAACvD,WAAW,CAAC;QACrBC,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE;UACJN,IAAI;UACJgE,KAAK,EAAEb;QACT;MACF,CAAC,CAAC;MACF,MAAMQ,OAAI,CAACvD,WAAW,CAAC;QACrBC,IAAI,EAAE,WAAW;QACjBC,IAAI,EAAE;UACJsD,MAAM,EAAEA,MAAM;UACdK,IAAI,EAAEd,SAAS;UACfe,aAAa,EAAEH,KAAK,GAAApC,aAAA,KAEX/B,2BAA2B,CAACmE,KAAK,CAAC,IAEvCF;QACN;MACF,CAAC,CAAC;IAAC;EACL;EAEMM,IAAIA,CAAWnE,IAAY,EAAEoE,IAA8B,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAAlE,iBAAA;MACjE,MAAMkE,OAAI,CAACjE,WAAW,CAAC;QACrBC,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE;UACJN,IAAI;UACJgE,KAAK,EAAEZ,IAAI,CAACC,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MAEF,IAAI;QACF,IAAMiB,MAAM,SAASF,IAAI,CAAC,CAAC;QAE3B,MAAMC,OAAI,CAACjE,WAAW,CAAC;UACrBC,IAAI,EAAE,WAAW;UACjBC,IAAI,EAAE;YACJsD,MAAM,EAAEjE,MAAM,CAACmE,MAAM;YACrBG,IAAI,EAAEb,IAAI,CAACC,GAAG,CAAC;UACjB;QACF,CAAC,CAAC;QAEF,OAAOiB,MAAM;MACf,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZ,IAAMC,OAAO,GAAG5E,2BAA2B,CAAC2E,GAAY,CAAC;QAEzD,MAAMF,OAAI,CAACjE,WAAW,CAAC;UACrBC,IAAI,EAAE,WAAW;UACjBC,IAAI,EAAE;YACJsD,MAAM,EAAE/D,kBAAkB,CAAC0E,GAAY,CAAC;YACxCN,IAAI,EAAEb,IAAI,CAACC,GAAG,CAAC,CAAC;YAChBa,aAAa,EAAAvC,aAAA,KACR6C,OAAO;UAEd;QACF,CAAC,CAAC;QAEF,MAAMD,GAAG;MACX;IAAC;EACH;EAEME,eAAeA,CAACzE,IAAY,EAAE;IAAA,IAAA0E,OAAA;IAAA,OAAAvE,iBAAA;MAClC,MAAMuE,OAAI,CAACtE,WAAW,CAAC;QACrBC,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE;UAAEN;QAAK;MACf,CAAC,CAAC;IAAC;EACL;EAEM2E,aAAaA,CAAC3E,IAAY,EAAEC,KAAa,EAAE2E,IAAoB,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAA1E,iBAAA;MACrE,MAAM0E,OAAI,CAACzE,WAAW,CAAC;QACrBC,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE;UACJoB,UAAU,EAAE,CAAC;YAAE1B,IAAI;YAAEC,KAAK;YAAE2E;UAAK,CAAC;QACpC;MACF,CAAC,CAAC;IAAC;EACL;AAGF", "ignoreList": []}