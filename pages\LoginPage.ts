import { Page, Locator } from '@playwright/test';
import { BasePage } from './BasePage';
import { logger } from '../utils/logger';

export class LoginPage extends BasePage {
  // Locators
  private readonly usernameInput: Locator;
  private readonly passwordInput: Locator;
  private readonly loginButton: Locator;
  private readonly forgotPasswordLink: Locator;
  private readonly errorMessage: Locator;
  private readonly successMessage: Locator;
  private readonly rememberMeCheckbox: Locator;
  private readonly signUpLink: Locator;

  constructor(page: Page) {
    super(page, '/login');
    
    // Initialize locators
    this.usernameInput = page.locator('[data-testid="username-input"]');
    this.passwordInput = page.locator('[data-testid="password-input"]');
    this.loginButton = page.locator('[data-testid="login-button"]');
    this.forgotPasswordLink = page.locator('[data-testid="forgot-password-link"]');
    this.errorMessage = page.locator('[data-testid="error-message"]');
    this.successMessage = page.locator('[data-testid="success-message"]');
    this.rememberMeCheckbox = page.locator('[data-testid="remember-me-checkbox"]');
    this.signUpLink = page.locator('[data-testid="signup-link"]');
  }

  /**
   * Perform login with username and password
   */
  async login(username: string, password: string, rememberMe: boolean = false): Promise<void> {
    logger.action(`Logging in with username: ${username}`);
    
    await this.fillInput(this.usernameInput, username);
    await this.fillInput(this.passwordInput, password);
    
    if (rememberMe) {
      await this.clickElement(this.rememberMeCheckbox);
      logger.info('✅ Remember me checkbox checked');
    }
    
    await this.clickElement(this.loginButton);
    logger.action('Login button clicked');
  }

  /**
   * Quick login method for valid credentials
   */
  async loginWithValidCredentials(username: string, password: string): Promise<void> {
    await this.login(username, password);
    await this.waitForSuccessfulLogin();
  }

  /**
   * Wait for successful login (redirect to dashboard)
   */
  async waitForSuccessfulLogin(): Promise<void> {
    await this.page.waitForURL('**/dashboard', { timeout: 10000 });
    logger.assertion('Successfully logged in and redirected to dashboard');
  }

  /**
   * Get error message text
   */
  async getErrorMessage(): Promise<string> {
    await this.waitForElement(this.errorMessage);
    const message = await this.getElementText(this.errorMessage);
    logger.info(`❌ Error message: ${message}`);
    return message;
  }

  /**
   * Get success message text
   */
  async getSuccessMessage(): Promise<string> {
    await this.waitForElement(this.successMessage);
    const message = await this.getElementText(this.successMessage);
    logger.info(`✅ Success message: ${message}`);
    return message;
  }

  /**
   * Click forgot password link
   */
  async clickForgotPassword(): Promise<void> {
    await this.clickElement(this.forgotPasswordLink);
    logger.action('Forgot password link clicked');
  }

  /**
   * Click sign up link
   */
  async clickSignUp(): Promise<void> {
    await this.clickElement(this.signUpLink);
    logger.action('Sign up link clicked');
  }

  /**
   * Check if login form is displayed
   */
  async isLoginFormDisplayed(): Promise<boolean> {
    const isUsernameVisible = await this.isElementVisible(this.usernameInput);
    const isPasswordVisible = await this.isElementVisible(this.passwordInput);
    const isLoginButtonVisible = await this.isElementVisible(this.loginButton);
    
    const isDisplayed = isUsernameVisible && isPasswordVisible && isLoginButtonVisible;
    logger.assertion(`Login form displayed: ${isDisplayed}`);
    
    return isDisplayed;
  }

  /**
   * Check if error message is displayed
   */
  async isErrorMessageDisplayed(): Promise<boolean> {
    const isDisplayed = await this.isElementVisible(this.errorMessage);
    logger.assertion(`Error message displayed: ${isDisplayed}`);
    return isDisplayed;
  }

  /**
   * Check if success message is displayed
   */
  async isSuccessMessageDisplayed(): Promise<boolean> {
    const isDisplayed = await this.isElementVisible(this.successMessage);
    logger.assertion(`Success message displayed: ${isDisplayed}`);
    return isDisplayed;
  }

  /**
   * Clear login form
   */
  async clearLoginForm(): Promise<void> {
    await this.fillInput(this.usernameInput, '', { clear: true });
    await this.fillInput(this.passwordInput, '', { clear: true });
    logger.action('Login form cleared');
  }

  /**
   * Verify login page elements
   */
  async verifyLoginPageElements(): Promise<void> {
    await this.verifyElementVisible(this.usernameInput);
    await this.verifyElementVisible(this.passwordInput);
    await this.verifyElementVisible(this.loginButton);
    await this.verifyElementVisible(this.forgotPasswordLink);
    await this.verifyElementVisible(this.signUpLink);
    logger.assertion('All login page elements verified');
  }

  /**
   * Get username input value
   */
  async getUsernameValue(): Promise<string> {
    const value = await this.usernameInput.inputValue();
    logger.info(`Username input value: ${value}`);
    return value;
  }

  /**
   * Check if login button is enabled
   */
  async isLoginButtonEnabled(): Promise<boolean> {
    const isEnabled = await this.isElementEnabled(this.loginButton);
    logger.assertion(`Login button enabled: ${isEnabled}`);
    return isEnabled;
  }

  /**
   * Check if remember me is checked
   */
  async isRememberMeChecked(): Promise<boolean> {
    const isChecked = await this.rememberMeCheckbox.isChecked();
    logger.assertion(`Remember me checked: ${isChecked}`);
    return isChecked;
  }
}
