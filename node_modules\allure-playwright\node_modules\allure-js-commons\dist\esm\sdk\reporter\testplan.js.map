{"version": 3, "file": "testplan.js", "names": ["readFileSync", "allureIdRegexp", "parseTestPlan", "test<PERSON><PERSON><PERSON><PERSON>", "process", "env", "ALLURE_TESTPLAN_PATH", "undefined", "file", "testPlan", "JSON", "parse", "tests", "length", "e", "console", "error", "concat", "includedInTestPlan", "subject", "id", "fullName", "tags", "effectiveId", "map", "tag", "_tag$match", "match", "groups", "find", "maybeId", "some", "test", "idMatched", "String", "selectorMatched", "selector", "addSkipLabel", "labels", "push", "name", "value", "addSkipLabelAsMeta", "hasSkipLabel", "_ref"], "sources": ["../../../../src/sdk/reporter/testplan.ts"], "sourcesContent": ["import { readFileSync } from \"node:fs\";\nimport type { Label } from \"../../model.js\";\nimport type { TestPlanV1 } from \"../types.js\";\nimport { allureIdRegexp } from \"../utils.js\";\n\nexport const parseTestPlan = (): TestPlanV1 | undefined => {\n  const testPlanPath = process.env.ALLURE_TESTPLAN_PATH;\n\n  if (!testPlanPath) {\n    return undefined;\n  }\n\n  try {\n    const file = readFileSync(testPlanPath, \"utf8\");\n    const testPlan = JSON.parse(file) as TestPlanV1;\n\n    // Execute all tests if test plan is empty\n    if ((testPlan.tests || []).length === 0) {\n      return undefined;\n    }\n\n    return testPlan;\n  } catch (e) {\n    // eslint-disable-next-line no-console\n    console.error(`could not parse test plan ${testPlanPath}`, e);\n    return undefined;\n  }\n};\n\nexport const includedInTestPlan = (\n  testPlan: TestPlanV1,\n  subject: { id?: string; fullName?: string; tags?: string[] },\n): boolean => {\n  const { id, fullName, tags = [] } = subject;\n  const effectiveId =\n    id ?? tags.map((tag) => tag?.match(allureIdRegexp)?.groups?.id).find((maybeId) => maybeId !== undefined);\n\n  return testPlan.tests.some((test) => {\n    const idMatched = effectiveId && test.id ? String(test.id) === effectiveId : false;\n    const selectorMatched = fullName && test.selector === fullName;\n\n    return idMatched || selectorMatched;\n  });\n};\n\nexport const addSkipLabel = (labels: Label[]) => {\n  labels.push({ name: \"ALLURE_TESTPLAN_SKIP\", value: \"true\" });\n};\n\nexport const addSkipLabelAsMeta = (name: string) => {\n  return `${name} @allure.label.ALLURE_TESTPLAN_SKIP:true`;\n};\n\nexport const hasSkipLabel = (labels: readonly Label[]) => labels.some(({ name }) => name === \"ALLURE_TESTPLAN_SKIP\");\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,SAAS;AAGtC,SAASC,cAAc,QAAQ,aAAa;AAE5C,OAAO,IAAMC,aAAa,GAAGA,CAAA,KAA8B;EACzD,IAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,oBAAoB;EAErD,IAAI,CAACH,YAAY,EAAE;IACjB,OAAOI,SAAS;EAClB;EAEA,IAAI;IACF,IAAMC,IAAI,GAAGR,YAAY,CAACG,YAAY,EAAE,MAAM,CAAC;IAC/C,IAAMM,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAe;;IAE/C;IACA,IAAI,CAACC,QAAQ,CAACG,KAAK,IAAI,EAAE,EAAEC,MAAM,KAAK,CAAC,EAAE;MACvC,OAAON,SAAS;IAClB;IAEA,OAAOE,QAAQ;EACjB,CAAC,CAAC,OAAOK,CAAC,EAAE;IACV;IACAC,OAAO,CAACC,KAAK,8BAAAC,MAAA,CAA8Bd,YAAY,GAAIW,CAAC,CAAC;IAC7D,OAAOP,SAAS;EAClB;AACF,CAAC;AAED,OAAO,IAAMW,kBAAkB,GAAGA,CAChCT,QAAoB,EACpBU,OAA4D,KAChD;EACZ,IAAM;IAAEC,EAAE;IAAEC,QAAQ;IAAEC,IAAI,GAAG;EAAG,CAAC,GAAGH,OAAO;EAC3C,IAAMI,WAAW,GACfH,EAAE,aAAFA,EAAE,cAAFA,EAAE,GAAIE,IAAI,CAACE,GAAG,CAAEC,GAAG;IAAA,IAAAC,UAAA;IAAA,OAAKD,GAAG,aAAHA,GAAG,gBAAAC,UAAA,GAAHD,GAAG,CAAEE,KAAK,CAAC1B,cAAc,CAAC,cAAAyB,UAAA,gBAAAA,UAAA,GAA1BA,UAAA,CAA4BE,MAAM,cAAAF,UAAA,uBAAlCA,UAAA,CAAoCN,EAAE;EAAA,EAAC,CAACS,IAAI,CAAEC,OAAO,IAAKA,OAAO,KAAKvB,SAAS,CAAC;EAE1G,OAAOE,QAAQ,CAACG,KAAK,CAACmB,IAAI,CAAEC,IAAI,IAAK;IACnC,IAAMC,SAAS,GAAGV,WAAW,IAAIS,IAAI,CAACZ,EAAE,GAAGc,MAAM,CAACF,IAAI,CAACZ,EAAE,CAAC,KAAKG,WAAW,GAAG,KAAK;IAClF,IAAMY,eAAe,GAAGd,QAAQ,IAAIW,IAAI,CAACI,QAAQ,KAAKf,QAAQ;IAE9D,OAAOY,SAAS,IAAIE,eAAe;EACrC,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,IAAME,YAAY,GAAIC,MAAe,IAAK;EAC/CA,MAAM,CAACC,IAAI,CAAC;IAAEC,IAAI,EAAE,sBAAsB;IAAEC,KAAK,EAAE;EAAO,CAAC,CAAC;AAC9D,CAAC;AAED,OAAO,IAAMC,kBAAkB,GAAIF,IAAY,IAAK;EAClD,UAAAvB,MAAA,CAAUuB,IAAI;AAChB,CAAC;AAED,OAAO,IAAMG,YAAY,GAAIL,MAAwB,IAAKA,MAAM,CAACP,IAAI,CAACa,IAAA;EAAA,IAAC;IAAEJ;EAAK,CAAC,GAAAI,IAAA;EAAA,OAAKJ,IAAI,KAAK,sBAAsB;AAAA,EAAC", "ignoreList": []}