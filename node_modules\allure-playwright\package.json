{"name": "allure-playwright", "version": "3.3.3", "description": "Allure Playwright integration", "keywords": ["allure", "playwright", "html", "test", "report", "reporter", "testing", "testops"], "homepage": "https://allurereport.org/", "repository": {"type": "git", "url": "https://github.com/allure-framework/allure-js.git", "directory": "packages/allure-playwright"}, "license": "Apache-2.0", "author": {"name": "Qameta Software", "email": "<EMAIL>", "url": "https://qameta.io/"}, "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/cjs/index.js", "default": "./dist/cjs/index.js"}, "./testplan": {"types": "./dist/types/testplan.d.ts", "require": "./dist/cjs/testplan.js", "default": "./dist/cjs/testplan.js"}, "./autoconfig": {"types": "./dist/types/autoconfig.d.ts", "require": "./dist/cjs/autoconfig.js", "default": "./dist/cjs/autoconfig.js"}}, "main": "./dist/cjs/index.js", "types": "./dist/types/index.d.ts", "files": ["dist"], "scripts": {"allure-report": "allure serve ./out/allure-results", "clean": "rimraf ./dist ./out", "compile": "run-p 'compile:*'", "compile:esm": "babel --config-file ./babel.esm.json ./src --out-dir ./dist/esm --extensions '.ts' --source-maps", "compile:cjs": "babel --config-file ./babel.cjs.json ./src --out-dir ./dist/cjs --extensions '.ts' --source-maps", "compile:types": "tsc", "compile:fixup": "node ./scripts/fixup.mjs", "generate-report": "allure generate ./out/allure-results -o ./out/allure-report --clean", "lint": "eslint ./src ./test --ext .ts", "lint:fix": "eslint ./src ./test --ext .ts --fix", "pretest": "run-p clean compile", "test": "vitest run"}, "dependencies": {"allure-js-commons": "3.3.3"}, "devDependencies": {"@babel/cli": "^7.28.0", "@babel/core": "^7.28.0", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.28.0", "@babel/preset-typescript": "^7.27.1", "@playwright/test": "^1.54.1", "@stylistic/eslint-plugin": "^2.6.1", "@types/babel__core": "^7.20.5", "@types/babel__preset-env": "^7.10.0", "@types/eslint": "^8.56.11", "@types/node": "^20.14.2", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "allure-commandline": "^2.29.0", "allure-vitest": "3.3.3", "eslint": "^8.57.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^50.0.0", "eslint-plugin-n": "^17.10.1", "eslint-plugin-no-null": "^1.0.2", "eslint-plugin-prefer-arrow": "^1.2.3", "glob": "^11.0.1", "npm-run-all2": "^8.0.0", "rimraf": "^6.0.0", "typescript": "^5.2.2", "vitest": "^3.0.5"}, "peerDependencies": {"@playwright/test": ">=1.36.0"}}