import { test, expect } from '@playwright/test';
import { LoginPage } from '../../pages/LoginPage';
import { DashboardPage } from '../../pages/DashboardPage';
import { TestDataManager } from '../../utils/TestDataManager';
import { logger } from '../../utils/logger';

test.describe('Sample E2E Tests', () => {
  let loginPage: LoginPage;
  let dashboardPage: DashboardPage;
  let testDataManager: TestDataManager;

  test.beforeEach(async ({ page }) => {
    loginPage = new LoginPage(page);
    dashboardPage = new DashboardPage(page);
    testDataManager = TestDataManager.getInstance();
    
    logger.info('🧪 Starting test setup');
  });

  test('should load login page successfully', async ({ page }) => {
    logger.scenario('Verify login page loads correctly');
    
    await loginPage.navigate();
    await expect(page).toHaveTitle(/Login/);
    
    const isFormDisplayed = await loginPage.isLoginFormDisplayed();
    expect(isFormDisplayed).toBe(true);
    
    logger.assertion('✅ Login page loaded successfully');
  });

  test('should perform successful login', async ({ page }) => {
    logger.scenario('Verify successful login flow');
    
    const userData = testDataManager.getUserData('user');
    
    await loginPage.navigate();
    await loginPage.loginWithValidCredentials(userData.username, userData.password);
    
    await expect(page).toHaveURL(/.*\/dashboard/);
    await dashboardPage.verifyDashboardLoaded();
    
    logger.assertion('✅ Login successful and dashboard loaded');
  });

  test('should handle invalid login', async ({ page }) => {
    logger.scenario('Verify invalid login handling');
    
    await loginPage.navigate();
    await loginPage.login('<EMAIL>', 'wrongpassword');
    
    const isErrorDisplayed = await loginPage.isErrorMessageDisplayed();
    expect(isErrorDisplayed).toBe(true);
    
    await expect(page).toHaveURL(/.*\/login/);
    
    logger.assertion('✅ Invalid login handled correctly');
  });

  test('should navigate between pages', async ({ page }) => {
    logger.scenario('Verify navigation between pages');
    
    const userData = testDataManager.getUserData('user');
    
    // Login
    await loginPage.navigate();
    await loginPage.loginWithValidCredentials(userData.username, userData.password);
    await dashboardPage.verifyDashboardLoaded();
    
    // Navigate to contacts
    await dashboardPage.navigateToContacts();
    await expect(page).toHaveURL(/.*\/contacts/);
    
    // Navigate to settings
    await page.goBack();
    await dashboardPage.navigateToSettings();
    await expect(page).toHaveURL(/.*\/settings/);
    
    logger.assertion('✅ Navigation between pages working correctly');
  });

  test('should logout successfully', async ({ page }) => {
    logger.scenario('Verify logout functionality');
    
    const userData = testDataManager.getUserData('user');
    
    // Login first
    await loginPage.navigate();
    await loginPage.loginWithValidCredentials(userData.username, userData.password);
    await dashboardPage.verifyDashboardLoaded();
    
    // Logout
    await dashboardPage.logout();
    await expect(page).toHaveURL(/.*\/login/);
    
    const isLoginFormDisplayed = await loginPage.isLoginFormDisplayed();
    expect(isLoginFormDisplayed).toBe(true);
    
    logger.assertion('✅ Logout successful');
  });

  test.afterEach(async ({ page }, testInfo) => {
    if (testInfo.status !== testInfo.expectedStatus) {
      logger.error(`❌ Test failed: ${testInfo.title}`);
      
      // Take screenshot on failure
      const screenshot = await page.screenshot({ fullPage: true });
      await testInfo.attach('screenshot', { body: screenshot, contentType: 'image/png' });
    } else {
      logger.info(`✅ Test passed: ${testInfo.title}`);
    }
  });
});
