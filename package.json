{"name": "smoothcontact-automation", "version": "1.0.0", "description": "Comprehensive Playwright + TypeScript automation testing framework with Cucumber and Allure reporting", "main": "index.js", "scripts": {"test": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --format @cucumber/pretty-formatter --format json:reports/cucumber-report.json", "test:headless": "cross-env HEADLESS=true npm run test", "test:tag": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --tags", "test:parallel": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --parallel 3", "test:dev": "cross-env NODE_ENV=development npm run test", "test:staging": "cross-env NODE_ENV=staging npm run test", "test:prod": "cross-env NODE_ENV=production npm run test", "test:login": "cucumber-js --require-module ts-node/register --require './step-definitions/**/*.ts' --tags '@login' features/login/*.feature", "report": "allure generate reports/allure-results --clean -o reports/allure-report && allure open reports/allure-report", "report:serve": "allure serve reports/allure-results", "report:allure": "allure generate reports/allure-results --clean -o reports/allure-report && allure open reports/allure-report", "report:html": "npx playwright show-report reports/playwright-report", "clean": "rimraf reports/allure-results reports/allure-report reports/cucumber-report.json logs/*.log", "lint": "eslint . --ext .ts,.js --fix", "format": "prettier --write \"**/*.{ts,js,json,md}\"", "type-check": "tsc --noEmit", "setup": "playwright install", "validate": "node scripts/validate-setup.js", "postinstall": "npm run setup"}, "keywords": ["playwright", "typescript", "cucumber", "automation", "testing", "e2e", "allure", "page-object-model"], "author": "SmoothContact Team", "license": "MIT", "devDependencies": {"@cucumber/cucumber": "^10.0.1", "@cucumber/pretty-formatter": "^1.0.1", "@playwright/test": "^1.40.0", "@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "allure-commandline": "^2.24.1", "allure-cucumberjs": "^2.5.0", "allure-playwright": "^3.3.3", "axe-playwright": "^2.1.0", "cross-env": "^7.0.3", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.3", "rimraf": "^5.0.5", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "dependencies": {"dotenv": "^16.3.1", "tsconfig-paths": "^4.2.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}